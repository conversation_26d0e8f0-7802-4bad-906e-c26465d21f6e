{"best_global_step": 500, "best_metric": 0.2512759566307068, "best_model_checkpoint": "./Qwen3-1.7B/checkpoint-500", "epoch": 3.0, "eval_steps": 500, "global_step": 750, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.04, "grad_norm": 1.696134090423584, "learning_rate": 1.8e-05, "loss": 3.3016, "step": 10}, {"epoch": 0.08, "grad_norm": 1.0984123945236206, "learning_rate": 3.8e-05, "loss": 2.9722, "step": 20}, {"epoch": 0.12, "grad_norm": 1.1645921468734741, "learning_rate": 5.8e-05, "loss": 2.5823, "step": 30}, {"epoch": 0.16, "grad_norm": 1.0759665966033936, "learning_rate": 7.800000000000001e-05, "loss": 2.0622, "step": 40}, {"epoch": 0.2, "grad_norm": 1.513805866241455, "learning_rate": 9.8e-05, "loss": 1.5855, "step": 50}, {"epoch": 0.24, "grad_norm": 1.3129925727844238, "learning_rate": 0.000118, "loss": 1.1037, "step": 60}, {"epoch": 0.28, "grad_norm": 1.1767939329147339, "learning_rate": 0.000138, "loss": 0.7624, "step": 70}, {"epoch": 0.32, "grad_norm": 0.9909018874168396, "learning_rate": 0.00015800000000000002, "loss": 0.6199, "step": 80}, {"epoch": 0.36, "grad_norm": 0.7598309516906738, "learning_rate": 0.00017800000000000002, "loss": 0.5543, "step": 90}, {"epoch": 0.4, "grad_norm": 0.9426883459091187, "learning_rate": 0.00019800000000000002, "loss": 0.4565, "step": 100}, {"epoch": 0.44, "grad_norm": 0.8274199366569519, "learning_rate": 0.00019723076923076923, "loss": 0.4056, "step": 110}, {"epoch": 0.48, "grad_norm": 0.6321233510971069, "learning_rate": 0.00019415384615384615, "loss": 0.3469, "step": 120}, {"epoch": 0.52, "grad_norm": 0.7143011689186096, "learning_rate": 0.0001910769230769231, "loss": 0.3302, "step": 130}, {"epoch": 0.56, "grad_norm": 0.6661898493766785, "learning_rate": 0.000188, "loss": 0.3095, "step": 140}, {"epoch": 0.6, "grad_norm": 0.5435110926628113, "learning_rate": 0.00018492307692307694, "loss": 0.3035, "step": 150}, {"epoch": 0.64, "grad_norm": 0.5121824741363525, "learning_rate": 0.00018184615384615385, "loss": 0.305, "step": 160}, {"epoch": 0.68, "grad_norm": 0.5734186172485352, "learning_rate": 0.00017876923076923077, "loss": 0.3335, "step": 170}, {"epoch": 0.72, "grad_norm": 0.4829121232032776, "learning_rate": 0.00017569230769230772, "loss": 0.3159, "step": 180}, {"epoch": 0.76, "grad_norm": 0.4829765856266022, "learning_rate": 0.0001726153846153846, "loss": 0.318, "step": 190}, {"epoch": 0.8, "grad_norm": 0.524296224117279, "learning_rate": 0.00016953846153846156, "loss": 0.2937, "step": 200}, {"epoch": 0.84, "grad_norm": 0.5521841645240784, "learning_rate": 0.00016646153846153848, "loss": 0.2936, "step": 210}, {"epoch": 0.88, "grad_norm": 0.6123150587081909, "learning_rate": 0.0001633846153846154, "loss": 0.3492, "step": 220}, {"epoch": 0.92, "grad_norm": 0.4896746277809143, "learning_rate": 0.00016030769230769232, "loss": 0.3283, "step": 230}, {"epoch": 0.96, "grad_norm": 0.5613753795623779, "learning_rate": 0.00015723076923076923, "loss": 0.3076, "step": 240}, {"epoch": 1.0, "grad_norm": 0.7507155537605286, "learning_rate": 0.00015415384615384615, "loss": 0.3089, "step": 250}, {"epoch": 1.04, "grad_norm": 0.6800742745399475, "learning_rate": 0.0001510769230769231, "loss": 0.2595, "step": 260}, {"epoch": 1.08, "grad_norm": 0.5076842308044434, "learning_rate": 0.000148, "loss": 0.2783, "step": 270}, {"epoch": 1.12, "grad_norm": 1.24828040599823, "learning_rate": 0.00014492307692307694, "loss": 0.301, "step": 280}, {"epoch": 1.16, "grad_norm": 0.5825150609016418, "learning_rate": 0.00014184615384615386, "loss": 0.3242, "step": 290}, {"epoch": 1.2, "grad_norm": 0.5212364196777344, "learning_rate": 0.00013876923076923078, "loss": 0.2917, "step": 300}, {"epoch": 1.24, "grad_norm": 0.32301023602485657, "learning_rate": 0.0001356923076923077, "loss": 0.2763, "step": 310}, {"epoch": 1.28, "grad_norm": 0.4160694181919098, "learning_rate": 0.00013261538461538464, "loss": 0.228, "step": 320}, {"epoch": 1.32, "grad_norm": 0.3393399119377136, "learning_rate": 0.00012953846153846153, "loss": 0.2738, "step": 330}, {"epoch": 1.3599999999999999, "grad_norm": 0.4258902966976166, "learning_rate": 0.00012646153846153848, "loss": 0.2506, "step": 340}, {"epoch": 1.4, "grad_norm": 0.6643441915512085, "learning_rate": 0.0001233846153846154, "loss": 0.2854, "step": 350}, {"epoch": 1.44, "grad_norm": 0.3857574164867401, "learning_rate": 0.00012030769230769232, "loss": 0.2492, "step": 360}, {"epoch": 1.48, "grad_norm": 0.40876781940460205, "learning_rate": 0.00011723076923076924, "loss": 0.2677, "step": 370}, {"epoch": 1.52, "grad_norm": 0.32005566358566284, "learning_rate": 0.00011415384615384617, "loss": 0.2423, "step": 380}, {"epoch": 1.56, "grad_norm": 0.41837185621261597, "learning_rate": 0.00011107692307692308, "loss": 0.2506, "step": 390}, {"epoch": 1.6, "grad_norm": 0.3786408007144928, "learning_rate": 0.00010800000000000001, "loss": 0.262, "step": 400}, {"epoch": 1.6400000000000001, "grad_norm": 0.4702136814594269, "learning_rate": 0.00010492307692307693, "loss": 0.2625, "step": 410}, {"epoch": 1.6800000000000002, "grad_norm": 0.5263090133666992, "learning_rate": 0.00010184615384615386, "loss": 0.2383, "step": 420}, {"epoch": 1.72, "grad_norm": 0.43857336044311523, "learning_rate": 9.876923076923077e-05, "loss": 0.2599, "step": 430}, {"epoch": 1.76, "grad_norm": 0.5053232312202454, "learning_rate": 9.569230769230769e-05, "loss": 0.2723, "step": 440}, {"epoch": 1.8, "grad_norm": 0.4562166631221771, "learning_rate": 9.261538461538462e-05, "loss": 0.2499, "step": 450}, {"epoch": 1.8399999999999999, "grad_norm": 0.46363645792007446, "learning_rate": 8.953846153846154e-05, "loss": 0.2114, "step": 460}, {"epoch": 1.88, "grad_norm": 0.47847887873649597, "learning_rate": 8.646153846153846e-05, "loss": 0.2638, "step": 470}, {"epoch": 1.92, "grad_norm": 0.39058494567871094, "learning_rate": 8.338461538461538e-05, "loss": 0.2238, "step": 480}, {"epoch": 1.96, "grad_norm": 0.36952459812164307, "learning_rate": 8.030769230769231e-05, "loss": 0.2384, "step": 490}, {"epoch": 2.0, "grad_norm": 0.511933445930481, "learning_rate": 7.723076923076924e-05, "loss": 0.2476, "step": 500}, {"epoch": 2.0, "eval_loss": 0.2512759566307068, "eval_runtime": 23.4832, "eval_samples_per_second": 42.584, "eval_steps_per_second": 10.646, "step": 500}, {"epoch": 2.04, "grad_norm": 0.5819000005722046, "learning_rate": 7.415384615384616e-05, "loss": 0.2292, "step": 510}, {"epoch": 2.08, "grad_norm": 0.43056565523147583, "learning_rate": 7.107692307692308e-05, "loss": 0.2359, "step": 520}, {"epoch": 2.12, "grad_norm": 0.4221995770931244, "learning_rate": 6.800000000000001e-05, "loss": 0.2462, "step": 530}, {"epoch": 2.16, "grad_norm": 0.3195938467979431, "learning_rate": 6.492307692307693e-05, "loss": 0.2375, "step": 540}, {"epoch": 2.2, "grad_norm": 0.45819804072380066, "learning_rate": 6.184615384615385e-05, "loss": 0.2442, "step": 550}, {"epoch": 2.24, "grad_norm": 0.46563586592674255, "learning_rate": 5.876923076923078e-05, "loss": 0.2298, "step": 560}, {"epoch": 2.2800000000000002, "grad_norm": 0.4572595953941345, "learning_rate": 5.5692307692307696e-05, "loss": 0.2225, "step": 570}, {"epoch": 2.32, "grad_norm": 0.4165525734424591, "learning_rate": 5.261538461538462e-05, "loss": 0.2332, "step": 580}, {"epoch": 2.36, "grad_norm": 0.3158119320869446, "learning_rate": 4.953846153846154e-05, "loss": 0.2155, "step": 590}, {"epoch": 2.4, "grad_norm": 0.39805033802986145, "learning_rate": 4.646153846153846e-05, "loss": 0.2253, "step": 600}, {"epoch": 2.44, "grad_norm": 0.44842642545700073, "learning_rate": 4.338461538461539e-05, "loss": 0.2432, "step": 610}, {"epoch": 2.48, "grad_norm": 0.4955955445766449, "learning_rate": 4.0307692307692306e-05, "loss": 0.2298, "step": 620}, {"epoch": 2.52, "grad_norm": 0.5176429152488708, "learning_rate": 3.723076923076923e-05, "loss": 0.2294, "step": 630}, {"epoch": 2.56, "grad_norm": 0.3769938051700592, "learning_rate": 3.415384615384615e-05, "loss": 0.2394, "step": 640}, {"epoch": 2.6, "grad_norm": 0.452240914106369, "learning_rate": 3.107692307692308e-05, "loss": 0.2319, "step": 650}, {"epoch": 2.64, "grad_norm": 0.42668139934539795, "learning_rate": 2.8000000000000003e-05, "loss": 0.2172, "step": 660}, {"epoch": 2.68, "grad_norm": 0.46046578884124756, "learning_rate": 2.4923076923076926e-05, "loss": 0.2313, "step": 670}, {"epoch": 2.7199999999999998, "grad_norm": 0.41307532787323, "learning_rate": 2.1846153846153848e-05, "loss": 0.2462, "step": 680}, {"epoch": 2.76, "grad_norm": 0.5569246411323547, "learning_rate": 1.876923076923077e-05, "loss": 0.2273, "step": 690}, {"epoch": 2.8, "grad_norm": 0.4237045347690582, "learning_rate": 1.5692307692307693e-05, "loss": 0.23, "step": 700}, {"epoch": 2.84, "grad_norm": 0.35510170459747314, "learning_rate": 1.2615384615384616e-05, "loss": 0.2278, "step": 710}, {"epoch": 2.88, "grad_norm": 0.4337610602378845, "learning_rate": 9.538461538461538e-06, "loss": 0.2183, "step": 720}, {"epoch": 2.92, "grad_norm": 0.5267841815948486, "learning_rate": 6.461538461538462e-06, "loss": 0.2335, "step": 730}, {"epoch": 2.96, "grad_norm": 0.42636534571647644, "learning_rate": 3.3846153846153848e-06, "loss": 0.2375, "step": 740}, {"epoch": 3.0, "grad_norm": 0.42224591970443726, "learning_rate": 3.076923076923077e-07, "loss": 0.2188, "step": 750}], "logging_steps": 10, "max_steps": 750, "num_input_tokens_seen": 0, "num_train_epochs": 3, "save_steps": 500, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": true}, "attributes": {}}}, "total_flos": 1.9395569745248256e+16, "train_batch_size": 4, "trial_name": null, "trial_params": null}