## Thank you for your contribution

We sincerely appreciate your interest in contributing to this project. Our goal is to maintain a high-quality, stable, and secure codebase. Your cooperation in adhering to these guidelines helps us achieve that.

---

### Important Notes on the Review Process

Reviewing and validating code takes a significant amount of time and resources. To mitigate security risks and prevent regressions, all changes undergo a rigorous review process. Please understand the following:

* **Keep Pull Requests Small and Focused:** Each pull request should address a single, specific issue or feature. Avoid bundling multiple bug fixes, new features, or unrelated refactoring into a single PR. This makes the review process faster and more effective.
* **Not All Contributions Will Be Merged:** We cannot guarantee that every pull request will be merged. The decision to merge is based on various factors, including code quality, adherence to project style, performance implications, and alignment with the project's long-term vision.

---

### Before Submitting Your Pull Request

To ensure your contribution has the best chance of being accepted, please check the following:

* **New Features:** New feature requests are not accepted directly via pull requests. To propose a new feature, you must first create an **Issue** to discuss the idea with the project maintainers and community. This allows us to align on the design and necessity of the feature before any code is written.
* **Bug Fixes:** For bug fixes, please open a corresponding **Issue** first. Describe the bug, provide steps to reproduce it, and explain the expected behavior. This helps us confirm the bug and track its resolution. Once a bug is confirmed, you may submit a pull request referencing the issue.
* **Testing:** All code changes, especially bug fixes and new features, must be accompanied by appropriate unit tests. If your change is not covered by existing tests, please include new tests to demonstrate that your code works correctly and that the issue is resolved.
* **Code Style:** Please ensure your code follows the [Google C++ coding style](https://google.github.io/styleguide/cppguide.html) guidelines.

### Pull Request Details

* **Describe the change:** Clearly and concisely describe the purpose of your pull request. Explain what it does, why it's needed, and how it was implemented.
* **Link to a related Issue:** If this pull request resolves or is related to a specific issue, please link it here using the `Fixes #` or `Closes #` syntax (e.g., `Fixes #123`).
* **Testing Information:** Detail the steps you took to test your changes. Include any relevant test cases, commands, or scenarios you used.
