# To get started with Dependabot version updates, you'll need to specify which
# package ecosystems to update and where the package manifests are located.
# Please see the documentation for all configuration options:
# https://docs.github.com/github/administering-a-repository/configuration-options-for-dependency-updates

version: 2
updates:
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "monthly"
    groups:
      github-actions:
        patterns:
          - "*"
  - package-ecosystem: "pip"
    directory: "/.github/workflows/requirements"
    schedule:
      interval: "monthly"
    groups:
      build-time-deps:
        patterns:
          - "*"
