name: CI for general build

on:
  push:
    branches: [ master ]
    tags:
      - 'v*'
  pull_request:
    branches: [ master ]
  release:
    types: [ created ]

permissions:
  contents: read

jobs:
  build:
    strategy:
      matrix:
        os: [ ubuntu-latest, windows-latest, macos-latest ]
        arch: [ x64 ]
        include:
          - os: windows-latest
            arch: x86
    runs-on: ${{ matrix.os }}

    permissions:
      contents: write # svenstaro/upload-release-action 

    steps:
    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
    - uses: actions/setup-python@a26af69be951a213d495a4c3e4e4022e16d87065 # v5.6.0
      with:
        python-version: '3.x'
        architecture: ${{matrix.arch}}

    - name: Config for Windows
      if: runner.os == 'Windows'
      run: |
        if ("${{matrix.arch}}" -eq "x64") {
          $msbuildPlatform = "x64"
        } else {
          $msbuildPlatform = "Win32"
        }
        cmake -A $msbuildPlatform -B ${{github.workspace}}/build -DSPM_BUILD_TEST=ON -DSPM_ENABLE_SHARED=OFF -DCMAKE_INSTALL_PREFIX=${{github.workspace}}/build/root

    - name: Config for Unix
      if: runner.os != 'Windows'
      run: cmake -B ${{github.workspace}}/build -DSPM_BUILD_TEST=ON -DCMAKE_INSTALL_PREFIX=${{github.workspace}}/build/root
      env:
        CMAKE_OSX_ARCHITECTURES: arm64;x86_64

    - name: Build
      run: cmake --build ${{github.workspace}}/build --config Release --target install --parallel 8

    - name: Test
      working-directory: ${{github.workspace}}/build
      run: ctest -C Release --output-on-failure

    - name: Package
      working-directory: ${{github.workspace}}/build
      run: cpack

    - name: Build Python wrapper
      working-directory: ${{github.workspace}}/python
      run: |
        python -m pip install --require-hashes --no-dependencies -r ../.github/workflows/requirements/base.txt
        python -m pip install pytest
        python -m pip install -v .
        python -m pytest

    - name: Upload artifacts
      uses: actions/upload-artifact@v4
      with:
        name: artifacts-${{ matrix.os }}-${{ matrix.arch }}
        path: ./build/*.7z
        overwrite: true

    - name: Upload Release Assets
      if: startsWith(github.ref, 'refs/tags/')
      uses: svenstaro/upload-release-action@81c65b7cd4de9b2570615ce3aad67a41de5b1a13 # v2.11.2
      with:
        repo_token: ${{ secrets.GITHUB_TOKEN }}
        file: ./build/*.7z
        tag: ${{ github.ref }}
        overwrite: true
        prerelease: true
        file_glob: true
        body: "This is my release text"
