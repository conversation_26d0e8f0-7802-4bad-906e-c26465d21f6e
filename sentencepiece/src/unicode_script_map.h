// Copyright 2016 Google Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.!

#ifndef UNICODE_SCRIPT_DATA_H_
#define UNICODE_SCRIPT_DATA_H_
#include "third_party/absl/container/flat_hash_map.h"
namespace sentencepiece {
namespace unicode_script {
namespace {
void InitTable(absl::flat_hash_map<char32, ScriptType> *smap) {
  for (char32 c = 0x0000; c <= 0x001F; ++c) (*smap)[c] = U_Common;
  (*smap)[0x0020] = U_Common;
  for (char32 c = 0x0021; c <= 0x0023; ++c) (*smap)[c] = U_Common;
  (*smap)[0x0024] = U_Common;
  for (char32 c = 0x0025; c <= 0x0027; ++c) (*smap)[c] = U_Common;
  (*smap)[0x0028] = U_Common;
  (*smap)[0x0029] = U_Common;
  (*smap)[0x002A] = U_Common;
  (*smap)[0x002B] = U_Common;
  (*smap)[0x002C] = U_Common;
  (*smap)[0x002D] = U_Common;
  for (char32 c = 0x002E; c <= 0x002F; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x0030; c <= 0x0039; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x003A; c <= 0x003B; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x003C; c <= 0x003E; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x003F; c <= 0x0040; ++c) (*smap)[c] = U_Common;
  (*smap)[0x005B] = U_Common;
  (*smap)[0x005C] = U_Common;
  (*smap)[0x005D] = U_Common;
  (*smap)[0x005E] = U_Common;
  (*smap)[0x005F] = U_Common;
  (*smap)[0x0060] = U_Common;
  (*smap)[0x007B] = U_Common;
  (*smap)[0x007C] = U_Common;
  (*smap)[0x007D] = U_Common;
  (*smap)[0x007E] = U_Common;
  for (char32 c = 0x007F; c <= 0x009F; ++c) (*smap)[c] = U_Common;
  (*smap)[0x00A0] = U_Common;
  (*smap)[0x00A1] = U_Common;
  for (char32 c = 0x00A2; c <= 0x00A5; ++c) (*smap)[c] = U_Common;
  (*smap)[0x00A6] = U_Common;
  (*smap)[0x00A7] = U_Common;
  (*smap)[0x00A8] = U_Common;
  (*smap)[0x00A9] = U_Common;
  (*smap)[0x00AB] = U_Common;
  (*smap)[0x00AC] = U_Common;
  (*smap)[0x00AD] = U_Common;
  (*smap)[0x00AE] = U_Common;
  (*smap)[0x00AF] = U_Common;
  (*smap)[0x00B0] = U_Common;
  (*smap)[0x00B1] = U_Common;
  for (char32 c = 0x00B2; c <= 0x00B3; ++c) (*smap)[c] = U_Common;
  (*smap)[0x00B4] = U_Common;
  (*smap)[0x00B5] = U_Common;
  for (char32 c = 0x00B6; c <= 0x00B7; ++c) (*smap)[c] = U_Common;
  (*smap)[0x00B8] = U_Common;
  (*smap)[0x00B9] = U_Common;
  (*smap)[0x00BB] = U_Common;
  for (char32 c = 0x00BC; c <= 0x00BE; ++c) (*smap)[c] = U_Common;
  (*smap)[0x00BF] = U_Common;
  (*smap)[0x00D7] = U_Common;
  (*smap)[0x00F7] = U_Common;
  for (char32 c = 0x02B9; c <= 0x02C1; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x02C2; c <= 0x02C5; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x02C6; c <= 0x02D1; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x02D2; c <= 0x02DF; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x02E5; c <= 0x02E9; ++c) (*smap)[c] = U_Common;
  (*smap)[0x02EC] = U_Common;
  (*smap)[0x02ED] = U_Common;
  (*smap)[0x02EE] = U_Common;
  for (char32 c = 0x02EF; c <= 0x02FF; ++c) (*smap)[c] = U_Common;
  (*smap)[0x0374] = U_Common;
  (*smap)[0x037E] = U_Common;
  (*smap)[0x0385] = U_Common;
  (*smap)[0x0387] = U_Common;
  (*smap)[0x0589] = U_Common;
  (*smap)[0x0605] = U_Common;
  (*smap)[0x060C] = U_Common;
  (*smap)[0x061B] = U_Common;
  (*smap)[0x061C] = U_Common;
  (*smap)[0x061F] = U_Common;
  (*smap)[0x0640] = U_Common;
  (*smap)[0x06DD] = U_Common;
  (*smap)[0x08E2] = U_Common;
  for (char32 c = 0x0964; c <= 0x0965; ++c) (*smap)[c] = U_Common;
  (*smap)[0x0E3F] = U_Common;
  for (char32 c = 0x0FD5; c <= 0x0FD8; ++c) (*smap)[c] = U_Common;
  (*smap)[0x10FB] = U_Common;
  for (char32 c = 0x16EB; c <= 0x16ED; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1735; c <= 0x1736; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1802; c <= 0x1803; ++c) (*smap)[c] = U_Common;
  (*smap)[0x1805] = U_Common;
  (*smap)[0x1CD3] = U_Common;
  (*smap)[0x1CE1] = U_Common;
  for (char32 c = 0x1CE9; c <= 0x1CEC; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1CEE; c <= 0x1CF1; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1CF2; c <= 0x1CF3; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1CF5; c <= 0x1CF6; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x2000; c <= 0x200A; ++c) (*smap)[c] = U_Common;
  (*smap)[0x200B] = U_Common;
  for (char32 c = 0x200E; c <= 0x200F; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x2010; c <= 0x2015; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x2016; c <= 0x2017; ++c) (*smap)[c] = U_Common;
  (*smap)[0x2018] = U_Common;
  (*smap)[0x2019] = U_Common;
  (*smap)[0x201A] = U_Common;
  for (char32 c = 0x201B; c <= 0x201C; ++c) (*smap)[c] = U_Common;
  (*smap)[0x201D] = U_Common;
  (*smap)[0x201E] = U_Common;
  (*smap)[0x201F] = U_Common;
  for (char32 c = 0x2020; c <= 0x2027; ++c) (*smap)[c] = U_Common;
  (*smap)[0x2028] = U_Common;
  (*smap)[0x2029] = U_Common;
  for (char32 c = 0x202A; c <= 0x202E; ++c) (*smap)[c] = U_Common;
  (*smap)[0x202F] = U_Common;
  for (char32 c = 0x2030; c <= 0x2038; ++c) (*smap)[c] = U_Common;
  (*smap)[0x2039] = U_Common;
  (*smap)[0x203A] = U_Common;
  for (char32 c = 0x203B; c <= 0x203E; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x203F; c <= 0x2040; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x2041; c <= 0x2043; ++c) (*smap)[c] = U_Common;
  (*smap)[0x2044] = U_Common;
  (*smap)[0x2045] = U_Common;
  (*smap)[0x2046] = U_Common;
  for (char32 c = 0x2047; c <= 0x2051; ++c) (*smap)[c] = U_Common;
  (*smap)[0x2052] = U_Common;
  (*smap)[0x2053] = U_Common;
  (*smap)[0x2054] = U_Common;
  for (char32 c = 0x2055; c <= 0x205E; ++c) (*smap)[c] = U_Common;
  (*smap)[0x205F] = U_Common;
  for (char32 c = 0x2060; c <= 0x2064; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x2066; c <= 0x206F; ++c) (*smap)[c] = U_Common;
  (*smap)[0x2070] = U_Common;
  for (char32 c = 0x2074; c <= 0x2079; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x207A; c <= 0x207C; ++c) (*smap)[c] = U_Common;
  (*smap)[0x207D] = U_Common;
  (*smap)[0x207E] = U_Common;
  for (char32 c = 0x2080; c <= 0x2089; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x208A; c <= 0x208C; ++c) (*smap)[c] = U_Common;
  (*smap)[0x208D] = U_Common;
  (*smap)[0x208E] = U_Common;
  for (char32 c = 0x20A0; c <= 0x20BE; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x2100; c <= 0x2101; ++c) (*smap)[c] = U_Common;
  (*smap)[0x2102] = U_Common;
  for (char32 c = 0x2103; c <= 0x2106; ++c) (*smap)[c] = U_Common;
  (*smap)[0x2107] = U_Common;
  for (char32 c = 0x2108; c <= 0x2109; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x210A; c <= 0x2113; ++c) (*smap)[c] = U_Common;
  (*smap)[0x2114] = U_Common;
  (*smap)[0x2115] = U_Common;
  for (char32 c = 0x2116; c <= 0x2117; ++c) (*smap)[c] = U_Common;
  (*smap)[0x2118] = U_Common;
  for (char32 c = 0x2119; c <= 0x211D; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x211E; c <= 0x2123; ++c) (*smap)[c] = U_Common;
  (*smap)[0x2124] = U_Common;
  (*smap)[0x2125] = U_Common;
  (*smap)[0x2127] = U_Common;
  (*smap)[0x2128] = U_Common;
  (*smap)[0x2129] = U_Common;
  for (char32 c = 0x212C; c <= 0x212D; ++c) (*smap)[c] = U_Common;
  (*smap)[0x212E] = U_Common;
  for (char32 c = 0x212F; c <= 0x2131; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x2133; c <= 0x2134; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x2135; c <= 0x2138; ++c) (*smap)[c] = U_Common;
  (*smap)[0x2139] = U_Common;
  for (char32 c = 0x213A; c <= 0x213B; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x213C; c <= 0x213F; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x2140; c <= 0x2144; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x2145; c <= 0x2149; ++c) (*smap)[c] = U_Common;
  (*smap)[0x214A] = U_Common;
  (*smap)[0x214B] = U_Common;
  for (char32 c = 0x214C; c <= 0x214D; ++c) (*smap)[c] = U_Common;
  (*smap)[0x214F] = U_Common;
  for (char32 c = 0x2150; c <= 0x215F; ++c) (*smap)[c] = U_Common;
  (*smap)[0x2189] = U_Common;
  for (char32 c = 0x218A; c <= 0x218B; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x2190; c <= 0x2194; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x2195; c <= 0x2199; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x219A; c <= 0x219B; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x219C; c <= 0x219F; ++c) (*smap)[c] = U_Common;
  (*smap)[0x21A0] = U_Common;
  for (char32 c = 0x21A1; c <= 0x21A2; ++c) (*smap)[c] = U_Common;
  (*smap)[0x21A3] = U_Common;
  for (char32 c = 0x21A4; c <= 0x21A5; ++c) (*smap)[c] = U_Common;
  (*smap)[0x21A6] = U_Common;
  for (char32 c = 0x21A7; c <= 0x21AD; ++c) (*smap)[c] = U_Common;
  (*smap)[0x21AE] = U_Common;
  for (char32 c = 0x21AF; c <= 0x21CD; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x21CE; c <= 0x21CF; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x21D0; c <= 0x21D1; ++c) (*smap)[c] = U_Common;
  (*smap)[0x21D2] = U_Common;
  (*smap)[0x21D3] = U_Common;
  (*smap)[0x21D4] = U_Common;
  for (char32 c = 0x21D5; c <= 0x21F3; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x21F4; c <= 0x22FF; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x2300; c <= 0x2307; ++c) (*smap)[c] = U_Common;
  (*smap)[0x2308] = U_Common;
  (*smap)[0x2309] = U_Common;
  (*smap)[0x230A] = U_Common;
  (*smap)[0x230B] = U_Common;
  for (char32 c = 0x230C; c <= 0x231F; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x2320; c <= 0x2321; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x2322; c <= 0x2328; ++c) (*smap)[c] = U_Common;
  (*smap)[0x2329] = U_Common;
  (*smap)[0x232A] = U_Common;
  for (char32 c = 0x232B; c <= 0x237B; ++c) (*smap)[c] = U_Common;
  (*smap)[0x237C] = U_Common;
  for (char32 c = 0x237D; c <= 0x239A; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x239B; c <= 0x23B3; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x23B4; c <= 0x23DB; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x23DC; c <= 0x23E1; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x23E2; c <= 0x23FE; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x2400; c <= 0x2426; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x2440; c <= 0x244A; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x2460; c <= 0x249B; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x249C; c <= 0x24E9; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x24EA; c <= 0x24FF; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x2500; c <= 0x25B6; ++c) (*smap)[c] = U_Common;
  (*smap)[0x25B7] = U_Common;
  for (char32 c = 0x25B8; c <= 0x25C0; ++c) (*smap)[c] = U_Common;
  (*smap)[0x25C1] = U_Common;
  for (char32 c = 0x25C2; c <= 0x25F7; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x25F8; c <= 0x25FF; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x2600; c <= 0x266E; ++c) (*smap)[c] = U_Common;
  (*smap)[0x266F] = U_Common;
  for (char32 c = 0x2670; c <= 0x2767; ++c) (*smap)[c] = U_Common;
  (*smap)[0x2768] = U_Common;
  (*smap)[0x2769] = U_Common;
  (*smap)[0x276A] = U_Common;
  (*smap)[0x276B] = U_Common;
  (*smap)[0x276C] = U_Common;
  (*smap)[0x276D] = U_Common;
  (*smap)[0x276E] = U_Common;
  (*smap)[0x276F] = U_Common;
  (*smap)[0x2770] = U_Common;
  (*smap)[0x2771] = U_Common;
  (*smap)[0x2772] = U_Common;
  (*smap)[0x2773] = U_Common;
  (*smap)[0x2774] = U_Common;
  (*smap)[0x2775] = U_Common;
  for (char32 c = 0x2776; c <= 0x2793; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x2794; c <= 0x27BF; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x27C0; c <= 0x27C4; ++c) (*smap)[c] = U_Common;
  (*smap)[0x27C5] = U_Common;
  (*smap)[0x27C6] = U_Common;
  for (char32 c = 0x27C7; c <= 0x27E5; ++c) (*smap)[c] = U_Common;
  (*smap)[0x27E6] = U_Common;
  (*smap)[0x27E7] = U_Common;
  (*smap)[0x27E8] = U_Common;
  (*smap)[0x27E9] = U_Common;
  (*smap)[0x27EA] = U_Common;
  (*smap)[0x27EB] = U_Common;
  (*smap)[0x27EC] = U_Common;
  (*smap)[0x27ED] = U_Common;
  (*smap)[0x27EE] = U_Common;
  (*smap)[0x27EF] = U_Common;
  for (char32 c = 0x27F0; c <= 0x27FF; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x2900; c <= 0x2982; ++c) (*smap)[c] = U_Common;
  (*smap)[0x2983] = U_Common;
  (*smap)[0x2984] = U_Common;
  (*smap)[0x2985] = U_Common;
  (*smap)[0x2986] = U_Common;
  (*smap)[0x2987] = U_Common;
  (*smap)[0x2988] = U_Common;
  (*smap)[0x2989] = U_Common;
  (*smap)[0x298A] = U_Common;
  (*smap)[0x298B] = U_Common;
  (*smap)[0x298C] = U_Common;
  (*smap)[0x298D] = U_Common;
  (*smap)[0x298E] = U_Common;
  (*smap)[0x298F] = U_Common;
  (*smap)[0x2990] = U_Common;
  (*smap)[0x2991] = U_Common;
  (*smap)[0x2992] = U_Common;
  (*smap)[0x2993] = U_Common;
  (*smap)[0x2994] = U_Common;
  (*smap)[0x2995] = U_Common;
  (*smap)[0x2996] = U_Common;
  (*smap)[0x2997] = U_Common;
  (*smap)[0x2998] = U_Common;
  for (char32 c = 0x2999; c <= 0x29D7; ++c) (*smap)[c] = U_Common;
  (*smap)[0x29D8] = U_Common;
  (*smap)[0x29D9] = U_Common;
  (*smap)[0x29DA] = U_Common;
  (*smap)[0x29DB] = U_Common;
  for (char32 c = 0x29DC; c <= 0x29FB; ++c) (*smap)[c] = U_Common;
  (*smap)[0x29FC] = U_Common;
  (*smap)[0x29FD] = U_Common;
  for (char32 c = 0x29FE; c <= 0x2AFF; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x2B00; c <= 0x2B2F; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x2B30; c <= 0x2B44; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x2B45; c <= 0x2B46; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x2B47; c <= 0x2B4C; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x2B4D; c <= 0x2B73; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x2B76; c <= 0x2B95; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x2B98; c <= 0x2BB9; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x2BBD; c <= 0x2BC8; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x2BCA; c <= 0x2BD1; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x2BEC; c <= 0x2BEF; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x2E00; c <= 0x2E01; ++c) (*smap)[c] = U_Common;
  (*smap)[0x2E02] = U_Common;
  (*smap)[0x2E03] = U_Common;
  (*smap)[0x2E04] = U_Common;
  (*smap)[0x2E05] = U_Common;
  for (char32 c = 0x2E06; c <= 0x2E08; ++c) (*smap)[c] = U_Common;
  (*smap)[0x2E09] = U_Common;
  (*smap)[0x2E0A] = U_Common;
  (*smap)[0x2E0B] = U_Common;
  (*smap)[0x2E0C] = U_Common;
  (*smap)[0x2E0D] = U_Common;
  for (char32 c = 0x2E0E; c <= 0x2E16; ++c) (*smap)[c] = U_Common;
  (*smap)[0x2E17] = U_Common;
  for (char32 c = 0x2E18; c <= 0x2E19; ++c) (*smap)[c] = U_Common;
  (*smap)[0x2E1A] = U_Common;
  (*smap)[0x2E1B] = U_Common;
  (*smap)[0x2E1C] = U_Common;
  (*smap)[0x2E1D] = U_Common;
  for (char32 c = 0x2E1E; c <= 0x2E1F; ++c) (*smap)[c] = U_Common;
  (*smap)[0x2E20] = U_Common;
  (*smap)[0x2E21] = U_Common;
  (*smap)[0x2E22] = U_Common;
  (*smap)[0x2E23] = U_Common;
  (*smap)[0x2E24] = U_Common;
  (*smap)[0x2E25] = U_Common;
  (*smap)[0x2E26] = U_Common;
  (*smap)[0x2E27] = U_Common;
  (*smap)[0x2E28] = U_Common;
  (*smap)[0x2E29] = U_Common;
  for (char32 c = 0x2E2A; c <= 0x2E2E; ++c) (*smap)[c] = U_Common;
  (*smap)[0x2E2F] = U_Common;
  for (char32 c = 0x2E30; c <= 0x2E39; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x2E3A; c <= 0x2E3B; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x2E3C; c <= 0x2E3F; ++c) (*smap)[c] = U_Common;
  (*smap)[0x2E40] = U_Common;
  (*smap)[0x2E41] = U_Common;
  (*smap)[0x2E42] = U_Common;
  for (char32 c = 0x2E43; c <= 0x2E44; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x2FF0; c <= 0x2FFB; ++c) (*smap)[c] = U_Common;
  (*smap)[0x3000] = U_Common;
  for (char32 c = 0x3001; c <= 0x3003; ++c) (*smap)[c] = U_Common;
  (*smap)[0x3004] = U_Common;
  (*smap)[0x3006] = U_Common;
  (*smap)[0x3008] = U_Common;
  (*smap)[0x3009] = U_Common;
  (*smap)[0x300A] = U_Common;
  (*smap)[0x300B] = U_Common;
  (*smap)[0x300C] = U_Common;
  (*smap)[0x300D] = U_Common;
  (*smap)[0x300E] = U_Common;
  (*smap)[0x300F] = U_Common;
  (*smap)[0x3010] = U_Common;
  (*smap)[0x3011] = U_Common;
  for (char32 c = 0x3012; c <= 0x3013; ++c) (*smap)[c] = U_Common;
  (*smap)[0x3014] = U_Common;
  (*smap)[0x3015] = U_Common;
  (*smap)[0x3016] = U_Common;
  (*smap)[0x3017] = U_Common;
  (*smap)[0x3018] = U_Common;
  (*smap)[0x3019] = U_Common;
  (*smap)[0x301A] = U_Common;
  (*smap)[0x301B] = U_Common;
  (*smap)[0x301C] = U_Common;
  (*smap)[0x301D] = U_Common;
  for (char32 c = 0x301E; c <= 0x301F; ++c) (*smap)[c] = U_Common;
  (*smap)[0x3020] = U_Common;
  (*smap)[0x3030] = U_Common;
  for (char32 c = 0x3031; c <= 0x3035; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x3036; c <= 0x3037; ++c) (*smap)[c] = U_Common;
  (*smap)[0x303C] = U_Common;
  (*smap)[0x303D] = U_Common;
  for (char32 c = 0x303E; c <= 0x303F; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x309B; c <= 0x309C; ++c) (*smap)[c] = U_Common;
  (*smap)[0x30A0] = U_Common;
  (*smap)[0x30FB] = U_Common;
  (*smap)[0x30FC] = U_Common;
  for (char32 c = 0x3190; c <= 0x3191; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x3192; c <= 0x3195; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x3196; c <= 0x319F; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x31C0; c <= 0x31E3; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x3220; c <= 0x3229; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x322A; c <= 0x3247; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x3248; c <= 0x324F; ++c) (*smap)[c] = U_Common;
  (*smap)[0x3250] = U_Common;
  for (char32 c = 0x3251; c <= 0x325F; ++c) (*smap)[c] = U_Common;
  (*smap)[0x327F] = U_Common;
  for (char32 c = 0x3280; c <= 0x3289; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x328A; c <= 0x32B0; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x32B1; c <= 0x32BF; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x32C0; c <= 0x32CF; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x3358; c <= 0x33FF; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x4DC0; c <= 0x4DFF; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0xA700; c <= 0xA716; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0xA717; c <= 0xA71F; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0xA720; c <= 0xA721; ++c) (*smap)[c] = U_Common;
  (*smap)[0xA788] = U_Common;
  for (char32 c = 0xA789; c <= 0xA78A; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0xA830; c <= 0xA835; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0xA836; c <= 0xA837; ++c) (*smap)[c] = U_Common;
  (*smap)[0xA838] = U_Common;
  (*smap)[0xA839] = U_Common;
  (*smap)[0xA92E] = U_Common;
  (*smap)[0xA9CF] = U_Common;
  (*smap)[0xAB5B] = U_Common;
  (*smap)[0xFD3E] = U_Common;
  (*smap)[0xFD3F] = U_Common;
  for (char32 c = 0xFE10; c <= 0xFE16; ++c) (*smap)[c] = U_Common;
  (*smap)[0xFE17] = U_Common;
  (*smap)[0xFE18] = U_Common;
  (*smap)[0xFE19] = U_Common;
  (*smap)[0xFE30] = U_Common;
  for (char32 c = 0xFE31; c <= 0xFE32; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0xFE33; c <= 0xFE34; ++c) (*smap)[c] = U_Common;
  (*smap)[0xFE35] = U_Common;
  (*smap)[0xFE36] = U_Common;
  (*smap)[0xFE37] = U_Common;
  (*smap)[0xFE38] = U_Common;
  (*smap)[0xFE39] = U_Common;
  (*smap)[0xFE3A] = U_Common;
  (*smap)[0xFE3B] = U_Common;
  (*smap)[0xFE3C] = U_Common;
  (*smap)[0xFE3D] = U_Common;
  (*smap)[0xFE3E] = U_Common;
  (*smap)[0xFE3F] = U_Common;
  (*smap)[0xFE40] = U_Common;
  (*smap)[0xFE41] = U_Common;
  (*smap)[0xFE42] = U_Common;
  (*smap)[0xFE43] = U_Common;
  (*smap)[0xFE44] = U_Common;
  for (char32 c = 0xFE45; c <= 0xFE46; ++c) (*smap)[c] = U_Common;
  (*smap)[0xFE47] = U_Common;
  (*smap)[0xFE48] = U_Common;
  for (char32 c = 0xFE49; c <= 0xFE4C; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0xFE4D; c <= 0xFE4F; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0xFE50; c <= 0xFE52; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0xFE54; c <= 0xFE57; ++c) (*smap)[c] = U_Common;
  (*smap)[0xFE58] = U_Common;
  (*smap)[0xFE59] = U_Common;
  (*smap)[0xFE5A] = U_Common;
  (*smap)[0xFE5B] = U_Common;
  (*smap)[0xFE5C] = U_Common;
  (*smap)[0xFE5D] = U_Common;
  (*smap)[0xFE5E] = U_Common;
  for (char32 c = 0xFE5F; c <= 0xFE61; ++c) (*smap)[c] = U_Common;
  (*smap)[0xFE62] = U_Common;
  (*smap)[0xFE63] = U_Common;
  for (char32 c = 0xFE64; c <= 0xFE66; ++c) (*smap)[c] = U_Common;
  (*smap)[0xFE68] = U_Common;
  (*smap)[0xFE69] = U_Common;
  for (char32 c = 0xFE6A; c <= 0xFE6B; ++c) (*smap)[c] = U_Common;
  (*smap)[0xFEFF] = U_Common;
  for (char32 c = 0xFF01; c <= 0xFF03; ++c) (*smap)[c] = U_Common;
  (*smap)[0xFF04] = U_Common;
  for (char32 c = 0xFF05; c <= 0xFF07; ++c) (*smap)[c] = U_Common;
  (*smap)[0xFF08] = U_Common;
  (*smap)[0xFF09] = U_Common;
  (*smap)[0xFF0A] = U_Common;
  (*smap)[0xFF0B] = U_Common;
  (*smap)[0xFF0C] = U_Common;
  (*smap)[0xFF0D] = U_Common;
  for (char32 c = 0xFF0E; c <= 0xFF0F; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0xFF10; c <= 0xFF19; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0xFF1A; c <= 0xFF1B; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0xFF1C; c <= 0xFF1E; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0xFF1F; c <= 0xFF20; ++c) (*smap)[c] = U_Common;
  (*smap)[0xFF3B] = U_Common;
  (*smap)[0xFF3C] = U_Common;
  (*smap)[0xFF3D] = U_Common;
  (*smap)[0xFF3E] = U_Common;
  (*smap)[0xFF3F] = U_Common;
  (*smap)[0xFF40] = U_Common;
  (*smap)[0xFF5B] = U_Common;
  (*smap)[0xFF5C] = U_Common;
  (*smap)[0xFF5D] = U_Common;
  (*smap)[0xFF5E] = U_Common;
  (*smap)[0xFF5F] = U_Common;
  (*smap)[0xFF60] = U_Common;
  (*smap)[0xFF61] = U_Common;
  (*smap)[0xFF62] = U_Common;
  (*smap)[0xFF63] = U_Common;
  for (char32 c = 0xFF64; c <= 0xFF65; ++c) (*smap)[c] = U_Common;
  (*smap)[0xFF70] = U_Common;
  for (char32 c = 0xFF9E; c <= 0xFF9F; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0xFFE0; c <= 0xFFE1; ++c) (*smap)[c] = U_Common;
  (*smap)[0xFFE2] = U_Common;
  (*smap)[0xFFE3] = U_Common;
  (*smap)[0xFFE4] = U_Common;
  for (char32 c = 0xFFE5; c <= 0xFFE6; ++c) (*smap)[c] = U_Common;
  (*smap)[0xFFE8] = U_Common;
  for (char32 c = 0xFFE9; c <= 0xFFEC; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0xFFED; c <= 0xFFEE; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0xFFF9; c <= 0xFFFB; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0xFFFC; c <= 0xFFFD; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x10100; c <= 0x10102; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x10107; c <= 0x10133; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x10137; c <= 0x1013F; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x10190; c <= 0x1019B; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x101D0; c <= 0x101FC; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x102E1; c <= 0x102FB; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1BCA0; c <= 0x1BCA3; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1D000; c <= 0x1D0F5; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1D100; c <= 0x1D126; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1D129; c <= 0x1D164; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1D165; c <= 0x1D166; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1D16A; c <= 0x1D16C; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1D16D; c <= 0x1D172; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1D173; c <= 0x1D17A; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1D183; c <= 0x1D184; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1D18C; c <= 0x1D1A9; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1D1AE; c <= 0x1D1E8; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1D300; c <= 0x1D356; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1D360; c <= 0x1D371; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1D400; c <= 0x1D454; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1D456; c <= 0x1D49C; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1D49E; c <= 0x1D49F; ++c) (*smap)[c] = U_Common;
  (*smap)[0x1D4A2] = U_Common;
  for (char32 c = 0x1D4A5; c <= 0x1D4A6; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1D4A9; c <= 0x1D4AC; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1D4AE; c <= 0x1D4B9; ++c) (*smap)[c] = U_Common;
  (*smap)[0x1D4BB] = U_Common;
  for (char32 c = 0x1D4BD; c <= 0x1D4C3; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1D4C5; c <= 0x1D505; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1D507; c <= 0x1D50A; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1D50D; c <= 0x1D514; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1D516; c <= 0x1D51C; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1D51E; c <= 0x1D539; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1D53B; c <= 0x1D53E; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1D540; c <= 0x1D544; ++c) (*smap)[c] = U_Common;
  (*smap)[0x1D546] = U_Common;
  for (char32 c = 0x1D54A; c <= 0x1D550; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1D552; c <= 0x1D6A5; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1D6A8; c <= 0x1D6C0; ++c) (*smap)[c] = U_Common;
  (*smap)[0x1D6C1] = U_Common;
  for (char32 c = 0x1D6C2; c <= 0x1D6DA; ++c) (*smap)[c] = U_Common;
  (*smap)[0x1D6DB] = U_Common;
  for (char32 c = 0x1D6DC; c <= 0x1D6FA; ++c) (*smap)[c] = U_Common;
  (*smap)[0x1D6FB] = U_Common;
  for (char32 c = 0x1D6FC; c <= 0x1D714; ++c) (*smap)[c] = U_Common;
  (*smap)[0x1D715] = U_Common;
  for (char32 c = 0x1D716; c <= 0x1D734; ++c) (*smap)[c] = U_Common;
  (*smap)[0x1D735] = U_Common;
  for (char32 c = 0x1D736; c <= 0x1D74E; ++c) (*smap)[c] = U_Common;
  (*smap)[0x1D74F] = U_Common;
  for (char32 c = 0x1D750; c <= 0x1D76E; ++c) (*smap)[c] = U_Common;
  (*smap)[0x1D76F] = U_Common;
  for (char32 c = 0x1D770; c <= 0x1D788; ++c) (*smap)[c] = U_Common;
  (*smap)[0x1D789] = U_Common;
  for (char32 c = 0x1D78A; c <= 0x1D7A8; ++c) (*smap)[c] = U_Common;
  (*smap)[0x1D7A9] = U_Common;
  for (char32 c = 0x1D7AA; c <= 0x1D7C2; ++c) (*smap)[c] = U_Common;
  (*smap)[0x1D7C3] = U_Common;
  for (char32 c = 0x1D7C4; c <= 0x1D7CB; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1D7CE; c <= 0x1D7FF; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1F000; c <= 0x1F02B; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1F030; c <= 0x1F093; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1F0A0; c <= 0x1F0AE; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1F0B1; c <= 0x1F0BF; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1F0C1; c <= 0x1F0CF; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1F0D1; c <= 0x1F0F5; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1F100; c <= 0x1F10C; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1F110; c <= 0x1F12E; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1F130; c <= 0x1F16B; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1F170; c <= 0x1F1AC; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1F1E6; c <= 0x1F1FF; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1F201; c <= 0x1F202; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1F210; c <= 0x1F23B; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1F240; c <= 0x1F248; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1F250; c <= 0x1F251; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1F300; c <= 0x1F3FA; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1F3FB; c <= 0x1F3FF; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1F400; c <= 0x1F6D2; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1F6E0; c <= 0x1F6EC; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1F6F0; c <= 0x1F6F6; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1F700; c <= 0x1F773; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1F780; c <= 0x1F7D4; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1F800; c <= 0x1F80B; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1F810; c <= 0x1F847; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1F850; c <= 0x1F859; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1F860; c <= 0x1F887; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1F890; c <= 0x1F8AD; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1F910; c <= 0x1F91E; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1F920; c <= 0x1F927; ++c) (*smap)[c] = U_Common;
  (*smap)[0x1F930] = U_Common;
  for (char32 c = 0x1F933; c <= 0x1F93E; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1F940; c <= 0x1F94B; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1F950; c <= 0x1F95E; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x1F980; c <= 0x1F991; ++c) (*smap)[c] = U_Common;
  (*smap)[0x1F9C0] = U_Common;
  (*smap)[0xE0001] = U_Common;
  for (char32 c = 0xE0020; c <= 0xE007F; ++c) (*smap)[c] = U_Common;
  for (char32 c = 0x0041; c <= 0x005A; ++c) (*smap)[c] = U_Latin;
  for (char32 c = 0x0061; c <= 0x007A; ++c) (*smap)[c] = U_Latin;
  (*smap)[0x00AA] = U_Latin;
  (*smap)[0x00BA] = U_Latin;
  for (char32 c = 0x00C0; c <= 0x00D6; ++c) (*smap)[c] = U_Latin;
  for (char32 c = 0x00D8; c <= 0x00F6; ++c) (*smap)[c] = U_Latin;
  for (char32 c = 0x00F8; c <= 0x01BA; ++c) (*smap)[c] = U_Latin;
  (*smap)[0x01BB] = U_Latin;
  for (char32 c = 0x01BC; c <= 0x01BF; ++c) (*smap)[c] = U_Latin;
  for (char32 c = 0x01C0; c <= 0x01C3; ++c) (*smap)[c] = U_Latin;
  for (char32 c = 0x01C4; c <= 0x0293; ++c) (*smap)[c] = U_Latin;
  (*smap)[0x0294] = U_Latin;
  for (char32 c = 0x0295; c <= 0x02AF; ++c) (*smap)[c] = U_Latin;
  for (char32 c = 0x02B0; c <= 0x02B8; ++c) (*smap)[c] = U_Latin;
  for (char32 c = 0x02E0; c <= 0x02E4; ++c) (*smap)[c] = U_Latin;
  for (char32 c = 0x1D00; c <= 0x1D25; ++c) (*smap)[c] = U_Latin;
  for (char32 c = 0x1D2C; c <= 0x1D5C; ++c) (*smap)[c] = U_Latin;
  for (char32 c = 0x1D62; c <= 0x1D65; ++c) (*smap)[c] = U_Latin;
  for (char32 c = 0x1D6B; c <= 0x1D77; ++c) (*smap)[c] = U_Latin;
  for (char32 c = 0x1D79; c <= 0x1D9A; ++c) (*smap)[c] = U_Latin;
  for (char32 c = 0x1D9B; c <= 0x1DBE; ++c) (*smap)[c] = U_Latin;
  for (char32 c = 0x1E00; c <= 0x1EFF; ++c) (*smap)[c] = U_Latin;
  (*smap)[0x2071] = U_Latin;
  (*smap)[0x207F] = U_Latin;
  for (char32 c = 0x2090; c <= 0x209C; ++c) (*smap)[c] = U_Latin;
  for (char32 c = 0x212A; c <= 0x212B; ++c) (*smap)[c] = U_Latin;
  (*smap)[0x2132] = U_Latin;
  (*smap)[0x214E] = U_Latin;
  for (char32 c = 0x2160; c <= 0x2182; ++c) (*smap)[c] = U_Latin;
  for (char32 c = 0x2183; c <= 0x2184; ++c) (*smap)[c] = U_Latin;
  for (char32 c = 0x2185; c <= 0x2188; ++c) (*smap)[c] = U_Latin;
  for (char32 c = 0x2C60; c <= 0x2C7B; ++c) (*smap)[c] = U_Latin;
  for (char32 c = 0x2C7C; c <= 0x2C7D; ++c) (*smap)[c] = U_Latin;
  for (char32 c = 0x2C7E; c <= 0x2C7F; ++c) (*smap)[c] = U_Latin;
  for (char32 c = 0xA722; c <= 0xA76F; ++c) (*smap)[c] = U_Latin;
  (*smap)[0xA770] = U_Latin;
  for (char32 c = 0xA771; c <= 0xA787; ++c) (*smap)[c] = U_Latin;
  for (char32 c = 0xA78B; c <= 0xA78E; ++c) (*smap)[c] = U_Latin;
  (*smap)[0xA78F] = U_Latin;
  for (char32 c = 0xA790; c <= 0xA7AE; ++c) (*smap)[c] = U_Latin;
  for (char32 c = 0xA7B0; c <= 0xA7B7; ++c) (*smap)[c] = U_Latin;
  (*smap)[0xA7F7] = U_Latin;
  for (char32 c = 0xA7F8; c <= 0xA7F9; ++c) (*smap)[c] = U_Latin;
  (*smap)[0xA7FA] = U_Latin;
  for (char32 c = 0xA7FB; c <= 0xA7FF; ++c) (*smap)[c] = U_Latin;
  for (char32 c = 0xAB30; c <= 0xAB5A; ++c) (*smap)[c] = U_Latin;
  for (char32 c = 0xAB5C; c <= 0xAB5F; ++c) (*smap)[c] = U_Latin;
  for (char32 c = 0xAB60; c <= 0xAB64; ++c) (*smap)[c] = U_Latin;
  for (char32 c = 0xFB00; c <= 0xFB06; ++c) (*smap)[c] = U_Latin;
  for (char32 c = 0xFF21; c <= 0xFF3A; ++c) (*smap)[c] = U_Latin;
  for (char32 c = 0xFF41; c <= 0xFF5A; ++c) (*smap)[c] = U_Latin;
  for (char32 c = 0x0370; c <= 0x0373; ++c) (*smap)[c] = U_Greek;
  (*smap)[0x0375] = U_Greek;
  for (char32 c = 0x0376; c <= 0x0377; ++c) (*smap)[c] = U_Greek;
  (*smap)[0x037A] = U_Greek;
  for (char32 c = 0x037B; c <= 0x037D; ++c) (*smap)[c] = U_Greek;
  (*smap)[0x037F] = U_Greek;
  (*smap)[0x0384] = U_Greek;
  (*smap)[0x0386] = U_Greek;
  for (char32 c = 0x0388; c <= 0x038A; ++c) (*smap)[c] = U_Greek;
  (*smap)[0x038C] = U_Greek;
  for (char32 c = 0x038E; c <= 0x03A1; ++c) (*smap)[c] = U_Greek;
  for (char32 c = 0x03A3; c <= 0x03E1; ++c) (*smap)[c] = U_Greek;
  for (char32 c = 0x03F0; c <= 0x03F5; ++c) (*smap)[c] = U_Greek;
  (*smap)[0x03F6] = U_Greek;
  for (char32 c = 0x03F7; c <= 0x03FF; ++c) (*smap)[c] = U_Greek;
  for (char32 c = 0x1D26; c <= 0x1D2A; ++c) (*smap)[c] = U_Greek;
  for (char32 c = 0x1D5D; c <= 0x1D61; ++c) (*smap)[c] = U_Greek;
  for (char32 c = 0x1D66; c <= 0x1D6A; ++c) (*smap)[c] = U_Greek;
  (*smap)[0x1DBF] = U_Greek;
  for (char32 c = 0x1F00; c <= 0x1F15; ++c) (*smap)[c] = U_Greek;
  for (char32 c = 0x1F18; c <= 0x1F1D; ++c) (*smap)[c] = U_Greek;
  for (char32 c = 0x1F20; c <= 0x1F45; ++c) (*smap)[c] = U_Greek;
  for (char32 c = 0x1F48; c <= 0x1F4D; ++c) (*smap)[c] = U_Greek;
  for (char32 c = 0x1F50; c <= 0x1F57; ++c) (*smap)[c] = U_Greek;
  (*smap)[0x1F59] = U_Greek;
  (*smap)[0x1F5B] = U_Greek;
  (*smap)[0x1F5D] = U_Greek;
  for (char32 c = 0x1F5F; c <= 0x1F7D; ++c) (*smap)[c] = U_Greek;
  for (char32 c = 0x1F80; c <= 0x1FB4; ++c) (*smap)[c] = U_Greek;
  for (char32 c = 0x1FB6; c <= 0x1FBC; ++c) (*smap)[c] = U_Greek;
  (*smap)[0x1FBD] = U_Greek;
  (*smap)[0x1FBE] = U_Greek;
  for (char32 c = 0x1FBF; c <= 0x1FC1; ++c) (*smap)[c] = U_Greek;
  for (char32 c = 0x1FC2; c <= 0x1FC4; ++c) (*smap)[c] = U_Greek;
  for (char32 c = 0x1FC6; c <= 0x1FCC; ++c) (*smap)[c] = U_Greek;
  for (char32 c = 0x1FCD; c <= 0x1FCF; ++c) (*smap)[c] = U_Greek;
  for (char32 c = 0x1FD0; c <= 0x1FD3; ++c) (*smap)[c] = U_Greek;
  for (char32 c = 0x1FD6; c <= 0x1FDB; ++c) (*smap)[c] = U_Greek;
  for (char32 c = 0x1FDD; c <= 0x1FDF; ++c) (*smap)[c] = U_Greek;
  for (char32 c = 0x1FE0; c <= 0x1FEC; ++c) (*smap)[c] = U_Greek;
  for (char32 c = 0x1FED; c <= 0x1FEF; ++c) (*smap)[c] = U_Greek;
  for (char32 c = 0x1FF2; c <= 0x1FF4; ++c) (*smap)[c] = U_Greek;
  for (char32 c = 0x1FF6; c <= 0x1FFC; ++c) (*smap)[c] = U_Greek;
  for (char32 c = 0x1FFD; c <= 0x1FFE; ++c) (*smap)[c] = U_Greek;
  (*smap)[0x2126] = U_Greek;
  (*smap)[0xAB65] = U_Greek;
  for (char32 c = 0x10140; c <= 0x10174; ++c) (*smap)[c] = U_Greek;
  for (char32 c = 0x10175; c <= 0x10178; ++c) (*smap)[c] = U_Greek;
  for (char32 c = 0x10179; c <= 0x10189; ++c) (*smap)[c] = U_Greek;
  for (char32 c = 0x1018A; c <= 0x1018B; ++c) (*smap)[c] = U_Greek;
  for (char32 c = 0x1018C; c <= 0x1018E; ++c) (*smap)[c] = U_Greek;
  (*smap)[0x101A0] = U_Greek;
  for (char32 c = 0x1D200; c <= 0x1D241; ++c) (*smap)[c] = U_Greek;
  for (char32 c = 0x1D242; c <= 0x1D244; ++c) (*smap)[c] = U_Greek;
  (*smap)[0x1D245] = U_Greek;
  for (char32 c = 0x0400; c <= 0x0481; ++c) (*smap)[c] = U_Cyrillic;
  (*smap)[0x0482] = U_Cyrillic;
  for (char32 c = 0x0483; c <= 0x0484; ++c) (*smap)[c] = U_Cyrillic;
  (*smap)[0x0487] = U_Cyrillic;
  for (char32 c = 0x0488; c <= 0x0489; ++c) (*smap)[c] = U_Cyrillic;
  for (char32 c = 0x048A; c <= 0x052F; ++c) (*smap)[c] = U_Cyrillic;
  for (char32 c = 0x1C80; c <= 0x1C88; ++c) (*smap)[c] = U_Cyrillic;
  (*smap)[0x1D2B] = U_Cyrillic;
  (*smap)[0x1D78] = U_Cyrillic;
  for (char32 c = 0x2DE0; c <= 0x2DFF; ++c) (*smap)[c] = U_Cyrillic;
  for (char32 c = 0xA640; c <= 0xA66D; ++c) (*smap)[c] = U_Cyrillic;
  (*smap)[0xA66E] = U_Cyrillic;
  (*smap)[0xA66F] = U_Cyrillic;
  for (char32 c = 0xA670; c <= 0xA672; ++c) (*smap)[c] = U_Cyrillic;
  (*smap)[0xA673] = U_Cyrillic;
  for (char32 c = 0xA674; c <= 0xA67D; ++c) (*smap)[c] = U_Cyrillic;
  (*smap)[0xA67E] = U_Cyrillic;
  (*smap)[0xA67F] = U_Cyrillic;
  for (char32 c = 0xA680; c <= 0xA69B; ++c) (*smap)[c] = U_Cyrillic;
  for (char32 c = 0xA69C; c <= 0xA69D; ++c) (*smap)[c] = U_Cyrillic;
  for (char32 c = 0xA69E; c <= 0xA69F; ++c) (*smap)[c] = U_Cyrillic;
  for (char32 c = 0xFE2E; c <= 0xFE2F; ++c) (*smap)[c] = U_Cyrillic;
  for (char32 c = 0x0531; c <= 0x0556; ++c) (*smap)[c] = U_Armenian;
  (*smap)[0x0559] = U_Armenian;
  for (char32 c = 0x055A; c <= 0x055F; ++c) (*smap)[c] = U_Armenian;
  for (char32 c = 0x0561; c <= 0x0587; ++c) (*smap)[c] = U_Armenian;
  (*smap)[0x058A] = U_Armenian;
  for (char32 c = 0x058D; c <= 0x058E; ++c) (*smap)[c] = U_Armenian;
  (*smap)[0x058F] = U_Armenian;
  for (char32 c = 0xFB13; c <= 0xFB17; ++c) (*smap)[c] = U_Armenian;
  for (char32 c = 0x0591; c <= 0x05BD; ++c) (*smap)[c] = U_Hebrew;
  (*smap)[0x05BE] = U_Hebrew;
  (*smap)[0x05BF] = U_Hebrew;
  (*smap)[0x05C0] = U_Hebrew;
  for (char32 c = 0x05C1; c <= 0x05C2; ++c) (*smap)[c] = U_Hebrew;
  (*smap)[0x05C3] = U_Hebrew;
  for (char32 c = 0x05C4; c <= 0x05C5; ++c) (*smap)[c] = U_Hebrew;
  (*smap)[0x05C6] = U_Hebrew;
  (*smap)[0x05C7] = U_Hebrew;
  for (char32 c = 0x05D0; c <= 0x05EA; ++c) (*smap)[c] = U_Hebrew;
  for (char32 c = 0x05F0; c <= 0x05F2; ++c) (*smap)[c] = U_Hebrew;
  for (char32 c = 0x05F3; c <= 0x05F4; ++c) (*smap)[c] = U_Hebrew;
  (*smap)[0xFB1D] = U_Hebrew;
  (*smap)[0xFB1E] = U_Hebrew;
  for (char32 c = 0xFB1F; c <= 0xFB28; ++c) (*smap)[c] = U_Hebrew;
  (*smap)[0xFB29] = U_Hebrew;
  for (char32 c = 0xFB2A; c <= 0xFB36; ++c) (*smap)[c] = U_Hebrew;
  for (char32 c = 0xFB38; c <= 0xFB3C; ++c) (*smap)[c] = U_Hebrew;
  (*smap)[0xFB3E] = U_Hebrew;
  for (char32 c = 0xFB40; c <= 0xFB41; ++c) (*smap)[c] = U_Hebrew;
  for (char32 c = 0xFB43; c <= 0xFB44; ++c) (*smap)[c] = U_Hebrew;
  for (char32 c = 0xFB46; c <= 0xFB4F; ++c) (*smap)[c] = U_Hebrew;
  for (char32 c = 0x0600; c <= 0x0604; ++c) (*smap)[c] = U_Arabic;
  for (char32 c = 0x0606; c <= 0x0608; ++c) (*smap)[c] = U_Arabic;
  for (char32 c = 0x0609; c <= 0x060A; ++c) (*smap)[c] = U_Arabic;
  (*smap)[0x060B] = U_Arabic;
  (*smap)[0x060D] = U_Arabic;
  for (char32 c = 0x060E; c <= 0x060F; ++c) (*smap)[c] = U_Arabic;
  for (char32 c = 0x0610; c <= 0x061A; ++c) (*smap)[c] = U_Arabic;
  (*smap)[0x061E] = U_Arabic;
  for (char32 c = 0x0620; c <= 0x063F; ++c) (*smap)[c] = U_Arabic;
  for (char32 c = 0x0641; c <= 0x064A; ++c) (*smap)[c] = U_Arabic;
  for (char32 c = 0x0656; c <= 0x065F; ++c) (*smap)[c] = U_Arabic;
  for (char32 c = 0x0660; c <= 0x0669; ++c) (*smap)[c] = U_Arabic;
  for (char32 c = 0x066A; c <= 0x066D; ++c) (*smap)[c] = U_Arabic;
  for (char32 c = 0x066E; c <= 0x066F; ++c) (*smap)[c] = U_Arabic;
  for (char32 c = 0x0671; c <= 0x06D3; ++c) (*smap)[c] = U_Arabic;
  (*smap)[0x06D4] = U_Arabic;
  (*smap)[0x06D5] = U_Arabic;
  for (char32 c = 0x06D6; c <= 0x06DC; ++c) (*smap)[c] = U_Arabic;
  (*smap)[0x06DE] = U_Arabic;
  for (char32 c = 0x06DF; c <= 0x06E4; ++c) (*smap)[c] = U_Arabic;
  for (char32 c = 0x06E5; c <= 0x06E6; ++c) (*smap)[c] = U_Arabic;
  for (char32 c = 0x06E7; c <= 0x06E8; ++c) (*smap)[c] = U_Arabic;
  (*smap)[0x06E9] = U_Arabic;
  for (char32 c = 0x06EA; c <= 0x06ED; ++c) (*smap)[c] = U_Arabic;
  for (char32 c = 0x06EE; c <= 0x06EF; ++c) (*smap)[c] = U_Arabic;
  for (char32 c = 0x06F0; c <= 0x06F9; ++c) (*smap)[c] = U_Arabic;
  for (char32 c = 0x06FA; c <= 0x06FC; ++c) (*smap)[c] = U_Arabic;
  for (char32 c = 0x06FD; c <= 0x06FE; ++c) (*smap)[c] = U_Arabic;
  (*smap)[0x06FF] = U_Arabic;
  for (char32 c = 0x0750; c <= 0x077F; ++c) (*smap)[c] = U_Arabic;
  for (char32 c = 0x08A0; c <= 0x08B4; ++c) (*smap)[c] = U_Arabic;
  for (char32 c = 0x08B6; c <= 0x08BD; ++c) (*smap)[c] = U_Arabic;
  for (char32 c = 0x08D4; c <= 0x08E1; ++c) (*smap)[c] = U_Arabic;
  for (char32 c = 0x08E3; c <= 0x08FF; ++c) (*smap)[c] = U_Arabic;
  for (char32 c = 0xFB50; c <= 0xFBB1; ++c) (*smap)[c] = U_Arabic;
  for (char32 c = 0xFBB2; c <= 0xFBC1; ++c) (*smap)[c] = U_Arabic;
  for (char32 c = 0xFBD3; c <= 0xFD3D; ++c) (*smap)[c] = U_Arabic;
  for (char32 c = 0xFD50; c <= 0xFD8F; ++c) (*smap)[c] = U_Arabic;
  for (char32 c = 0xFD92; c <= 0xFDC7; ++c) (*smap)[c] = U_Arabic;
  for (char32 c = 0xFDF0; c <= 0xFDFB; ++c) (*smap)[c] = U_Arabic;
  (*smap)[0xFDFC] = U_Arabic;
  (*smap)[0xFDFD] = U_Arabic;
  for (char32 c = 0xFE70; c <= 0xFE74; ++c) (*smap)[c] = U_Arabic;
  for (char32 c = 0xFE76; c <= 0xFEFC; ++c) (*smap)[c] = U_Arabic;
  for (char32 c = 0x10E60; c <= 0x10E7E; ++c) (*smap)[c] = U_Arabic;
  for (char32 c = 0x1EE00; c <= 0x1EE03; ++c) (*smap)[c] = U_Arabic;
  for (char32 c = 0x1EE05; c <= 0x1EE1F; ++c) (*smap)[c] = U_Arabic;
  for (char32 c = 0x1EE21; c <= 0x1EE22; ++c) (*smap)[c] = U_Arabic;
  (*smap)[0x1EE24] = U_Arabic;
  (*smap)[0x1EE27] = U_Arabic;
  for (char32 c = 0x1EE29; c <= 0x1EE32; ++c) (*smap)[c] = U_Arabic;
  for (char32 c = 0x1EE34; c <= 0x1EE37; ++c) (*smap)[c] = U_Arabic;
  (*smap)[0x1EE39] = U_Arabic;
  (*smap)[0x1EE3B] = U_Arabic;
  (*smap)[0x1EE42] = U_Arabic;
  (*smap)[0x1EE47] = U_Arabic;
  (*smap)[0x1EE49] = U_Arabic;
  (*smap)[0x1EE4B] = U_Arabic;
  for (char32 c = 0x1EE4D; c <= 0x1EE4F; ++c) (*smap)[c] = U_Arabic;
  for (char32 c = 0x1EE51; c <= 0x1EE52; ++c) (*smap)[c] = U_Arabic;
  (*smap)[0x1EE54] = U_Arabic;
  (*smap)[0x1EE57] = U_Arabic;
  (*smap)[0x1EE59] = U_Arabic;
  (*smap)[0x1EE5B] = U_Arabic;
  (*smap)[0x1EE5D] = U_Arabic;
  (*smap)[0x1EE5F] = U_Arabic;
  for (char32 c = 0x1EE61; c <= 0x1EE62; ++c) (*smap)[c] = U_Arabic;
  (*smap)[0x1EE64] = U_Arabic;
  for (char32 c = 0x1EE67; c <= 0x1EE6A; ++c) (*smap)[c] = U_Arabic;
  for (char32 c = 0x1EE6C; c <= 0x1EE72; ++c) (*smap)[c] = U_Arabic;
  for (char32 c = 0x1EE74; c <= 0x1EE77; ++c) (*smap)[c] = U_Arabic;
  for (char32 c = 0x1EE79; c <= 0x1EE7C; ++c) (*smap)[c] = U_Arabic;
  (*smap)[0x1EE7E] = U_Arabic;
  for (char32 c = 0x1EE80; c <= 0x1EE89; ++c) (*smap)[c] = U_Arabic;
  for (char32 c = 0x1EE8B; c <= 0x1EE9B; ++c) (*smap)[c] = U_Arabic;
  for (char32 c = 0x1EEA1; c <= 0x1EEA3; ++c) (*smap)[c] = U_Arabic;
  for (char32 c = 0x1EEA5; c <= 0x1EEA9; ++c) (*smap)[c] = U_Arabic;
  for (char32 c = 0x1EEAB; c <= 0x1EEBB; ++c) (*smap)[c] = U_Arabic;
  for (char32 c = 0x1EEF0; c <= 0x1EEF1; ++c) (*smap)[c] = U_Arabic;
  for (char32 c = 0x0700; c <= 0x070D; ++c) (*smap)[c] = U_Syriac;
  (*smap)[0x070F] = U_Syriac;
  (*smap)[0x0710] = U_Syriac;
  (*smap)[0x0711] = U_Syriac;
  for (char32 c = 0x0712; c <= 0x072F; ++c) (*smap)[c] = U_Syriac;
  for (char32 c = 0x0730; c <= 0x074A; ++c) (*smap)[c] = U_Syriac;
  for (char32 c = 0x074D; c <= 0x074F; ++c) (*smap)[c] = U_Syriac;
  for (char32 c = 0x0780; c <= 0x07A5; ++c) (*smap)[c] = U_Thaana;
  for (char32 c = 0x07A6; c <= 0x07B0; ++c) (*smap)[c] = U_Thaana;
  (*smap)[0x07B1] = U_Thaana;
  for (char32 c = 0x0900; c <= 0x0902; ++c) (*smap)[c] = U_Devanagari;
  (*smap)[0x0903] = U_Devanagari;
  for (char32 c = 0x0904; c <= 0x0939; ++c) (*smap)[c] = U_Devanagari;
  (*smap)[0x093A] = U_Devanagari;
  (*smap)[0x093B] = U_Devanagari;
  (*smap)[0x093C] = U_Devanagari;
  (*smap)[0x093D] = U_Devanagari;
  for (char32 c = 0x093E; c <= 0x0940; ++c) (*smap)[c] = U_Devanagari;
  for (char32 c = 0x0941; c <= 0x0948; ++c) (*smap)[c] = U_Devanagari;
  for (char32 c = 0x0949; c <= 0x094C; ++c) (*smap)[c] = U_Devanagari;
  (*smap)[0x094D] = U_Devanagari;
  for (char32 c = 0x094E; c <= 0x094F; ++c) (*smap)[c] = U_Devanagari;
  (*smap)[0x0950] = U_Devanagari;
  for (char32 c = 0x0953; c <= 0x0957; ++c) (*smap)[c] = U_Devanagari;
  for (char32 c = 0x0958; c <= 0x0961; ++c) (*smap)[c] = U_Devanagari;
  for (char32 c = 0x0962; c <= 0x0963; ++c) (*smap)[c] = U_Devanagari;
  for (char32 c = 0x0966; c <= 0x096F; ++c) (*smap)[c] = U_Devanagari;
  (*smap)[0x0970] = U_Devanagari;
  (*smap)[0x0971] = U_Devanagari;
  for (char32 c = 0x0972; c <= 0x097F; ++c) (*smap)[c] = U_Devanagari;
  for (char32 c = 0xA8E0; c <= 0xA8F1; ++c) (*smap)[c] = U_Devanagari;
  for (char32 c = 0xA8F2; c <= 0xA8F7; ++c) (*smap)[c] = U_Devanagari;
  for (char32 c = 0xA8F8; c <= 0xA8FA; ++c) (*smap)[c] = U_Devanagari;
  (*smap)[0xA8FB] = U_Devanagari;
  (*smap)[0xA8FC] = U_Devanagari;
  (*smap)[0xA8FD] = U_Devanagari;
  (*smap)[0x0980] = U_Bengali;
  (*smap)[0x0981] = U_Bengali;
  for (char32 c = 0x0982; c <= 0x0983; ++c) (*smap)[c] = U_Bengali;
  for (char32 c = 0x0985; c <= 0x098C; ++c) (*smap)[c] = U_Bengali;
  for (char32 c = 0x098F; c <= 0x0990; ++c) (*smap)[c] = U_Bengali;
  for (char32 c = 0x0993; c <= 0x09A8; ++c) (*smap)[c] = U_Bengali;
  for (char32 c = 0x09AA; c <= 0x09B0; ++c) (*smap)[c] = U_Bengali;
  (*smap)[0x09B2] = U_Bengali;
  for (char32 c = 0x09B6; c <= 0x09B9; ++c) (*smap)[c] = U_Bengali;
  (*smap)[0x09BC] = U_Bengali;
  (*smap)[0x09BD] = U_Bengali;
  for (char32 c = 0x09BE; c <= 0x09C0; ++c) (*smap)[c] = U_Bengali;
  for (char32 c = 0x09C1; c <= 0x09C4; ++c) (*smap)[c] = U_Bengali;
  for (char32 c = 0x09C7; c <= 0x09C8; ++c) (*smap)[c] = U_Bengali;
  for (char32 c = 0x09CB; c <= 0x09CC; ++c) (*smap)[c] = U_Bengali;
  (*smap)[0x09CD] = U_Bengali;
  (*smap)[0x09CE] = U_Bengali;
  (*smap)[0x09D7] = U_Bengali;
  for (char32 c = 0x09DC; c <= 0x09DD; ++c) (*smap)[c] = U_Bengali;
  for (char32 c = 0x09DF; c <= 0x09E1; ++c) (*smap)[c] = U_Bengali;
  for (char32 c = 0x09E2; c <= 0x09E3; ++c) (*smap)[c] = U_Bengali;
  for (char32 c = 0x09E6; c <= 0x09EF; ++c) (*smap)[c] = U_Bengali;
  for (char32 c = 0x09F0; c <= 0x09F1; ++c) (*smap)[c] = U_Bengali;
  for (char32 c = 0x09F2; c <= 0x09F3; ++c) (*smap)[c] = U_Bengali;
  for (char32 c = 0x09F4; c <= 0x09F9; ++c) (*smap)[c] = U_Bengali;
  (*smap)[0x09FA] = U_Bengali;
  (*smap)[0x09FB] = U_Bengali;
  for (char32 c = 0x0A01; c <= 0x0A02; ++c) (*smap)[c] = U_Gurmukhi;
  (*smap)[0x0A03] = U_Gurmukhi;
  for (char32 c = 0x0A05; c <= 0x0A0A; ++c) (*smap)[c] = U_Gurmukhi;
  for (char32 c = 0x0A0F; c <= 0x0A10; ++c) (*smap)[c] = U_Gurmukhi;
  for (char32 c = 0x0A13; c <= 0x0A28; ++c) (*smap)[c] = U_Gurmukhi;
  for (char32 c = 0x0A2A; c <= 0x0A30; ++c) (*smap)[c] = U_Gurmukhi;
  for (char32 c = 0x0A32; c <= 0x0A33; ++c) (*smap)[c] = U_Gurmukhi;
  for (char32 c = 0x0A35; c <= 0x0A36; ++c) (*smap)[c] = U_Gurmukhi;
  for (char32 c = 0x0A38; c <= 0x0A39; ++c) (*smap)[c] = U_Gurmukhi;
  (*smap)[0x0A3C] = U_Gurmukhi;
  for (char32 c = 0x0A3E; c <= 0x0A40; ++c) (*smap)[c] = U_Gurmukhi;
  for (char32 c = 0x0A41; c <= 0x0A42; ++c) (*smap)[c] = U_Gurmukhi;
  for (char32 c = 0x0A47; c <= 0x0A48; ++c) (*smap)[c] = U_Gurmukhi;
  for (char32 c = 0x0A4B; c <= 0x0A4D; ++c) (*smap)[c] = U_Gurmukhi;
  (*smap)[0x0A51] = U_Gurmukhi;
  for (char32 c = 0x0A59; c <= 0x0A5C; ++c) (*smap)[c] = U_Gurmukhi;
  (*smap)[0x0A5E] = U_Gurmukhi;
  for (char32 c = 0x0A66; c <= 0x0A6F; ++c) (*smap)[c] = U_Gurmukhi;
  for (char32 c = 0x0A70; c <= 0x0A71; ++c) (*smap)[c] = U_Gurmukhi;
  for (char32 c = 0x0A72; c <= 0x0A74; ++c) (*smap)[c] = U_Gurmukhi;
  (*smap)[0x0A75] = U_Gurmukhi;
  for (char32 c = 0x0A81; c <= 0x0A82; ++c) (*smap)[c] = U_Gujarati;
  (*smap)[0x0A83] = U_Gujarati;
  for (char32 c = 0x0A85; c <= 0x0A8D; ++c) (*smap)[c] = U_Gujarati;
  for (char32 c = 0x0A8F; c <= 0x0A91; ++c) (*smap)[c] = U_Gujarati;
  for (char32 c = 0x0A93; c <= 0x0AA8; ++c) (*smap)[c] = U_Gujarati;
  for (char32 c = 0x0AAA; c <= 0x0AB0; ++c) (*smap)[c] = U_Gujarati;
  for (char32 c = 0x0AB2; c <= 0x0AB3; ++c) (*smap)[c] = U_Gujarati;
  for (char32 c = 0x0AB5; c <= 0x0AB9; ++c) (*smap)[c] = U_Gujarati;
  (*smap)[0x0ABC] = U_Gujarati;
  (*smap)[0x0ABD] = U_Gujarati;
  for (char32 c = 0x0ABE; c <= 0x0AC0; ++c) (*smap)[c] = U_Gujarati;
  for (char32 c = 0x0AC1; c <= 0x0AC5; ++c) (*smap)[c] = U_Gujarati;
  for (char32 c = 0x0AC7; c <= 0x0AC8; ++c) (*smap)[c] = U_Gujarati;
  (*smap)[0x0AC9] = U_Gujarati;
  for (char32 c = 0x0ACB; c <= 0x0ACC; ++c) (*smap)[c] = U_Gujarati;
  (*smap)[0x0ACD] = U_Gujarati;
  (*smap)[0x0AD0] = U_Gujarati;
  for (char32 c = 0x0AE0; c <= 0x0AE1; ++c) (*smap)[c] = U_Gujarati;
  for (char32 c = 0x0AE2; c <= 0x0AE3; ++c) (*smap)[c] = U_Gujarati;
  for (char32 c = 0x0AE6; c <= 0x0AEF; ++c) (*smap)[c] = U_Gujarati;
  (*smap)[0x0AF0] = U_Gujarati;
  (*smap)[0x0AF1] = U_Gujarati;
  (*smap)[0x0AF9] = U_Gujarati;
  (*smap)[0x0B01] = U_Oriya;
  for (char32 c = 0x0B02; c <= 0x0B03; ++c) (*smap)[c] = U_Oriya;
  for (char32 c = 0x0B05; c <= 0x0B0C; ++c) (*smap)[c] = U_Oriya;
  for (char32 c = 0x0B0F; c <= 0x0B10; ++c) (*smap)[c] = U_Oriya;
  for (char32 c = 0x0B13; c <= 0x0B28; ++c) (*smap)[c] = U_Oriya;
  for (char32 c = 0x0B2A; c <= 0x0B30; ++c) (*smap)[c] = U_Oriya;
  for (char32 c = 0x0B32; c <= 0x0B33; ++c) (*smap)[c] = U_Oriya;
  for (char32 c = 0x0B35; c <= 0x0B39; ++c) (*smap)[c] = U_Oriya;
  (*smap)[0x0B3C] = U_Oriya;
  (*smap)[0x0B3D] = U_Oriya;
  (*smap)[0x0B3E] = U_Oriya;
  (*smap)[0x0B3F] = U_Oriya;
  (*smap)[0x0B40] = U_Oriya;
  for (char32 c = 0x0B41; c <= 0x0B44; ++c) (*smap)[c] = U_Oriya;
  for (char32 c = 0x0B47; c <= 0x0B48; ++c) (*smap)[c] = U_Oriya;
  for (char32 c = 0x0B4B; c <= 0x0B4C; ++c) (*smap)[c] = U_Oriya;
  (*smap)[0x0B4D] = U_Oriya;
  (*smap)[0x0B56] = U_Oriya;
  (*smap)[0x0B57] = U_Oriya;
  for (char32 c = 0x0B5C; c <= 0x0B5D; ++c) (*smap)[c] = U_Oriya;
  for (char32 c = 0x0B5F; c <= 0x0B61; ++c) (*smap)[c] = U_Oriya;
  for (char32 c = 0x0B62; c <= 0x0B63; ++c) (*smap)[c] = U_Oriya;
  for (char32 c = 0x0B66; c <= 0x0B6F; ++c) (*smap)[c] = U_Oriya;
  (*smap)[0x0B70] = U_Oriya;
  (*smap)[0x0B71] = U_Oriya;
  for (char32 c = 0x0B72; c <= 0x0B77; ++c) (*smap)[c] = U_Oriya;
  (*smap)[0x0B82] = U_Tamil;
  (*smap)[0x0B83] = U_Tamil;
  for (char32 c = 0x0B85; c <= 0x0B8A; ++c) (*smap)[c] = U_Tamil;
  for (char32 c = 0x0B8E; c <= 0x0B90; ++c) (*smap)[c] = U_Tamil;
  for (char32 c = 0x0B92; c <= 0x0B95; ++c) (*smap)[c] = U_Tamil;
  for (char32 c = 0x0B99; c <= 0x0B9A; ++c) (*smap)[c] = U_Tamil;
  (*smap)[0x0B9C] = U_Tamil;
  for (char32 c = 0x0B9E; c <= 0x0B9F; ++c) (*smap)[c] = U_Tamil;
  for (char32 c = 0x0BA3; c <= 0x0BA4; ++c) (*smap)[c] = U_Tamil;
  for (char32 c = 0x0BA8; c <= 0x0BAA; ++c) (*smap)[c] = U_Tamil;
  for (char32 c = 0x0BAE; c <= 0x0BB9; ++c) (*smap)[c] = U_Tamil;
  for (char32 c = 0x0BBE; c <= 0x0BBF; ++c) (*smap)[c] = U_Tamil;
  (*smap)[0x0BC0] = U_Tamil;
  for (char32 c = 0x0BC1; c <= 0x0BC2; ++c) (*smap)[c] = U_Tamil;
  for (char32 c = 0x0BC6; c <= 0x0BC8; ++c) (*smap)[c] = U_Tamil;
  for (char32 c = 0x0BCA; c <= 0x0BCC; ++c) (*smap)[c] = U_Tamil;
  (*smap)[0x0BCD] = U_Tamil;
  (*smap)[0x0BD0] = U_Tamil;
  (*smap)[0x0BD7] = U_Tamil;
  for (char32 c = 0x0BE6; c <= 0x0BEF; ++c) (*smap)[c] = U_Tamil;
  for (char32 c = 0x0BF0; c <= 0x0BF2; ++c) (*smap)[c] = U_Tamil;
  for (char32 c = 0x0BF3; c <= 0x0BF8; ++c) (*smap)[c] = U_Tamil;
  (*smap)[0x0BF9] = U_Tamil;
  (*smap)[0x0BFA] = U_Tamil;
  (*smap)[0x0C00] = U_Telugu;
  for (char32 c = 0x0C01; c <= 0x0C03; ++c) (*smap)[c] = U_Telugu;
  for (char32 c = 0x0C05; c <= 0x0C0C; ++c) (*smap)[c] = U_Telugu;
  for (char32 c = 0x0C0E; c <= 0x0C10; ++c) (*smap)[c] = U_Telugu;
  for (char32 c = 0x0C12; c <= 0x0C28; ++c) (*smap)[c] = U_Telugu;
  for (char32 c = 0x0C2A; c <= 0x0C39; ++c) (*smap)[c] = U_Telugu;
  (*smap)[0x0C3D] = U_Telugu;
  for (char32 c = 0x0C3E; c <= 0x0C40; ++c) (*smap)[c] = U_Telugu;
  for (char32 c = 0x0C41; c <= 0x0C44; ++c) (*smap)[c] = U_Telugu;
  for (char32 c = 0x0C46; c <= 0x0C48; ++c) (*smap)[c] = U_Telugu;
  for (char32 c = 0x0C4A; c <= 0x0C4D; ++c) (*smap)[c] = U_Telugu;
  for (char32 c = 0x0C55; c <= 0x0C56; ++c) (*smap)[c] = U_Telugu;
  for (char32 c = 0x0C58; c <= 0x0C5A; ++c) (*smap)[c] = U_Telugu;
  for (char32 c = 0x0C60; c <= 0x0C61; ++c) (*smap)[c] = U_Telugu;
  for (char32 c = 0x0C62; c <= 0x0C63; ++c) (*smap)[c] = U_Telugu;
  for (char32 c = 0x0C66; c <= 0x0C6F; ++c) (*smap)[c] = U_Telugu;
  for (char32 c = 0x0C78; c <= 0x0C7E; ++c) (*smap)[c] = U_Telugu;
  (*smap)[0x0C7F] = U_Telugu;
  (*smap)[0x0C80] = U_Kannada;
  (*smap)[0x0C81] = U_Kannada;
  for (char32 c = 0x0C82; c <= 0x0C83; ++c) (*smap)[c] = U_Kannada;
  for (char32 c = 0x0C85; c <= 0x0C8C; ++c) (*smap)[c] = U_Kannada;
  for (char32 c = 0x0C8E; c <= 0x0C90; ++c) (*smap)[c] = U_Kannada;
  for (char32 c = 0x0C92; c <= 0x0CA8; ++c) (*smap)[c] = U_Kannada;
  for (char32 c = 0x0CAA; c <= 0x0CB3; ++c) (*smap)[c] = U_Kannada;
  for (char32 c = 0x0CB5; c <= 0x0CB9; ++c) (*smap)[c] = U_Kannada;
  (*smap)[0x0CBC] = U_Kannada;
  (*smap)[0x0CBD] = U_Kannada;
  (*smap)[0x0CBE] = U_Kannada;
  (*smap)[0x0CBF] = U_Kannada;
  for (char32 c = 0x0CC0; c <= 0x0CC4; ++c) (*smap)[c] = U_Kannada;
  (*smap)[0x0CC6] = U_Kannada;
  for (char32 c = 0x0CC7; c <= 0x0CC8; ++c) (*smap)[c] = U_Kannada;
  for (char32 c = 0x0CCA; c <= 0x0CCB; ++c) (*smap)[c] = U_Kannada;
  for (char32 c = 0x0CCC; c <= 0x0CCD; ++c) (*smap)[c] = U_Kannada;
  for (char32 c = 0x0CD5; c <= 0x0CD6; ++c) (*smap)[c] = U_Kannada;
  (*smap)[0x0CDE] = U_Kannada;
  for (char32 c = 0x0CE0; c <= 0x0CE1; ++c) (*smap)[c] = U_Kannada;
  for (char32 c = 0x0CE2; c <= 0x0CE3; ++c) (*smap)[c] = U_Kannada;
  for (char32 c = 0x0CE6; c <= 0x0CEF; ++c) (*smap)[c] = U_Kannada;
  for (char32 c = 0x0CF1; c <= 0x0CF2; ++c) (*smap)[c] = U_Kannada;
  (*smap)[0x0D01] = U_Malayalam;
  for (char32 c = 0x0D02; c <= 0x0D03; ++c) (*smap)[c] = U_Malayalam;
  for (char32 c = 0x0D05; c <= 0x0D0C; ++c) (*smap)[c] = U_Malayalam;
  for (char32 c = 0x0D0E; c <= 0x0D10; ++c) (*smap)[c] = U_Malayalam;
  for (char32 c = 0x0D12; c <= 0x0D3A; ++c) (*smap)[c] = U_Malayalam;
  (*smap)[0x0D3D] = U_Malayalam;
  for (char32 c = 0x0D3E; c <= 0x0D40; ++c) (*smap)[c] = U_Malayalam;
  for (char32 c = 0x0D41; c <= 0x0D44; ++c) (*smap)[c] = U_Malayalam;
  for (char32 c = 0x0D46; c <= 0x0D48; ++c) (*smap)[c] = U_Malayalam;
  for (char32 c = 0x0D4A; c <= 0x0D4C; ++c) (*smap)[c] = U_Malayalam;
  (*smap)[0x0D4D] = U_Malayalam;
  (*smap)[0x0D4E] = U_Malayalam;
  (*smap)[0x0D4F] = U_Malayalam;
  for (char32 c = 0x0D54; c <= 0x0D56; ++c) (*smap)[c] = U_Malayalam;
  (*smap)[0x0D57] = U_Malayalam;
  for (char32 c = 0x0D58; c <= 0x0D5E; ++c) (*smap)[c] = U_Malayalam;
  for (char32 c = 0x0D5F; c <= 0x0D61; ++c) (*smap)[c] = U_Malayalam;
  for (char32 c = 0x0D62; c <= 0x0D63; ++c) (*smap)[c] = U_Malayalam;
  for (char32 c = 0x0D66; c <= 0x0D6F; ++c) (*smap)[c] = U_Malayalam;
  for (char32 c = 0x0D70; c <= 0x0D78; ++c) (*smap)[c] = U_Malayalam;
  (*smap)[0x0D79] = U_Malayalam;
  for (char32 c = 0x0D7A; c <= 0x0D7F; ++c) (*smap)[c] = U_Malayalam;
  for (char32 c = 0x0D82; c <= 0x0D83; ++c) (*smap)[c] = U_Sinhala;
  for (char32 c = 0x0D85; c <= 0x0D96; ++c) (*smap)[c] = U_Sinhala;
  for (char32 c = 0x0D9A; c <= 0x0DB1; ++c) (*smap)[c] = U_Sinhala;
  for (char32 c = 0x0DB3; c <= 0x0DBB; ++c) (*smap)[c] = U_Sinhala;
  (*smap)[0x0DBD] = U_Sinhala;
  for (char32 c = 0x0DC0; c <= 0x0DC6; ++c) (*smap)[c] = U_Sinhala;
  (*smap)[0x0DCA] = U_Sinhala;
  for (char32 c = 0x0DCF; c <= 0x0DD1; ++c) (*smap)[c] = U_Sinhala;
  for (char32 c = 0x0DD2; c <= 0x0DD4; ++c) (*smap)[c] = U_Sinhala;
  (*smap)[0x0DD6] = U_Sinhala;
  for (char32 c = 0x0DD8; c <= 0x0DDF; ++c) (*smap)[c] = U_Sinhala;
  for (char32 c = 0x0DE6; c <= 0x0DEF; ++c) (*smap)[c] = U_Sinhala;
  for (char32 c = 0x0DF2; c <= 0x0DF3; ++c) (*smap)[c] = U_Sinhala;
  (*smap)[0x0DF4] = U_Sinhala;
  for (char32 c = 0x111E1; c <= 0x111F4; ++c) (*smap)[c] = U_Sinhala;
  for (char32 c = 0x0E01; c <= 0x0E30; ++c) (*smap)[c] = U_Thai;
  (*smap)[0x0E31] = U_Thai;
  for (char32 c = 0x0E32; c <= 0x0E33; ++c) (*smap)[c] = U_Thai;
  for (char32 c = 0x0E34; c <= 0x0E3A; ++c) (*smap)[c] = U_Thai;
  for (char32 c = 0x0E40; c <= 0x0E45; ++c) (*smap)[c] = U_Thai;
  (*smap)[0x0E46] = U_Thai;
  for (char32 c = 0x0E47; c <= 0x0E4E; ++c) (*smap)[c] = U_Thai;
  (*smap)[0x0E4F] = U_Thai;
  for (char32 c = 0x0E50; c <= 0x0E59; ++c) (*smap)[c] = U_Thai;
  for (char32 c = 0x0E5A; c <= 0x0E5B; ++c) (*smap)[c] = U_Thai;
  for (char32 c = 0x0E81; c <= 0x0E82; ++c) (*smap)[c] = U_Lao;
  (*smap)[0x0E84] = U_Lao;
  for (char32 c = 0x0E87; c <= 0x0E88; ++c) (*smap)[c] = U_Lao;
  (*smap)[0x0E8A] = U_Lao;
  (*smap)[0x0E8D] = U_Lao;
  for (char32 c = 0x0E94; c <= 0x0E97; ++c) (*smap)[c] = U_Lao;
  for (char32 c = 0x0E99; c <= 0x0E9F; ++c) (*smap)[c] = U_Lao;
  for (char32 c = 0x0EA1; c <= 0x0EA3; ++c) (*smap)[c] = U_Lao;
  (*smap)[0x0EA5] = U_Lao;
  (*smap)[0x0EA7] = U_Lao;
  for (char32 c = 0x0EAA; c <= 0x0EAB; ++c) (*smap)[c] = U_Lao;
  for (char32 c = 0x0EAD; c <= 0x0EB0; ++c) (*smap)[c] = U_Lao;
  (*smap)[0x0EB1] = U_Lao;
  for (char32 c = 0x0EB2; c <= 0x0EB3; ++c) (*smap)[c] = U_Lao;
  for (char32 c = 0x0EB4; c <= 0x0EB9; ++c) (*smap)[c] = U_Lao;
  for (char32 c = 0x0EBB; c <= 0x0EBC; ++c) (*smap)[c] = U_Lao;
  (*smap)[0x0EBD] = U_Lao;
  for (char32 c = 0x0EC0; c <= 0x0EC4; ++c) (*smap)[c] = U_Lao;
  (*smap)[0x0EC6] = U_Lao;
  for (char32 c = 0x0EC8; c <= 0x0ECD; ++c) (*smap)[c] = U_Lao;
  for (char32 c = 0x0ED0; c <= 0x0ED9; ++c) (*smap)[c] = U_Lao;
  for (char32 c = 0x0EDC; c <= 0x0EDF; ++c) (*smap)[c] = U_Lao;
  (*smap)[0x0F00] = U_Tibetan;
  for (char32 c = 0x0F01; c <= 0x0F03; ++c) (*smap)[c] = U_Tibetan;
  for (char32 c = 0x0F04; c <= 0x0F12; ++c) (*smap)[c] = U_Tibetan;
  (*smap)[0x0F13] = U_Tibetan;
  (*smap)[0x0F14] = U_Tibetan;
  for (char32 c = 0x0F15; c <= 0x0F17; ++c) (*smap)[c] = U_Tibetan;
  for (char32 c = 0x0F18; c <= 0x0F19; ++c) (*smap)[c] = U_Tibetan;
  for (char32 c = 0x0F1A; c <= 0x0F1F; ++c) (*smap)[c] = U_Tibetan;
  for (char32 c = 0x0F20; c <= 0x0F29; ++c) (*smap)[c] = U_Tibetan;
  for (char32 c = 0x0F2A; c <= 0x0F33; ++c) (*smap)[c] = U_Tibetan;
  (*smap)[0x0F34] = U_Tibetan;
  (*smap)[0x0F35] = U_Tibetan;
  (*smap)[0x0F36] = U_Tibetan;
  (*smap)[0x0F37] = U_Tibetan;
  (*smap)[0x0F38] = U_Tibetan;
  (*smap)[0x0F39] = U_Tibetan;
  (*smap)[0x0F3A] = U_Tibetan;
  (*smap)[0x0F3B] = U_Tibetan;
  (*smap)[0x0F3C] = U_Tibetan;
  (*smap)[0x0F3D] = U_Tibetan;
  for (char32 c = 0x0F3E; c <= 0x0F3F; ++c) (*smap)[c] = U_Tibetan;
  for (char32 c = 0x0F40; c <= 0x0F47; ++c) (*smap)[c] = U_Tibetan;
  for (char32 c = 0x0F49; c <= 0x0F6C; ++c) (*smap)[c] = U_Tibetan;
  for (char32 c = 0x0F71; c <= 0x0F7E; ++c) (*smap)[c] = U_Tibetan;
  (*smap)[0x0F7F] = U_Tibetan;
  for (char32 c = 0x0F80; c <= 0x0F84; ++c) (*smap)[c] = U_Tibetan;
  (*smap)[0x0F85] = U_Tibetan;
  for (char32 c = 0x0F86; c <= 0x0F87; ++c) (*smap)[c] = U_Tibetan;
  for (char32 c = 0x0F88; c <= 0x0F8C; ++c) (*smap)[c] = U_Tibetan;
  for (char32 c = 0x0F8D; c <= 0x0F97; ++c) (*smap)[c] = U_Tibetan;
  for (char32 c = 0x0F99; c <= 0x0FBC; ++c) (*smap)[c] = U_Tibetan;
  for (char32 c = 0x0FBE; c <= 0x0FC5; ++c) (*smap)[c] = U_Tibetan;
  (*smap)[0x0FC6] = U_Tibetan;
  for (char32 c = 0x0FC7; c <= 0x0FCC; ++c) (*smap)[c] = U_Tibetan;
  for (char32 c = 0x0FCE; c <= 0x0FCF; ++c) (*smap)[c] = U_Tibetan;
  for (char32 c = 0x0FD0; c <= 0x0FD4; ++c) (*smap)[c] = U_Tibetan;
  for (char32 c = 0x0FD9; c <= 0x0FDA; ++c) (*smap)[c] = U_Tibetan;
  for (char32 c = 0x1000; c <= 0x102A; ++c) (*smap)[c] = U_Myanmar;
  for (char32 c = 0x102B; c <= 0x102C; ++c) (*smap)[c] = U_Myanmar;
  for (char32 c = 0x102D; c <= 0x1030; ++c) (*smap)[c] = U_Myanmar;
  (*smap)[0x1031] = U_Myanmar;
  for (char32 c = 0x1032; c <= 0x1037; ++c) (*smap)[c] = U_Myanmar;
  (*smap)[0x1038] = U_Myanmar;
  for (char32 c = 0x1039; c <= 0x103A; ++c) (*smap)[c] = U_Myanmar;
  for (char32 c = 0x103B; c <= 0x103C; ++c) (*smap)[c] = U_Myanmar;
  for (char32 c = 0x103D; c <= 0x103E; ++c) (*smap)[c] = U_Myanmar;
  (*smap)[0x103F] = U_Myanmar;
  for (char32 c = 0x1040; c <= 0x1049; ++c) (*smap)[c] = U_Myanmar;
  for (char32 c = 0x104A; c <= 0x104F; ++c) (*smap)[c] = U_Myanmar;
  for (char32 c = 0x1050; c <= 0x1055; ++c) (*smap)[c] = U_Myanmar;
  for (char32 c = 0x1056; c <= 0x1057; ++c) (*smap)[c] = U_Myanmar;
  for (char32 c = 0x1058; c <= 0x1059; ++c) (*smap)[c] = U_Myanmar;
  for (char32 c = 0x105A; c <= 0x105D; ++c) (*smap)[c] = U_Myanmar;
  for (char32 c = 0x105E; c <= 0x1060; ++c) (*smap)[c] = U_Myanmar;
  (*smap)[0x1061] = U_Myanmar;
  for (char32 c = 0x1062; c <= 0x1064; ++c) (*smap)[c] = U_Myanmar;
  for (char32 c = 0x1065; c <= 0x1066; ++c) (*smap)[c] = U_Myanmar;
  for (char32 c = 0x1067; c <= 0x106D; ++c) (*smap)[c] = U_Myanmar;
  for (char32 c = 0x106E; c <= 0x1070; ++c) (*smap)[c] = U_Myanmar;
  for (char32 c = 0x1071; c <= 0x1074; ++c) (*smap)[c] = U_Myanmar;
  for (char32 c = 0x1075; c <= 0x1081; ++c) (*smap)[c] = U_Myanmar;
  (*smap)[0x1082] = U_Myanmar;
  for (char32 c = 0x1083; c <= 0x1084; ++c) (*smap)[c] = U_Myanmar;
  for (char32 c = 0x1085; c <= 0x1086; ++c) (*smap)[c] = U_Myanmar;
  for (char32 c = 0x1087; c <= 0x108C; ++c) (*smap)[c] = U_Myanmar;
  (*smap)[0x108D] = U_Myanmar;
  (*smap)[0x108E] = U_Myanmar;
  (*smap)[0x108F] = U_Myanmar;
  for (char32 c = 0x1090; c <= 0x1099; ++c) (*smap)[c] = U_Myanmar;
  for (char32 c = 0x109A; c <= 0x109C; ++c) (*smap)[c] = U_Myanmar;
  (*smap)[0x109D] = U_Myanmar;
  for (char32 c = 0x109E; c <= 0x109F; ++c) (*smap)[c] = U_Myanmar;
  for (char32 c = 0xA9E0; c <= 0xA9E4; ++c) (*smap)[c] = U_Myanmar;
  (*smap)[0xA9E5] = U_Myanmar;
  (*smap)[0xA9E6] = U_Myanmar;
  for (char32 c = 0xA9E7; c <= 0xA9EF; ++c) (*smap)[c] = U_Myanmar;
  for (char32 c = 0xA9F0; c <= 0xA9F9; ++c) (*smap)[c] = U_Myanmar;
  for (char32 c = 0xA9FA; c <= 0xA9FE; ++c) (*smap)[c] = U_Myanmar;
  for (char32 c = 0xAA60; c <= 0xAA6F; ++c) (*smap)[c] = U_Myanmar;
  (*smap)[0xAA70] = U_Myanmar;
  for (char32 c = 0xAA71; c <= 0xAA76; ++c) (*smap)[c] = U_Myanmar;
  for (char32 c = 0xAA77; c <= 0xAA79; ++c) (*smap)[c] = U_Myanmar;
  (*smap)[0xAA7A] = U_Myanmar;
  (*smap)[0xAA7B] = U_Myanmar;
  (*smap)[0xAA7C] = U_Myanmar;
  (*smap)[0xAA7D] = U_Myanmar;
  for (char32 c = 0xAA7E; c <= 0xAA7F; ++c) (*smap)[c] = U_Myanmar;
  for (char32 c = 0x10A0; c <= 0x10C5; ++c) (*smap)[c] = U_Georgian;
  (*smap)[0x10C7] = U_Georgian;
  (*smap)[0x10CD] = U_Georgian;
  for (char32 c = 0x10D0; c <= 0x10FA; ++c) (*smap)[c] = U_Georgian;
  (*smap)[0x10FC] = U_Georgian;
  for (char32 c = 0x10FD; c <= 0x10FF; ++c) (*smap)[c] = U_Georgian;
  for (char32 c = 0x2D00; c <= 0x2D25; ++c) (*smap)[c] = U_Georgian;
  (*smap)[0x2D27] = U_Georgian;
  (*smap)[0x2D2D] = U_Georgian;
  for (char32 c = 0x1100; c <= 0x11FF; ++c) (*smap)[c] = U_Hangul;
  for (char32 c = 0x302E; c <= 0x302F; ++c) (*smap)[c] = U_Hangul;
  for (char32 c = 0x3131; c <= 0x318E; ++c) (*smap)[c] = U_Hangul;
  for (char32 c = 0x3200; c <= 0x321E; ++c) (*smap)[c] = U_Hangul;
  for (char32 c = 0x3260; c <= 0x327E; ++c) (*smap)[c] = U_Hangul;
  for (char32 c = 0xA960; c <= 0xA97C; ++c) (*smap)[c] = U_Hangul;
  for (char32 c = 0xAC00; c <= 0xD7A3; ++c) (*smap)[c] = U_Hangul;
  for (char32 c = 0xD7B0; c <= 0xD7C6; ++c) (*smap)[c] = U_Hangul;
  for (char32 c = 0xD7CB; c <= 0xD7FB; ++c) (*smap)[c] = U_Hangul;
  for (char32 c = 0xFFA0; c <= 0xFFBE; ++c) (*smap)[c] = U_Hangul;
  for (char32 c = 0xFFC2; c <= 0xFFC7; ++c) (*smap)[c] = U_Hangul;
  for (char32 c = 0xFFCA; c <= 0xFFCF; ++c) (*smap)[c] = U_Hangul;
  for (char32 c = 0xFFD2; c <= 0xFFD7; ++c) (*smap)[c] = U_Hangul;
  for (char32 c = 0xFFDA; c <= 0xFFDC; ++c) (*smap)[c] = U_Hangul;
  for (char32 c = 0x1200; c <= 0x1248; ++c) (*smap)[c] = U_Ethiopic;
  for (char32 c = 0x124A; c <= 0x124D; ++c) (*smap)[c] = U_Ethiopic;
  for (char32 c = 0x1250; c <= 0x1256; ++c) (*smap)[c] = U_Ethiopic;
  (*smap)[0x1258] = U_Ethiopic;
  for (char32 c = 0x125A; c <= 0x125D; ++c) (*smap)[c] = U_Ethiopic;
  for (char32 c = 0x1260; c <= 0x1288; ++c) (*smap)[c] = U_Ethiopic;
  for (char32 c = 0x128A; c <= 0x128D; ++c) (*smap)[c] = U_Ethiopic;
  for (char32 c = 0x1290; c <= 0x12B0; ++c) (*smap)[c] = U_Ethiopic;
  for (char32 c = 0x12B2; c <= 0x12B5; ++c) (*smap)[c] = U_Ethiopic;
  for (char32 c = 0x12B8; c <= 0x12BE; ++c) (*smap)[c] = U_Ethiopic;
  (*smap)[0x12C0] = U_Ethiopic;
  for (char32 c = 0x12C2; c <= 0x12C5; ++c) (*smap)[c] = U_Ethiopic;
  for (char32 c = 0x12C8; c <= 0x12D6; ++c) (*smap)[c] = U_Ethiopic;
  for (char32 c = 0x12D8; c <= 0x1310; ++c) (*smap)[c] = U_Ethiopic;
  for (char32 c = 0x1312; c <= 0x1315; ++c) (*smap)[c] = U_Ethiopic;
  for (char32 c = 0x1318; c <= 0x135A; ++c) (*smap)[c] = U_Ethiopic;
  for (char32 c = 0x135D; c <= 0x135F; ++c) (*smap)[c] = U_Ethiopic;
  for (char32 c = 0x1360; c <= 0x1368; ++c) (*smap)[c] = U_Ethiopic;
  for (char32 c = 0x1369; c <= 0x137C; ++c) (*smap)[c] = U_Ethiopic;
  for (char32 c = 0x1380; c <= 0x138F; ++c) (*smap)[c] = U_Ethiopic;
  for (char32 c = 0x1390; c <= 0x1399; ++c) (*smap)[c] = U_Ethiopic;
  for (char32 c = 0x2D80; c <= 0x2D96; ++c) (*smap)[c] = U_Ethiopic;
  for (char32 c = 0x2DA0; c <= 0x2DA6; ++c) (*smap)[c] = U_Ethiopic;
  for (char32 c = 0x2DA8; c <= 0x2DAE; ++c) (*smap)[c] = U_Ethiopic;
  for (char32 c = 0x2DB0; c <= 0x2DB6; ++c) (*smap)[c] = U_Ethiopic;
  for (char32 c = 0x2DB8; c <= 0x2DBE; ++c) (*smap)[c] = U_Ethiopic;
  for (char32 c = 0x2DC0; c <= 0x2DC6; ++c) (*smap)[c] = U_Ethiopic;
  for (char32 c = 0x2DC8; c <= 0x2DCE; ++c) (*smap)[c] = U_Ethiopic;
  for (char32 c = 0x2DD0; c <= 0x2DD6; ++c) (*smap)[c] = U_Ethiopic;
  for (char32 c = 0x2DD8; c <= 0x2DDE; ++c) (*smap)[c] = U_Ethiopic;
  for (char32 c = 0xAB01; c <= 0xAB06; ++c) (*smap)[c] = U_Ethiopic;
  for (char32 c = 0xAB09; c <= 0xAB0E; ++c) (*smap)[c] = U_Ethiopic;
  for (char32 c = 0xAB11; c <= 0xAB16; ++c) (*smap)[c] = U_Ethiopic;
  for (char32 c = 0xAB20; c <= 0xAB26; ++c) (*smap)[c] = U_Ethiopic;
  for (char32 c = 0xAB28; c <= 0xAB2E; ++c) (*smap)[c] = U_Ethiopic;
  for (char32 c = 0x13A0; c <= 0x13F5; ++c) (*smap)[c] = U_Cherokee;
  for (char32 c = 0x13F8; c <= 0x13FD; ++c) (*smap)[c] = U_Cherokee;
  for (char32 c = 0xAB70; c <= 0xABBF; ++c) (*smap)[c] = U_Cherokee;
  (*smap)[0x1400] = U_Canadian_Aboriginal;
  for (char32 c = 0x1401; c <= 0x166C; ++c) (*smap)[c] = U_Canadian_Aboriginal;
  for (char32 c = 0x166D; c <= 0x166E; ++c) (*smap)[c] = U_Canadian_Aboriginal;
  for (char32 c = 0x166F; c <= 0x167F; ++c) (*smap)[c] = U_Canadian_Aboriginal;
  for (char32 c = 0x18B0; c <= 0x18F5; ++c) (*smap)[c] = U_Canadian_Aboriginal;
  (*smap)[0x1680] = U_Ogham;
  for (char32 c = 0x1681; c <= 0x169A; ++c) (*smap)[c] = U_Ogham;
  (*smap)[0x169B] = U_Ogham;
  (*smap)[0x169C] = U_Ogham;
  for (char32 c = 0x16A0; c <= 0x16EA; ++c) (*smap)[c] = U_Runic;
  for (char32 c = 0x16EE; c <= 0x16F0; ++c) (*smap)[c] = U_Runic;
  for (char32 c = 0x16F1; c <= 0x16F8; ++c) (*smap)[c] = U_Runic;
  for (char32 c = 0x1780; c <= 0x17B3; ++c) (*smap)[c] = U_Khmer;
  for (char32 c = 0x17B4; c <= 0x17B5; ++c) (*smap)[c] = U_Khmer;
  (*smap)[0x17B6] = U_Khmer;
  for (char32 c = 0x17B7; c <= 0x17BD; ++c) (*smap)[c] = U_Khmer;
  for (char32 c = 0x17BE; c <= 0x17C5; ++c) (*smap)[c] = U_Khmer;
  (*smap)[0x17C6] = U_Khmer;
  for (char32 c = 0x17C7; c <= 0x17C8; ++c) (*smap)[c] = U_Khmer;
  for (char32 c = 0x17C9; c <= 0x17D3; ++c) (*smap)[c] = U_Khmer;
  for (char32 c = 0x17D4; c <= 0x17D6; ++c) (*smap)[c] = U_Khmer;
  (*smap)[0x17D7] = U_Khmer;
  for (char32 c = 0x17D8; c <= 0x17DA; ++c) (*smap)[c] = U_Khmer;
  (*smap)[0x17DB] = U_Khmer;
  (*smap)[0x17DC] = U_Khmer;
  (*smap)[0x17DD] = U_Khmer;
  for (char32 c = 0x17E0; c <= 0x17E9; ++c) (*smap)[c] = U_Khmer;
  for (char32 c = 0x17F0; c <= 0x17F9; ++c) (*smap)[c] = U_Khmer;
  for (char32 c = 0x19E0; c <= 0x19FF; ++c) (*smap)[c] = U_Khmer;
  for (char32 c = 0x1800; c <= 0x1801; ++c) (*smap)[c] = U_Mongolian;
  (*smap)[0x1804] = U_Mongolian;
  (*smap)[0x1806] = U_Mongolian;
  for (char32 c = 0x1807; c <= 0x180A; ++c) (*smap)[c] = U_Mongolian;
  for (char32 c = 0x180B; c <= 0x180D; ++c) (*smap)[c] = U_Mongolian;
  (*smap)[0x180E] = U_Mongolian;
  for (char32 c = 0x1810; c <= 0x1819; ++c) (*smap)[c] = U_Mongolian;
  for (char32 c = 0x1820; c <= 0x1842; ++c) (*smap)[c] = U_Mongolian;
  (*smap)[0x1843] = U_Mongolian;
  for (char32 c = 0x1844; c <= 0x1877; ++c) (*smap)[c] = U_Mongolian;
  for (char32 c = 0x1880; c <= 0x1884; ++c) (*smap)[c] = U_Mongolian;
  for (char32 c = 0x1885; c <= 0x1886; ++c) (*smap)[c] = U_Mongolian;
  for (char32 c = 0x1887; c <= 0x18A8; ++c) (*smap)[c] = U_Mongolian;
  (*smap)[0x18A9] = U_Mongolian;
  (*smap)[0x18AA] = U_Mongolian;
  for (char32 c = 0x11660; c <= 0x1166C; ++c) (*smap)[c] = U_Mongolian;
  for (char32 c = 0x3041; c <= 0x3096; ++c) (*smap)[c] = U_Hiragana;
  for (char32 c = 0x309D; c <= 0x309E; ++c) (*smap)[c] = U_Hiragana;
  (*smap)[0x309F] = U_Hiragana;
  (*smap)[0x1B001] = U_Hiragana;
  (*smap)[0x1F200] = U_Hiragana;
  for (char32 c = 0x30A1; c <= 0x30FA; ++c) (*smap)[c] = U_Katakana;
  for (char32 c = 0x30FD; c <= 0x30FE; ++c) (*smap)[c] = U_Katakana;
  (*smap)[0x30FF] = U_Katakana;
  for (char32 c = 0x31F0; c <= 0x31FF; ++c) (*smap)[c] = U_Katakana;
  for (char32 c = 0x32D0; c <= 0x32FE; ++c) (*smap)[c] = U_Katakana;
  for (char32 c = 0x3300; c <= 0x3357; ++c) (*smap)[c] = U_Katakana;
  for (char32 c = 0xFF66; c <= 0xFF6F; ++c) (*smap)[c] = U_Katakana;
  for (char32 c = 0xFF71; c <= 0xFF9D; ++c) (*smap)[c] = U_Katakana;
  (*smap)[0x1B000] = U_Katakana;
  for (char32 c = 0x02EA; c <= 0x02EB; ++c) (*smap)[c] = U_Bopomofo;
  for (char32 c = 0x3105; c <= 0x312D; ++c) (*smap)[c] = U_Bopomofo;
  for (char32 c = 0x31A0; c <= 0x31BA; ++c) (*smap)[c] = U_Bopomofo;
  for (char32 c = 0x2E80; c <= 0x2E99; ++c) (*smap)[c] = U_Han;
  for (char32 c = 0x2E9B; c <= 0x2EF3; ++c) (*smap)[c] = U_Han;
  for (char32 c = 0x2F00; c <= 0x2FD5; ++c) (*smap)[c] = U_Han;
  (*smap)[0x3005] = U_Han;
  (*smap)[0x3007] = U_Han;
  for (char32 c = 0x3021; c <= 0x3029; ++c) (*smap)[c] = U_Han;
  for (char32 c = 0x3038; c <= 0x303A; ++c) (*smap)[c] = U_Han;
  (*smap)[0x303B] = U_Han;
  for (char32 c = 0x3400; c <= 0x4DB5; ++c) (*smap)[c] = U_Han;
  for (char32 c = 0x4E00; c <= 0x9FD5; ++c) (*smap)[c] = U_Han;
  for (char32 c = 0xF900; c <= 0xFA6D; ++c) (*smap)[c] = U_Han;
  for (char32 c = 0xFA70; c <= 0xFAD9; ++c) (*smap)[c] = U_Han;
  for (char32 c = 0x20000; c <= 0x2A6D6; ++c) (*smap)[c] = U_Han;
  for (char32 c = 0x2A700; c <= 0x2B734; ++c) (*smap)[c] = U_Han;
  for (char32 c = 0x2B740; c <= 0x2B81D; ++c) (*smap)[c] = U_Han;
  for (char32 c = 0x2B820; c <= 0x2CEA1; ++c) (*smap)[c] = U_Han;
  for (char32 c = 0x2F800; c <= 0x2FA1D; ++c) (*smap)[c] = U_Han;
  for (char32 c = 0xA000; c <= 0xA014; ++c) (*smap)[c] = U_Yi;
  (*smap)[0xA015] = U_Yi;
  for (char32 c = 0xA016; c <= 0xA48C; ++c) (*smap)[c] = U_Yi;
  for (char32 c = 0xA490; c <= 0xA4C6; ++c) (*smap)[c] = U_Yi;
  for (char32 c = 0x10300; c <= 0x1031F; ++c) (*smap)[c] = U_Old_Italic;
  for (char32 c = 0x10320; c <= 0x10323; ++c) (*smap)[c] = U_Old_Italic;
  for (char32 c = 0x10330; c <= 0x10340; ++c) (*smap)[c] = U_Gothic;
  (*smap)[0x10341] = U_Gothic;
  for (char32 c = 0x10342; c <= 0x10349; ++c) (*smap)[c] = U_Gothic;
  (*smap)[0x1034A] = U_Gothic;
  for (char32 c = 0x10400; c <= 0x1044F; ++c) (*smap)[c] = U_Deseret;
  for (char32 c = 0x0300; c <= 0x036F; ++c) (*smap)[c] = U_Inherited;
  for (char32 c = 0x0485; c <= 0x0486; ++c) (*smap)[c] = U_Inherited;
  for (char32 c = 0x064B; c <= 0x0655; ++c) (*smap)[c] = U_Inherited;
  (*smap)[0x0670] = U_Inherited;
  for (char32 c = 0x0951; c <= 0x0952; ++c) (*smap)[c] = U_Inherited;
  for (char32 c = 0x1AB0; c <= 0x1ABD; ++c) (*smap)[c] = U_Inherited;
  (*smap)[0x1ABE] = U_Inherited;
  for (char32 c = 0x1CD0; c <= 0x1CD2; ++c) (*smap)[c] = U_Inherited;
  for (char32 c = 0x1CD4; c <= 0x1CE0; ++c) (*smap)[c] = U_Inherited;
  for (char32 c = 0x1CE2; c <= 0x1CE8; ++c) (*smap)[c] = U_Inherited;
  (*smap)[0x1CED] = U_Inherited;
  (*smap)[0x1CF4] = U_Inherited;
  for (char32 c = 0x1CF8; c <= 0x1CF9; ++c) (*smap)[c] = U_Inherited;
  for (char32 c = 0x1DC0; c <= 0x1DF5; ++c) (*smap)[c] = U_Inherited;
  for (char32 c = 0x1DFB; c <= 0x1DFF; ++c) (*smap)[c] = U_Inherited;
  for (char32 c = 0x200C; c <= 0x200D; ++c) (*smap)[c] = U_Inherited;
  for (char32 c = 0x20D0; c <= 0x20DC; ++c) (*smap)[c] = U_Inherited;
  for (char32 c = 0x20DD; c <= 0x20E0; ++c) (*smap)[c] = U_Inherited;
  (*smap)[0x20E1] = U_Inherited;
  for (char32 c = 0x20E2; c <= 0x20E4; ++c) (*smap)[c] = U_Inherited;
  for (char32 c = 0x20E5; c <= 0x20F0; ++c) (*smap)[c] = U_Inherited;
  for (char32 c = 0x302A; c <= 0x302D; ++c) (*smap)[c] = U_Inherited;
  for (char32 c = 0x3099; c <= 0x309A; ++c) (*smap)[c] = U_Inherited;
  for (char32 c = 0xFE00; c <= 0xFE0F; ++c) (*smap)[c] = U_Inherited;
  for (char32 c = 0xFE20; c <= 0xFE2D; ++c) (*smap)[c] = U_Inherited;
  (*smap)[0x101FD] = U_Inherited;
  (*smap)[0x102E0] = U_Inherited;
  for (char32 c = 0x1D167; c <= 0x1D169; ++c) (*smap)[c] = U_Inherited;
  for (char32 c = 0x1D17B; c <= 0x1D182; ++c) (*smap)[c] = U_Inherited;
  for (char32 c = 0x1D185; c <= 0x1D18B; ++c) (*smap)[c] = U_Inherited;
  for (char32 c = 0x1D1AA; c <= 0x1D1AD; ++c) (*smap)[c] = U_Inherited;
  for (char32 c = 0xE0100; c <= 0xE01EF; ++c) (*smap)[c] = U_Inherited;
  for (char32 c = 0x1700; c <= 0x170C; ++c) (*smap)[c] = U_Tagalog;
  for (char32 c = 0x170E; c <= 0x1711; ++c) (*smap)[c] = U_Tagalog;
  for (char32 c = 0x1712; c <= 0x1714; ++c) (*smap)[c] = U_Tagalog;
  for (char32 c = 0x1720; c <= 0x1731; ++c) (*smap)[c] = U_Hanunoo;
  for (char32 c = 0x1732; c <= 0x1734; ++c) (*smap)[c] = U_Hanunoo;
  for (char32 c = 0x1740; c <= 0x1751; ++c) (*smap)[c] = U_Buhid;
  for (char32 c = 0x1752; c <= 0x1753; ++c) (*smap)[c] = U_Buhid;
  for (char32 c = 0x1760; c <= 0x176C; ++c) (*smap)[c] = U_Tagbanwa;
  for (char32 c = 0x176E; c <= 0x1770; ++c) (*smap)[c] = U_Tagbanwa;
  for (char32 c = 0x1772; c <= 0x1773; ++c) (*smap)[c] = U_Tagbanwa;
  for (char32 c = 0x1900; c <= 0x191E; ++c) (*smap)[c] = U_Limbu;
  for (char32 c = 0x1920; c <= 0x1922; ++c) (*smap)[c] = U_Limbu;
  for (char32 c = 0x1923; c <= 0x1926; ++c) (*smap)[c] = U_Limbu;
  for (char32 c = 0x1927; c <= 0x1928; ++c) (*smap)[c] = U_Limbu;
  for (char32 c = 0x1929; c <= 0x192B; ++c) (*smap)[c] = U_Limbu;
  for (char32 c = 0x1930; c <= 0x1931; ++c) (*smap)[c] = U_Limbu;
  (*smap)[0x1932] = U_Limbu;
  for (char32 c = 0x1933; c <= 0x1938; ++c) (*smap)[c] = U_Limbu;
  for (char32 c = 0x1939; c <= 0x193B; ++c) (*smap)[c] = U_Limbu;
  (*smap)[0x1940] = U_Limbu;
  for (char32 c = 0x1944; c <= 0x1945; ++c) (*smap)[c] = U_Limbu;
  for (char32 c = 0x1946; c <= 0x194F; ++c) (*smap)[c] = U_Limbu;
  for (char32 c = 0x1950; c <= 0x196D; ++c) (*smap)[c] = U_Tai_Le;
  for (char32 c = 0x1970; c <= 0x1974; ++c) (*smap)[c] = U_Tai_Le;
  for (char32 c = 0x10000; c <= 0x1000B; ++c) (*smap)[c] = U_Linear_B;
  for (char32 c = 0x1000D; c <= 0x10026; ++c) (*smap)[c] = U_Linear_B;
  for (char32 c = 0x10028; c <= 0x1003A; ++c) (*smap)[c] = U_Linear_B;
  for (char32 c = 0x1003C; c <= 0x1003D; ++c) (*smap)[c] = U_Linear_B;
  for (char32 c = 0x1003F; c <= 0x1004D; ++c) (*smap)[c] = U_Linear_B;
  for (char32 c = 0x10050; c <= 0x1005D; ++c) (*smap)[c] = U_Linear_B;
  for (char32 c = 0x10080; c <= 0x100FA; ++c) (*smap)[c] = U_Linear_B;
  for (char32 c = 0x10380; c <= 0x1039D; ++c) (*smap)[c] = U_Ugaritic;
  (*smap)[0x1039F] = U_Ugaritic;
  for (char32 c = 0x10450; c <= 0x1047F; ++c) (*smap)[c] = U_Shavian;
  for (char32 c = 0x10480; c <= 0x1049D; ++c) (*smap)[c] = U_Osmanya;
  for (char32 c = 0x104A0; c <= 0x104A9; ++c) (*smap)[c] = U_Osmanya;
  for (char32 c = 0x10800; c <= 0x10805; ++c) (*smap)[c] = U_Cypriot;
  (*smap)[0x10808] = U_Cypriot;
  for (char32 c = 0x1080A; c <= 0x10835; ++c) (*smap)[c] = U_Cypriot;
  for (char32 c = 0x10837; c <= 0x10838; ++c) (*smap)[c] = U_Cypriot;
  (*smap)[0x1083C] = U_Cypriot;
  (*smap)[0x1083F] = U_Cypriot;
  for (char32 c = 0x2800; c <= 0x28FF; ++c) (*smap)[c] = U_Braille;
  for (char32 c = 0x1A00; c <= 0x1A16; ++c) (*smap)[c] = U_Buginese;
  for (char32 c = 0x1A17; c <= 0x1A18; ++c) (*smap)[c] = U_Buginese;
  for (char32 c = 0x1A19; c <= 0x1A1A; ++c) (*smap)[c] = U_Buginese;
  (*smap)[0x1A1B] = U_Buginese;
  for (char32 c = 0x1A1E; c <= 0x1A1F; ++c) (*smap)[c] = U_Buginese;
  for (char32 c = 0x03E2; c <= 0x03EF; ++c) (*smap)[c] = U_Coptic;
  for (char32 c = 0x2C80; c <= 0x2CE4; ++c) (*smap)[c] = U_Coptic;
  for (char32 c = 0x2CE5; c <= 0x2CEA; ++c) (*smap)[c] = U_Coptic;
  for (char32 c = 0x2CEB; c <= 0x2CEE; ++c) (*smap)[c] = U_Coptic;
  for (char32 c = 0x2CEF; c <= 0x2CF1; ++c) (*smap)[c] = U_Coptic;
  for (char32 c = 0x2CF2; c <= 0x2CF3; ++c) (*smap)[c] = U_Coptic;
  for (char32 c = 0x2CF9; c <= 0x2CFC; ++c) (*smap)[c] = U_Coptic;
  (*smap)[0x2CFD] = U_Coptic;
  for (char32 c = 0x2CFE; c <= 0x2CFF; ++c) (*smap)[c] = U_Coptic;
  for (char32 c = 0x1980; c <= 0x19AB; ++c) (*smap)[c] = U_New_Tai_Lue;
  for (char32 c = 0x19B0; c <= 0x19C9; ++c) (*smap)[c] = U_New_Tai_Lue;
  for (char32 c = 0x19D0; c <= 0x19D9; ++c) (*smap)[c] = U_New_Tai_Lue;
  (*smap)[0x19DA] = U_New_Tai_Lue;
  for (char32 c = 0x19DE; c <= 0x19DF; ++c) (*smap)[c] = U_New_Tai_Lue;
  for (char32 c = 0x2C00; c <= 0x2C2E; ++c) (*smap)[c] = U_Glagolitic;
  for (char32 c = 0x2C30; c <= 0x2C5E; ++c) (*smap)[c] = U_Glagolitic;
  for (char32 c = 0x1E000; c <= 0x1E006; ++c) (*smap)[c] = U_Glagolitic;
  for (char32 c = 0x1E008; c <= 0x1E018; ++c) (*smap)[c] = U_Glagolitic;
  for (char32 c = 0x1E01B; c <= 0x1E021; ++c) (*smap)[c] = U_Glagolitic;
  for (char32 c = 0x1E023; c <= 0x1E024; ++c) (*smap)[c] = U_Glagolitic;
  for (char32 c = 0x1E026; c <= 0x1E02A; ++c) (*smap)[c] = U_Glagolitic;
  for (char32 c = 0x2D30; c <= 0x2D67; ++c) (*smap)[c] = U_Tifinagh;
  (*smap)[0x2D6F] = U_Tifinagh;
  (*smap)[0x2D70] = U_Tifinagh;
  (*smap)[0x2D7F] = U_Tifinagh;
  for (char32 c = 0xA800; c <= 0xA801; ++c) (*smap)[c] = U_Syloti_Nagri;
  (*smap)[0xA802] = U_Syloti_Nagri;
  for (char32 c = 0xA803; c <= 0xA805; ++c) (*smap)[c] = U_Syloti_Nagri;
  (*smap)[0xA806] = U_Syloti_Nagri;
  for (char32 c = 0xA807; c <= 0xA80A; ++c) (*smap)[c] = U_Syloti_Nagri;
  (*smap)[0xA80B] = U_Syloti_Nagri;
  for (char32 c = 0xA80C; c <= 0xA822; ++c) (*smap)[c] = U_Syloti_Nagri;
  for (char32 c = 0xA823; c <= 0xA824; ++c) (*smap)[c] = U_Syloti_Nagri;
  for (char32 c = 0xA825; c <= 0xA826; ++c) (*smap)[c] = U_Syloti_Nagri;
  (*smap)[0xA827] = U_Syloti_Nagri;
  for (char32 c = 0xA828; c <= 0xA82B; ++c) (*smap)[c] = U_Syloti_Nagri;
  for (char32 c = 0x103A0; c <= 0x103C3; ++c) (*smap)[c] = U_Old_Persian;
  for (char32 c = 0x103C8; c <= 0x103CF; ++c) (*smap)[c] = U_Old_Persian;
  (*smap)[0x103D0] = U_Old_Persian;
  for (char32 c = 0x103D1; c <= 0x103D5; ++c) (*smap)[c] = U_Old_Persian;
  (*smap)[0x10A00] = U_Kharoshthi;
  for (char32 c = 0x10A01; c <= 0x10A03; ++c) (*smap)[c] = U_Kharoshthi;
  for (char32 c = 0x10A05; c <= 0x10A06; ++c) (*smap)[c] = U_Kharoshthi;
  for (char32 c = 0x10A0C; c <= 0x10A0F; ++c) (*smap)[c] = U_Kharoshthi;
  for (char32 c = 0x10A10; c <= 0x10A13; ++c) (*smap)[c] = U_Kharoshthi;
  for (char32 c = 0x10A15; c <= 0x10A17; ++c) (*smap)[c] = U_Kharoshthi;
  for (char32 c = 0x10A19; c <= 0x10A33; ++c) (*smap)[c] = U_Kharoshthi;
  for (char32 c = 0x10A38; c <= 0x10A3A; ++c) (*smap)[c] = U_Kharoshthi;
  (*smap)[0x10A3F] = U_Kharoshthi;
  for (char32 c = 0x10A40; c <= 0x10A47; ++c) (*smap)[c] = U_Kharoshthi;
  for (char32 c = 0x10A50; c <= 0x10A58; ++c) (*smap)[c] = U_Kharoshthi;
  for (char32 c = 0x1B00; c <= 0x1B03; ++c) (*smap)[c] = U_Balinese;
  (*smap)[0x1B04] = U_Balinese;
  for (char32 c = 0x1B05; c <= 0x1B33; ++c) (*smap)[c] = U_Balinese;
  (*smap)[0x1B34] = U_Balinese;
  (*smap)[0x1B35] = U_Balinese;
  for (char32 c = 0x1B36; c <= 0x1B3A; ++c) (*smap)[c] = U_Balinese;
  (*smap)[0x1B3B] = U_Balinese;
  (*smap)[0x1B3C] = U_Balinese;
  for (char32 c = 0x1B3D; c <= 0x1B41; ++c) (*smap)[c] = U_Balinese;
  (*smap)[0x1B42] = U_Balinese;
  for (char32 c = 0x1B43; c <= 0x1B44; ++c) (*smap)[c] = U_Balinese;
  for (char32 c = 0x1B45; c <= 0x1B4B; ++c) (*smap)[c] = U_Balinese;
  for (char32 c = 0x1B50; c <= 0x1B59; ++c) (*smap)[c] = U_Balinese;
  for (char32 c = 0x1B5A; c <= 0x1B60; ++c) (*smap)[c] = U_Balinese;
  for (char32 c = 0x1B61; c <= 0x1B6A; ++c) (*smap)[c] = U_Balinese;
  for (char32 c = 0x1B6B; c <= 0x1B73; ++c) (*smap)[c] = U_Balinese;
  for (char32 c = 0x1B74; c <= 0x1B7C; ++c) (*smap)[c] = U_Balinese;
  for (char32 c = 0x12000; c <= 0x12399; ++c) (*smap)[c] = U_Cuneiform;
  for (char32 c = 0x12400; c <= 0x1246E; ++c) (*smap)[c] = U_Cuneiform;
  for (char32 c = 0x12470; c <= 0x12474; ++c) (*smap)[c] = U_Cuneiform;
  for (char32 c = 0x12480; c <= 0x12543; ++c) (*smap)[c] = U_Cuneiform;
  for (char32 c = 0x10900; c <= 0x10915; ++c) (*smap)[c] = U_Phoenician;
  for (char32 c = 0x10916; c <= 0x1091B; ++c) (*smap)[c] = U_Phoenician;
  (*smap)[0x1091F] = U_Phoenician;
  for (char32 c = 0xA840; c <= 0xA873; ++c) (*smap)[c] = U_Phags_Pa;
  for (char32 c = 0xA874; c <= 0xA877; ++c) (*smap)[c] = U_Phags_Pa;
  for (char32 c = 0x07C0; c <= 0x07C9; ++c) (*smap)[c] = U_Nko;
  for (char32 c = 0x07CA; c <= 0x07EA; ++c) (*smap)[c] = U_Nko;
  for (char32 c = 0x07EB; c <= 0x07F3; ++c) (*smap)[c] = U_Nko;
  for (char32 c = 0x07F4; c <= 0x07F5; ++c) (*smap)[c] = U_Nko;
  (*smap)[0x07F6] = U_Nko;
  for (char32 c = 0x07F7; c <= 0x07F9; ++c) (*smap)[c] = U_Nko;
  (*smap)[0x07FA] = U_Nko;
  for (char32 c = 0x1B80; c <= 0x1B81; ++c) (*smap)[c] = U_Sundanese;
  (*smap)[0x1B82] = U_Sundanese;
  for (char32 c = 0x1B83; c <= 0x1BA0; ++c) (*smap)[c] = U_Sundanese;
  (*smap)[0x1BA1] = U_Sundanese;
  for (char32 c = 0x1BA2; c <= 0x1BA5; ++c) (*smap)[c] = U_Sundanese;
  for (char32 c = 0x1BA6; c <= 0x1BA7; ++c) (*smap)[c] = U_Sundanese;
  for (char32 c = 0x1BA8; c <= 0x1BA9; ++c) (*smap)[c] = U_Sundanese;
  (*smap)[0x1BAA] = U_Sundanese;
  for (char32 c = 0x1BAB; c <= 0x1BAD; ++c) (*smap)[c] = U_Sundanese;
  for (char32 c = 0x1BAE; c <= 0x1BAF; ++c) (*smap)[c] = U_Sundanese;
  for (char32 c = 0x1BB0; c <= 0x1BB9; ++c) (*smap)[c] = U_Sundanese;
  for (char32 c = 0x1BBA; c <= 0x1BBF; ++c) (*smap)[c] = U_Sundanese;
  for (char32 c = 0x1CC0; c <= 0x1CC7; ++c) (*smap)[c] = U_Sundanese;
  for (char32 c = 0x1C00; c <= 0x1C23; ++c) (*smap)[c] = U_Lepcha;
  for (char32 c = 0x1C24; c <= 0x1C2B; ++c) (*smap)[c] = U_Lepcha;
  for (char32 c = 0x1C2C; c <= 0x1C33; ++c) (*smap)[c] = U_Lepcha;
  for (char32 c = 0x1C34; c <= 0x1C35; ++c) (*smap)[c] = U_Lepcha;
  for (char32 c = 0x1C36; c <= 0x1C37; ++c) (*smap)[c] = U_Lepcha;
  for (char32 c = 0x1C3B; c <= 0x1C3F; ++c) (*smap)[c] = U_Lepcha;
  for (char32 c = 0x1C40; c <= 0x1C49; ++c) (*smap)[c] = U_Lepcha;
  for (char32 c = 0x1C4D; c <= 0x1C4F; ++c) (*smap)[c] = U_Lepcha;
  for (char32 c = 0x1C50; c <= 0x1C59; ++c) (*smap)[c] = U_Ol_Chiki;
  for (char32 c = 0x1C5A; c <= 0x1C77; ++c) (*smap)[c] = U_Ol_Chiki;
  for (char32 c = 0x1C78; c <= 0x1C7D; ++c) (*smap)[c] = U_Ol_Chiki;
  for (char32 c = 0x1C7E; c <= 0x1C7F; ++c) (*smap)[c] = U_Ol_Chiki;
  for (char32 c = 0xA500; c <= 0xA60B; ++c) (*smap)[c] = U_Vai;
  (*smap)[0xA60C] = U_Vai;
  for (char32 c = 0xA60D; c <= 0xA60F; ++c) (*smap)[c] = U_Vai;
  for (char32 c = 0xA610; c <= 0xA61F; ++c) (*smap)[c] = U_Vai;
  for (char32 c = 0xA620; c <= 0xA629; ++c) (*smap)[c] = U_Vai;
  for (char32 c = 0xA62A; c <= 0xA62B; ++c) (*smap)[c] = U_Vai;
  for (char32 c = 0xA880; c <= 0xA881; ++c) (*smap)[c] = U_Saurashtra;
  for (char32 c = 0xA882; c <= 0xA8B3; ++c) (*smap)[c] = U_Saurashtra;
  for (char32 c = 0xA8B4; c <= 0xA8C3; ++c) (*smap)[c] = U_Saurashtra;
  for (char32 c = 0xA8C4; c <= 0xA8C5; ++c) (*smap)[c] = U_Saurashtra;
  for (char32 c = 0xA8CE; c <= 0xA8CF; ++c) (*smap)[c] = U_Saurashtra;
  for (char32 c = 0xA8D0; c <= 0xA8D9; ++c) (*smap)[c] = U_Saurashtra;
  for (char32 c = 0xA900; c <= 0xA909; ++c) (*smap)[c] = U_Kayah_Li;
  for (char32 c = 0xA90A; c <= 0xA925; ++c) (*smap)[c] = U_Kayah_Li;
  for (char32 c = 0xA926; c <= 0xA92D; ++c) (*smap)[c] = U_Kayah_Li;
  (*smap)[0xA92F] = U_Kayah_Li;
  for (char32 c = 0xA930; c <= 0xA946; ++c) (*smap)[c] = U_Rejang;
  for (char32 c = 0xA947; c <= 0xA951; ++c) (*smap)[c] = U_Rejang;
  for (char32 c = 0xA952; c <= 0xA953; ++c) (*smap)[c] = U_Rejang;
  (*smap)[0xA95F] = U_Rejang;
  for (char32 c = 0x10280; c <= 0x1029C; ++c) (*smap)[c] = U_Lycian;
  for (char32 c = 0x102A0; c <= 0x102D0; ++c) (*smap)[c] = U_Carian;
  for (char32 c = 0x10920; c <= 0x10939; ++c) (*smap)[c] = U_Lydian;
  (*smap)[0x1093F] = U_Lydian;
  for (char32 c = 0xAA00; c <= 0xAA28; ++c) (*smap)[c] = U_Cham;
  for (char32 c = 0xAA29; c <= 0xAA2E; ++c) (*smap)[c] = U_Cham;
  for (char32 c = 0xAA2F; c <= 0xAA30; ++c) (*smap)[c] = U_Cham;
  for (char32 c = 0xAA31; c <= 0xAA32; ++c) (*smap)[c] = U_Cham;
  for (char32 c = 0xAA33; c <= 0xAA34; ++c) (*smap)[c] = U_Cham;
  for (char32 c = 0xAA35; c <= 0xAA36; ++c) (*smap)[c] = U_Cham;
  for (char32 c = 0xAA40; c <= 0xAA42; ++c) (*smap)[c] = U_Cham;
  (*smap)[0xAA43] = U_Cham;
  for (char32 c = 0xAA44; c <= 0xAA4B; ++c) (*smap)[c] = U_Cham;
  (*smap)[0xAA4C] = U_Cham;
  (*smap)[0xAA4D] = U_Cham;
  for (char32 c = 0xAA50; c <= 0xAA59; ++c) (*smap)[c] = U_Cham;
  for (char32 c = 0xAA5C; c <= 0xAA5F; ++c) (*smap)[c] = U_Cham;
  for (char32 c = 0x1A20; c <= 0x1A54; ++c) (*smap)[c] = U_Tai_Tham;
  (*smap)[0x1A55] = U_Tai_Tham;
  (*smap)[0x1A56] = U_Tai_Tham;
  (*smap)[0x1A57] = U_Tai_Tham;
  for (char32 c = 0x1A58; c <= 0x1A5E; ++c) (*smap)[c] = U_Tai_Tham;
  (*smap)[0x1A60] = U_Tai_Tham;
  (*smap)[0x1A61] = U_Tai_Tham;
  (*smap)[0x1A62] = U_Tai_Tham;
  for (char32 c = 0x1A63; c <= 0x1A64; ++c) (*smap)[c] = U_Tai_Tham;
  for (char32 c = 0x1A65; c <= 0x1A6C; ++c) (*smap)[c] = U_Tai_Tham;
  for (char32 c = 0x1A6D; c <= 0x1A72; ++c) (*smap)[c] = U_Tai_Tham;
  for (char32 c = 0x1A73; c <= 0x1A7C; ++c) (*smap)[c] = U_Tai_Tham;
  (*smap)[0x1A7F] = U_Tai_Tham;
  for (char32 c = 0x1A80; c <= 0x1A89; ++c) (*smap)[c] = U_Tai_Tham;
  for (char32 c = 0x1A90; c <= 0x1A99; ++c) (*smap)[c] = U_Tai_Tham;
  for (char32 c = 0x1AA0; c <= 0x1AA6; ++c) (*smap)[c] = U_Tai_Tham;
  (*smap)[0x1AA7] = U_Tai_Tham;
  for (char32 c = 0x1AA8; c <= 0x1AAD; ++c) (*smap)[c] = U_Tai_Tham;
  for (char32 c = 0xAA80; c <= 0xAAAF; ++c) (*smap)[c] = U_Tai_Viet;
  (*smap)[0xAAB0] = U_Tai_Viet;
  (*smap)[0xAAB1] = U_Tai_Viet;
  for (char32 c = 0xAAB2; c <= 0xAAB4; ++c) (*smap)[c] = U_Tai_Viet;
  for (char32 c = 0xAAB5; c <= 0xAAB6; ++c) (*smap)[c] = U_Tai_Viet;
  for (char32 c = 0xAAB7; c <= 0xAAB8; ++c) (*smap)[c] = U_Tai_Viet;
  for (char32 c = 0xAAB9; c <= 0xAABD; ++c) (*smap)[c] = U_Tai_Viet;
  for (char32 c = 0xAABE; c <= 0xAABF; ++c) (*smap)[c] = U_Tai_Viet;
  (*smap)[0xAAC0] = U_Tai_Viet;
  (*smap)[0xAAC1] = U_Tai_Viet;
  (*smap)[0xAAC2] = U_Tai_Viet;
  for (char32 c = 0xAADB; c <= 0xAADC; ++c) (*smap)[c] = U_Tai_Viet;
  (*smap)[0xAADD] = U_Tai_Viet;
  for (char32 c = 0xAADE; c <= 0xAADF; ++c) (*smap)[c] = U_Tai_Viet;
  for (char32 c = 0x10B00; c <= 0x10B35; ++c) (*smap)[c] = U_Avestan;
  for (char32 c = 0x10B39; c <= 0x10B3F; ++c) (*smap)[c] = U_Avestan;
  for (char32 c = 0x13000; c <= 0x1342E; ++c)
    (*smap)[c] = U_Egyptian_Hieroglyphs;
  for (char32 c = 0x0800; c <= 0x0815; ++c) (*smap)[c] = U_Samaritan;
  for (char32 c = 0x0816; c <= 0x0819; ++c) (*smap)[c] = U_Samaritan;
  (*smap)[0x081A] = U_Samaritan;
  for (char32 c = 0x081B; c <= 0x0823; ++c) (*smap)[c] = U_Samaritan;
  (*smap)[0x0824] = U_Samaritan;
  for (char32 c = 0x0825; c <= 0x0827; ++c) (*smap)[c] = U_Samaritan;
  (*smap)[0x0828] = U_Samaritan;
  for (char32 c = 0x0829; c <= 0x082D; ++c) (*smap)[c] = U_Samaritan;
  for (char32 c = 0x0830; c <= 0x083E; ++c) (*smap)[c] = U_Samaritan;
  for (char32 c = 0xA4D0; c <= 0xA4F7; ++c) (*smap)[c] = U_Lisu;
  for (char32 c = 0xA4F8; c <= 0xA4FD; ++c) (*smap)[c] = U_Lisu;
  for (char32 c = 0xA4FE; c <= 0xA4FF; ++c) (*smap)[c] = U_Lisu;
  for (char32 c = 0xA6A0; c <= 0xA6E5; ++c) (*smap)[c] = U_Bamum;
  for (char32 c = 0xA6E6; c <= 0xA6EF; ++c) (*smap)[c] = U_Bamum;
  for (char32 c = 0xA6F0; c <= 0xA6F1; ++c) (*smap)[c] = U_Bamum;
  for (char32 c = 0xA6F2; c <= 0xA6F7; ++c) (*smap)[c] = U_Bamum;
  for (char32 c = 0x16800; c <= 0x16A38; ++c) (*smap)[c] = U_Bamum;
  for (char32 c = 0xA980; c <= 0xA982; ++c) (*smap)[c] = U_Javanese;
  (*smap)[0xA983] = U_Javanese;
  for (char32 c = 0xA984; c <= 0xA9B2; ++c) (*smap)[c] = U_Javanese;
  (*smap)[0xA9B3] = U_Javanese;
  for (char32 c = 0xA9B4; c <= 0xA9B5; ++c) (*smap)[c] = U_Javanese;
  for (char32 c = 0xA9B6; c <= 0xA9B9; ++c) (*smap)[c] = U_Javanese;
  for (char32 c = 0xA9BA; c <= 0xA9BB; ++c) (*smap)[c] = U_Javanese;
  (*smap)[0xA9BC] = U_Javanese;
  for (char32 c = 0xA9BD; c <= 0xA9C0; ++c) (*smap)[c] = U_Javanese;
  for (char32 c = 0xA9C1; c <= 0xA9CD; ++c) (*smap)[c] = U_Javanese;
  for (char32 c = 0xA9D0; c <= 0xA9D9; ++c) (*smap)[c] = U_Javanese;
  for (char32 c = 0xA9DE; c <= 0xA9DF; ++c) (*smap)[c] = U_Javanese;
  for (char32 c = 0xAAE0; c <= 0xAAEA; ++c) (*smap)[c] = U_Meetei_Mayek;
  (*smap)[0xAAEB] = U_Meetei_Mayek;
  for (char32 c = 0xAAEC; c <= 0xAAED; ++c) (*smap)[c] = U_Meetei_Mayek;
  for (char32 c = 0xAAEE; c <= 0xAAEF; ++c) (*smap)[c] = U_Meetei_Mayek;
  for (char32 c = 0xAAF0; c <= 0xAAF1; ++c) (*smap)[c] = U_Meetei_Mayek;
  (*smap)[0xAAF2] = U_Meetei_Mayek;
  for (char32 c = 0xAAF3; c <= 0xAAF4; ++c) (*smap)[c] = U_Meetei_Mayek;
  (*smap)[0xAAF5] = U_Meetei_Mayek;
  (*smap)[0xAAF6] = U_Meetei_Mayek;
  for (char32 c = 0xABC0; c <= 0xABE2; ++c) (*smap)[c] = U_Meetei_Mayek;
  for (char32 c = 0xABE3; c <= 0xABE4; ++c) (*smap)[c] = U_Meetei_Mayek;
  (*smap)[0xABE5] = U_Meetei_Mayek;
  for (char32 c = 0xABE6; c <= 0xABE7; ++c) (*smap)[c] = U_Meetei_Mayek;
  (*smap)[0xABE8] = U_Meetei_Mayek;
  for (char32 c = 0xABE9; c <= 0xABEA; ++c) (*smap)[c] = U_Meetei_Mayek;
  (*smap)[0xABEB] = U_Meetei_Mayek;
  (*smap)[0xABEC] = U_Meetei_Mayek;
  (*smap)[0xABED] = U_Meetei_Mayek;
  for (char32 c = 0xABF0; c <= 0xABF9; ++c) (*smap)[c] = U_Meetei_Mayek;
  for (char32 c = 0x10840; c <= 0x10855; ++c) (*smap)[c] = U_Imperial_Aramaic;
  (*smap)[0x10857] = U_Imperial_Aramaic;
  for (char32 c = 0x10858; c <= 0x1085F; ++c) (*smap)[c] = U_Imperial_Aramaic;
  for (char32 c = 0x10A60; c <= 0x10A7C; ++c) (*smap)[c] = U_Old_South_Arabian;
  for (char32 c = 0x10A7D; c <= 0x10A7E; ++c) (*smap)[c] = U_Old_South_Arabian;
  (*smap)[0x10A7F] = U_Old_South_Arabian;
  for (char32 c = 0x10B40; c <= 0x10B55; ++c)
    (*smap)[c] = U_Inscriptional_Parthian;
  for (char32 c = 0x10B58; c <= 0x10B5F; ++c)
    (*smap)[c] = U_Inscriptional_Parthian;
  for (char32 c = 0x10B60; c <= 0x10B72; ++c)
    (*smap)[c] = U_Inscriptional_Pahlavi;
  for (char32 c = 0x10B78; c <= 0x10B7F; ++c)
    (*smap)[c] = U_Inscriptional_Pahlavi;
  for (char32 c = 0x10C00; c <= 0x10C48; ++c) (*smap)[c] = U_Old_Turkic;
  for (char32 c = 0x11080; c <= 0x11081; ++c) (*smap)[c] = U_Kaithi;
  (*smap)[0x11082] = U_Kaithi;
  for (char32 c = 0x11083; c <= 0x110AF; ++c) (*smap)[c] = U_Kaithi;
  for (char32 c = 0x110B0; c <= 0x110B2; ++c) (*smap)[c] = U_Kaithi;
  for (char32 c = 0x110B3; c <= 0x110B6; ++c) (*smap)[c] = U_Kaithi;
  for (char32 c = 0x110B7; c <= 0x110B8; ++c) (*smap)[c] = U_Kaithi;
  for (char32 c = 0x110B9; c <= 0x110BA; ++c) (*smap)[c] = U_Kaithi;
  for (char32 c = 0x110BB; c <= 0x110BC; ++c) (*smap)[c] = U_Kaithi;
  (*smap)[0x110BD] = U_Kaithi;
  for (char32 c = 0x110BE; c <= 0x110C1; ++c) (*smap)[c] = U_Kaithi;
  for (char32 c = 0x1BC0; c <= 0x1BE5; ++c) (*smap)[c] = U_Batak;
  (*smap)[0x1BE6] = U_Batak;
  (*smap)[0x1BE7] = U_Batak;
  for (char32 c = 0x1BE8; c <= 0x1BE9; ++c) (*smap)[c] = U_Batak;
  for (char32 c = 0x1BEA; c <= 0x1BEC; ++c) (*smap)[c] = U_Batak;
  (*smap)[0x1BED] = U_Batak;
  (*smap)[0x1BEE] = U_Batak;
  for (char32 c = 0x1BEF; c <= 0x1BF1; ++c) (*smap)[c] = U_Batak;
  for (char32 c = 0x1BF2; c <= 0x1BF3; ++c) (*smap)[c] = U_Batak;
  for (char32 c = 0x1BFC; c <= 0x1BFF; ++c) (*smap)[c] = U_Batak;
  (*smap)[0x11000] = U_Brahmi;
  (*smap)[0x11001] = U_Brahmi;
  (*smap)[0x11002] = U_Brahmi;
  for (char32 c = 0x11003; c <= 0x11037; ++c) (*smap)[c] = U_Brahmi;
  for (char32 c = 0x11038; c <= 0x11046; ++c) (*smap)[c] = U_Brahmi;
  for (char32 c = 0x11047; c <= 0x1104D; ++c) (*smap)[c] = U_Brahmi;
  for (char32 c = 0x11052; c <= 0x11065; ++c) (*smap)[c] = U_Brahmi;
  for (char32 c = 0x11066; c <= 0x1106F; ++c) (*smap)[c] = U_Brahmi;
  (*smap)[0x1107F] = U_Brahmi;
  for (char32 c = 0x0840; c <= 0x0858; ++c) (*smap)[c] = U_Mandaic;
  for (char32 c = 0x0859; c <= 0x085B; ++c) (*smap)[c] = U_Mandaic;
  (*smap)[0x085E] = U_Mandaic;
  for (char32 c = 0x11100; c <= 0x11102; ++c) (*smap)[c] = U_Chakma;
  for (char32 c = 0x11103; c <= 0x11126; ++c) (*smap)[c] = U_Chakma;
  for (char32 c = 0x11127; c <= 0x1112B; ++c) (*smap)[c] = U_Chakma;
  (*smap)[0x1112C] = U_Chakma;
  for (char32 c = 0x1112D; c <= 0x11134; ++c) (*smap)[c] = U_Chakma;
  for (char32 c = 0x11136; c <= 0x1113F; ++c) (*smap)[c] = U_Chakma;
  for (char32 c = 0x11140; c <= 0x11143; ++c) (*smap)[c] = U_Chakma;
  for (char32 c = 0x109A0; c <= 0x109B7; ++c) (*smap)[c] = U_Meroitic_Cursive;
  for (char32 c = 0x109BC; c <= 0x109BD; ++c) (*smap)[c] = U_Meroitic_Cursive;
  for (char32 c = 0x109BE; c <= 0x109BF; ++c) (*smap)[c] = U_Meroitic_Cursive;
  for (char32 c = 0x109C0; c <= 0x109CF; ++c) (*smap)[c] = U_Meroitic_Cursive;
  for (char32 c = 0x109D2; c <= 0x109FF; ++c) (*smap)[c] = U_Meroitic_Cursive;
  for (char32 c = 0x10980; c <= 0x1099F; ++c)
    (*smap)[c] = U_Meroitic_Hieroglyphs;
  for (char32 c = 0x16F00; c <= 0x16F44; ++c) (*smap)[c] = U_Miao;
  (*smap)[0x16F50] = U_Miao;
  for (char32 c = 0x16F51; c <= 0x16F7E; ++c) (*smap)[c] = U_Miao;
  for (char32 c = 0x16F8F; c <= 0x16F92; ++c) (*smap)[c] = U_Miao;
  for (char32 c = 0x16F93; c <= 0x16F9F; ++c) (*smap)[c] = U_Miao;
  for (char32 c = 0x11180; c <= 0x11181; ++c) (*smap)[c] = U_Sharada;
  (*smap)[0x11182] = U_Sharada;
  for (char32 c = 0x11183; c <= 0x111B2; ++c) (*smap)[c] = U_Sharada;
  for (char32 c = 0x111B3; c <= 0x111B5; ++c) (*smap)[c] = U_Sharada;
  for (char32 c = 0x111B6; c <= 0x111BE; ++c) (*smap)[c] = U_Sharada;
  for (char32 c = 0x111BF; c <= 0x111C0; ++c) (*smap)[c] = U_Sharada;
  for (char32 c = 0x111C1; c <= 0x111C4; ++c) (*smap)[c] = U_Sharada;
  for (char32 c = 0x111C5; c <= 0x111C9; ++c) (*smap)[c] = U_Sharada;
  for (char32 c = 0x111CA; c <= 0x111CC; ++c) (*smap)[c] = U_Sharada;
  (*smap)[0x111CD] = U_Sharada;
  for (char32 c = 0x111D0; c <= 0x111D9; ++c) (*smap)[c] = U_Sharada;
  (*smap)[0x111DA] = U_Sharada;
  (*smap)[0x111DB] = U_Sharada;
  (*smap)[0x111DC] = U_Sharada;
  for (char32 c = 0x111DD; c <= 0x111DF; ++c) (*smap)[c] = U_Sharada;
  for (char32 c = 0x110D0; c <= 0x110E8; ++c) (*smap)[c] = U_Sora_Sompeng;
  for (char32 c = 0x110F0; c <= 0x110F9; ++c) (*smap)[c] = U_Sora_Sompeng;
  for (char32 c = 0x11680; c <= 0x116AA; ++c) (*smap)[c] = U_Takri;
  (*smap)[0x116AB] = U_Takri;
  (*smap)[0x116AC] = U_Takri;
  (*smap)[0x116AD] = U_Takri;
  for (char32 c = 0x116AE; c <= 0x116AF; ++c) (*smap)[c] = U_Takri;
  for (char32 c = 0x116B0; c <= 0x116B5; ++c) (*smap)[c] = U_Takri;
  (*smap)[0x116B6] = U_Takri;
  (*smap)[0x116B7] = U_Takri;
  for (char32 c = 0x116C0; c <= 0x116C9; ++c) (*smap)[c] = U_Takri;
  for (char32 c = 0x10530; c <= 0x10563; ++c) (*smap)[c] = U_Caucasian_Albanian;
  (*smap)[0x1056F] = U_Caucasian_Albanian;
  for (char32 c = 0x16AD0; c <= 0x16AED; ++c) (*smap)[c] = U_Bassa_Vah;
  for (char32 c = 0x16AF0; c <= 0x16AF4; ++c) (*smap)[c] = U_Bassa_Vah;
  (*smap)[0x16AF5] = U_Bassa_Vah;
  for (char32 c = 0x1BC00; c <= 0x1BC6A; ++c) (*smap)[c] = U_Duployan;
  for (char32 c = 0x1BC70; c <= 0x1BC7C; ++c) (*smap)[c] = U_Duployan;
  for (char32 c = 0x1BC80; c <= 0x1BC88; ++c) (*smap)[c] = U_Duployan;
  for (char32 c = 0x1BC90; c <= 0x1BC99; ++c) (*smap)[c] = U_Duployan;
  (*smap)[0x1BC9C] = U_Duployan;
  for (char32 c = 0x1BC9D; c <= 0x1BC9E; ++c) (*smap)[c] = U_Duployan;
  (*smap)[0x1BC9F] = U_Duployan;
  for (char32 c = 0x10500; c <= 0x10527; ++c) (*smap)[c] = U_Elbasan;
  for (char32 c = 0x11300; c <= 0x11301; ++c) (*smap)[c] = U_Grantha;
  for (char32 c = 0x11302; c <= 0x11303; ++c) (*smap)[c] = U_Grantha;
  for (char32 c = 0x11305; c <= 0x1130C; ++c) (*smap)[c] = U_Grantha;
  for (char32 c = 0x1130F; c <= 0x11310; ++c) (*smap)[c] = U_Grantha;
  for (char32 c = 0x11313; c <= 0x11328; ++c) (*smap)[c] = U_Grantha;
  for (char32 c = 0x1132A; c <= 0x11330; ++c) (*smap)[c] = U_Grantha;
  for (char32 c = 0x11332; c <= 0x11333; ++c) (*smap)[c] = U_Grantha;
  for (char32 c = 0x11335; c <= 0x11339; ++c) (*smap)[c] = U_Grantha;
  (*smap)[0x1133C] = U_Grantha;
  (*smap)[0x1133D] = U_Grantha;
  for (char32 c = 0x1133E; c <= 0x1133F; ++c) (*smap)[c] = U_Grantha;
  (*smap)[0x11340] = U_Grantha;
  for (char32 c = 0x11341; c <= 0x11344; ++c) (*smap)[c] = U_Grantha;
  for (char32 c = 0x11347; c <= 0x11348; ++c) (*smap)[c] = U_Grantha;
  for (char32 c = 0x1134B; c <= 0x1134D; ++c) (*smap)[c] = U_Grantha;
  (*smap)[0x11350] = U_Grantha;
  (*smap)[0x11357] = U_Grantha;
  for (char32 c = 0x1135D; c <= 0x11361; ++c) (*smap)[c] = U_Grantha;
  for (char32 c = 0x11362; c <= 0x11363; ++c) (*smap)[c] = U_Grantha;
  for (char32 c = 0x11366; c <= 0x1136C; ++c) (*smap)[c] = U_Grantha;
  for (char32 c = 0x11370; c <= 0x11374; ++c) (*smap)[c] = U_Grantha;
  for (char32 c = 0x16B00; c <= 0x16B2F; ++c) (*smap)[c] = U_Pahawh_Hmong;
  for (char32 c = 0x16B30; c <= 0x16B36; ++c) (*smap)[c] = U_Pahawh_Hmong;
  for (char32 c = 0x16B37; c <= 0x16B3B; ++c) (*smap)[c] = U_Pahawh_Hmong;
  for (char32 c = 0x16B3C; c <= 0x16B3F; ++c) (*smap)[c] = U_Pahawh_Hmong;
  for (char32 c = 0x16B40; c <= 0x16B43; ++c) (*smap)[c] = U_Pahawh_Hmong;
  (*smap)[0x16B44] = U_Pahawh_Hmong;
  (*smap)[0x16B45] = U_Pahawh_Hmong;
  for (char32 c = 0x16B50; c <= 0x16B59; ++c) (*smap)[c] = U_Pahawh_Hmong;
  for (char32 c = 0x16B5B; c <= 0x16B61; ++c) (*smap)[c] = U_Pahawh_Hmong;
  for (char32 c = 0x16B63; c <= 0x16B77; ++c) (*smap)[c] = U_Pahawh_Hmong;
  for (char32 c = 0x16B7D; c <= 0x16B8F; ++c) (*smap)[c] = U_Pahawh_Hmong;
  for (char32 c = 0x11200; c <= 0x11211; ++c) (*smap)[c] = U_Khojki;
  for (char32 c = 0x11213; c <= 0x1122B; ++c) (*smap)[c] = U_Khojki;
  for (char32 c = 0x1122C; c <= 0x1122E; ++c) (*smap)[c] = U_Khojki;
  for (char32 c = 0x1122F; c <= 0x11231; ++c) (*smap)[c] = U_Khojki;
  for (char32 c = 0x11232; c <= 0x11233; ++c) (*smap)[c] = U_Khojki;
  (*smap)[0x11234] = U_Khojki;
  (*smap)[0x11235] = U_Khojki;
  for (char32 c = 0x11236; c <= 0x11237; ++c) (*smap)[c] = U_Khojki;
  for (char32 c = 0x11238; c <= 0x1123D; ++c) (*smap)[c] = U_Khojki;
  (*smap)[0x1123E] = U_Khojki;
  for (char32 c = 0x10600; c <= 0x10736; ++c) (*smap)[c] = U_Linear_A;
  for (char32 c = 0x10740; c <= 0x10755; ++c) (*smap)[c] = U_Linear_A;
  for (char32 c = 0x10760; c <= 0x10767; ++c) (*smap)[c] = U_Linear_A;
  for (char32 c = 0x11150; c <= 0x11172; ++c) (*smap)[c] = U_Mahajani;
  (*smap)[0x11173] = U_Mahajani;
  for (char32 c = 0x11174; c <= 0x11175; ++c) (*smap)[c] = U_Mahajani;
  (*smap)[0x11176] = U_Mahajani;
  for (char32 c = 0x10AC0; c <= 0x10AC7; ++c) (*smap)[c] = U_Manichaean;
  (*smap)[0x10AC8] = U_Manichaean;
  for (char32 c = 0x10AC9; c <= 0x10AE4; ++c) (*smap)[c] = U_Manichaean;
  for (char32 c = 0x10AE5; c <= 0x10AE6; ++c) (*smap)[c] = U_Manichaean;
  for (char32 c = 0x10AEB; c <= 0x10AEF; ++c) (*smap)[c] = U_Manichaean;
  for (char32 c = 0x10AF0; c <= 0x10AF6; ++c) (*smap)[c] = U_Manichaean;
  for (char32 c = 0x1E800; c <= 0x1E8C4; ++c) (*smap)[c] = U_Mende_Kikakui;
  for (char32 c = 0x1E8C7; c <= 0x1E8CF; ++c) (*smap)[c] = U_Mende_Kikakui;
  for (char32 c = 0x1E8D0; c <= 0x1E8D6; ++c) (*smap)[c] = U_Mende_Kikakui;
  for (char32 c = 0x11600; c <= 0x1162F; ++c) (*smap)[c] = U_Modi;
  for (char32 c = 0x11630; c <= 0x11632; ++c) (*smap)[c] = U_Modi;
  for (char32 c = 0x11633; c <= 0x1163A; ++c) (*smap)[c] = U_Modi;
  for (char32 c = 0x1163B; c <= 0x1163C; ++c) (*smap)[c] = U_Modi;
  (*smap)[0x1163D] = U_Modi;
  (*smap)[0x1163E] = U_Modi;
  for (char32 c = 0x1163F; c <= 0x11640; ++c) (*smap)[c] = U_Modi;
  for (char32 c = 0x11641; c <= 0x11643; ++c) (*smap)[c] = U_Modi;
  (*smap)[0x11644] = U_Modi;
  for (char32 c = 0x11650; c <= 0x11659; ++c) (*smap)[c] = U_Modi;
  for (char32 c = 0x16A40; c <= 0x16A5E; ++c) (*smap)[c] = U_Mro;
  for (char32 c = 0x16A60; c <= 0x16A69; ++c) (*smap)[c] = U_Mro;
  for (char32 c = 0x16A6E; c <= 0x16A6F; ++c) (*smap)[c] = U_Mro;
  for (char32 c = 0x10A80; c <= 0x10A9C; ++c) (*smap)[c] = U_Old_North_Arabian;
  for (char32 c = 0x10A9D; c <= 0x10A9F; ++c) (*smap)[c] = U_Old_North_Arabian;
  for (char32 c = 0x10880; c <= 0x1089E; ++c) (*smap)[c] = U_Nabataean;
  for (char32 c = 0x108A7; c <= 0x108AF; ++c) (*smap)[c] = U_Nabataean;
  for (char32 c = 0x10860; c <= 0x10876; ++c) (*smap)[c] = U_Palmyrene;
  for (char32 c = 0x10877; c <= 0x10878; ++c) (*smap)[c] = U_Palmyrene;
  for (char32 c = 0x10879; c <= 0x1087F; ++c) (*smap)[c] = U_Palmyrene;
  for (char32 c = 0x11AC0; c <= 0x11AF8; ++c) (*smap)[c] = U_Pau_Cin_Hau;
  for (char32 c = 0x10350; c <= 0x10375; ++c) (*smap)[c] = U_Old_Permic;
  for (char32 c = 0x10376; c <= 0x1037A; ++c) (*smap)[c] = U_Old_Permic;
  for (char32 c = 0x10B80; c <= 0x10B91; ++c) (*smap)[c] = U_Psalter_Pahlavi;
  for (char32 c = 0x10B99; c <= 0x10B9C; ++c) (*smap)[c] = U_Psalter_Pahlavi;
  for (char32 c = 0x10BA9; c <= 0x10BAF; ++c) (*smap)[c] = U_Psalter_Pahlavi;
  for (char32 c = 0x11580; c <= 0x115AE; ++c) (*smap)[c] = U_Siddham;
  for (char32 c = 0x115AF; c <= 0x115B1; ++c) (*smap)[c] = U_Siddham;
  for (char32 c = 0x115B2; c <= 0x115B5; ++c) (*smap)[c] = U_Siddham;
  for (char32 c = 0x115B8; c <= 0x115BB; ++c) (*smap)[c] = U_Siddham;
  for (char32 c = 0x115BC; c <= 0x115BD; ++c) (*smap)[c] = U_Siddham;
  (*smap)[0x115BE] = U_Siddham;
  for (char32 c = 0x115BF; c <= 0x115C0; ++c) (*smap)[c] = U_Siddham;
  for (char32 c = 0x115C1; c <= 0x115D7; ++c) (*smap)[c] = U_Siddham;
  for (char32 c = 0x115D8; c <= 0x115DB; ++c) (*smap)[c] = U_Siddham;
  for (char32 c = 0x115DC; c <= 0x115DD; ++c) (*smap)[c] = U_Siddham;
  for (char32 c = 0x112B0; c <= 0x112DE; ++c) (*smap)[c] = U_Khudawadi;
  (*smap)[0x112DF] = U_Khudawadi;
  for (char32 c = 0x112E0; c <= 0x112E2; ++c) (*smap)[c] = U_Khudawadi;
  for (char32 c = 0x112E3; c <= 0x112EA; ++c) (*smap)[c] = U_Khudawadi;
  for (char32 c = 0x112F0; c <= 0x112F9; ++c) (*smap)[c] = U_Khudawadi;
  for (char32 c = 0x11480; c <= 0x114AF; ++c) (*smap)[c] = U_Tirhuta;
  for (char32 c = 0x114B0; c <= 0x114B2; ++c) (*smap)[c] = U_Tirhuta;
  for (char32 c = 0x114B3; c <= 0x114B8; ++c) (*smap)[c] = U_Tirhuta;
  (*smap)[0x114B9] = U_Tirhuta;
  (*smap)[0x114BA] = U_Tirhuta;
  for (char32 c = 0x114BB; c <= 0x114BE; ++c) (*smap)[c] = U_Tirhuta;
  for (char32 c = 0x114BF; c <= 0x114C0; ++c) (*smap)[c] = U_Tirhuta;
  (*smap)[0x114C1] = U_Tirhuta;
  for (char32 c = 0x114C2; c <= 0x114C3; ++c) (*smap)[c] = U_Tirhuta;
  for (char32 c = 0x114C4; c <= 0x114C5; ++c) (*smap)[c] = U_Tirhuta;
  (*smap)[0x114C6] = U_Tirhuta;
  (*smap)[0x114C7] = U_Tirhuta;
  for (char32 c = 0x114D0; c <= 0x114D9; ++c) (*smap)[c] = U_Tirhuta;
  for (char32 c = 0x118A0; c <= 0x118DF; ++c) (*smap)[c] = U_Warang_Citi;
  for (char32 c = 0x118E0; c <= 0x118E9; ++c) (*smap)[c] = U_Warang_Citi;
  for (char32 c = 0x118EA; c <= 0x118F2; ++c) (*smap)[c] = U_Warang_Citi;
  (*smap)[0x118FF] = U_Warang_Citi;
  for (char32 c = 0x11700; c <= 0x11719; ++c) (*smap)[c] = U_Ahom;
  for (char32 c = 0x1171D; c <= 0x1171F; ++c) (*smap)[c] = U_Ahom;
  for (char32 c = 0x11720; c <= 0x11721; ++c) (*smap)[c] = U_Ahom;
  for (char32 c = 0x11722; c <= 0x11725; ++c) (*smap)[c] = U_Ahom;
  (*smap)[0x11726] = U_Ahom;
  for (char32 c = 0x11727; c <= 0x1172B; ++c) (*smap)[c] = U_Ahom;
  for (char32 c = 0x11730; c <= 0x11739; ++c) (*smap)[c] = U_Ahom;
  for (char32 c = 0x1173A; c <= 0x1173B; ++c) (*smap)[c] = U_Ahom;
  for (char32 c = 0x1173C; c <= 0x1173E; ++c) (*smap)[c] = U_Ahom;
  (*smap)[0x1173F] = U_Ahom;
  for (char32 c = 0x14400; c <= 0x14646; ++c)
    (*smap)[c] = U_Anatolian_Hieroglyphs;
  for (char32 c = 0x108E0; c <= 0x108F2; ++c) (*smap)[c] = U_Hatran;
  for (char32 c = 0x108F4; c <= 0x108F5; ++c) (*smap)[c] = U_Hatran;
  for (char32 c = 0x108FB; c <= 0x108FF; ++c) (*smap)[c] = U_Hatran;
  for (char32 c = 0x11280; c <= 0x11286; ++c) (*smap)[c] = U_Multani;
  (*smap)[0x11288] = U_Multani;
  for (char32 c = 0x1128A; c <= 0x1128D; ++c) (*smap)[c] = U_Multani;
  for (char32 c = 0x1128F; c <= 0x1129D; ++c) (*smap)[c] = U_Multani;
  for (char32 c = 0x1129F; c <= 0x112A8; ++c) (*smap)[c] = U_Multani;
  (*smap)[0x112A9] = U_Multani;
  for (char32 c = 0x10C80; c <= 0x10CB2; ++c) (*smap)[c] = U_Old_Hungarian;
  for (char32 c = 0x10CC0; c <= 0x10CF2; ++c) (*smap)[c] = U_Old_Hungarian;
  for (char32 c = 0x10CFA; c <= 0x10CFF; ++c) (*smap)[c] = U_Old_Hungarian;
  for (char32 c = 0x1D800; c <= 0x1D9FF; ++c) (*smap)[c] = U_SignWriting;
  for (char32 c = 0x1DA00; c <= 0x1DA36; ++c) (*smap)[c] = U_SignWriting;
  for (char32 c = 0x1DA37; c <= 0x1DA3A; ++c) (*smap)[c] = U_SignWriting;
  for (char32 c = 0x1DA3B; c <= 0x1DA6C; ++c) (*smap)[c] = U_SignWriting;
  for (char32 c = 0x1DA6D; c <= 0x1DA74; ++c) (*smap)[c] = U_SignWriting;
  (*smap)[0x1DA75] = U_SignWriting;
  for (char32 c = 0x1DA76; c <= 0x1DA83; ++c) (*smap)[c] = U_SignWriting;
  (*smap)[0x1DA84] = U_SignWriting;
  for (char32 c = 0x1DA85; c <= 0x1DA86; ++c) (*smap)[c] = U_SignWriting;
  for (char32 c = 0x1DA87; c <= 0x1DA8B; ++c) (*smap)[c] = U_SignWriting;
  for (char32 c = 0x1DA9B; c <= 0x1DA9F; ++c) (*smap)[c] = U_SignWriting;
  for (char32 c = 0x1DAA1; c <= 0x1DAAF; ++c) (*smap)[c] = U_SignWriting;
  for (char32 c = 0x1E900; c <= 0x1E943; ++c) (*smap)[c] = U_Adlam;
  for (char32 c = 0x1E944; c <= 0x1E94A; ++c) (*smap)[c] = U_Adlam;
  for (char32 c = 0x1E950; c <= 0x1E959; ++c) (*smap)[c] = U_Adlam;
  for (char32 c = 0x1E95E; c <= 0x1E95F; ++c) (*smap)[c] = U_Adlam;
  for (char32 c = 0x11C00; c <= 0x11C08; ++c) (*smap)[c] = U_Bhaiksuki;
  for (char32 c = 0x11C0A; c <= 0x11C2E; ++c) (*smap)[c] = U_Bhaiksuki;
  (*smap)[0x11C2F] = U_Bhaiksuki;
  for (char32 c = 0x11C30; c <= 0x11C36; ++c) (*smap)[c] = U_Bhaiksuki;
  for (char32 c = 0x11C38; c <= 0x11C3D; ++c) (*smap)[c] = U_Bhaiksuki;
  (*smap)[0x11C3E] = U_Bhaiksuki;
  (*smap)[0x11C3F] = U_Bhaiksuki;
  (*smap)[0x11C40] = U_Bhaiksuki;
  for (char32 c = 0x11C41; c <= 0x11C45; ++c) (*smap)[c] = U_Bhaiksuki;
  for (char32 c = 0x11C50; c <= 0x11C59; ++c) (*smap)[c] = U_Bhaiksuki;
  for (char32 c = 0x11C5A; c <= 0x11C6C; ++c) (*smap)[c] = U_Bhaiksuki;
  for (char32 c = 0x11C70; c <= 0x11C71; ++c) (*smap)[c] = U_Marchen;
  for (char32 c = 0x11C72; c <= 0x11C8F; ++c) (*smap)[c] = U_Marchen;
  for (char32 c = 0x11C92; c <= 0x11CA7; ++c) (*smap)[c] = U_Marchen;
  (*smap)[0x11CA9] = U_Marchen;
  for (char32 c = 0x11CAA; c <= 0x11CB0; ++c) (*smap)[c] = U_Marchen;
  (*smap)[0x11CB1] = U_Marchen;
  for (char32 c = 0x11CB2; c <= 0x11CB3; ++c) (*smap)[c] = U_Marchen;
  (*smap)[0x11CB4] = U_Marchen;
  for (char32 c = 0x11CB5; c <= 0x11CB6; ++c) (*smap)[c] = U_Marchen;
  for (char32 c = 0x11400; c <= 0x11434; ++c) (*smap)[c] = U_Newa;
  for (char32 c = 0x11435; c <= 0x11437; ++c) (*smap)[c] = U_Newa;
  for (char32 c = 0x11438; c <= 0x1143F; ++c) (*smap)[c] = U_Newa;
  for (char32 c = 0x11440; c <= 0x11441; ++c) (*smap)[c] = U_Newa;
  for (char32 c = 0x11442; c <= 0x11444; ++c) (*smap)[c] = U_Newa;
  (*smap)[0x11445] = U_Newa;
  (*smap)[0x11446] = U_Newa;
  for (char32 c = 0x11447; c <= 0x1144A; ++c) (*smap)[c] = U_Newa;
  for (char32 c = 0x1144B; c <= 0x1144F; ++c) (*smap)[c] = U_Newa;
  for (char32 c = 0x11450; c <= 0x11459; ++c) (*smap)[c] = U_Newa;
  (*smap)[0x1145B] = U_Newa;
  (*smap)[0x1145D] = U_Newa;
  for (char32 c = 0x104B0; c <= 0x104D3; ++c) (*smap)[c] = U_Osage;
  for (char32 c = 0x104D8; c <= 0x104FB; ++c) (*smap)[c] = U_Osage;
  (*smap)[0x16FE0] = U_Tangut;
  for (char32 c = 0x17000; c <= 0x187EC; ++c) (*smap)[c] = U_Tangut;
  for (char32 c = 0x18800; c <= 0x18AF2; ++c) (*smap)[c] = U_Tangut;
}
}  // namespace
}  // namespace unicode_script
}  // namespace sentencepiece
#endif  // UNICODE_SCRIPT_DATA_H_
