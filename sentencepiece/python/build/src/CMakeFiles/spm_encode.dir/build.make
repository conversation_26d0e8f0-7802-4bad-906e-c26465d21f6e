# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Documents/log-anomaly/sentencepiece

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Documents/log-anomaly/sentencepiece/python/build

# Include any dependencies generated for this target.
include src/CMakeFiles/spm_encode.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include src/CMakeFiles/spm_encode.dir/compiler_depend.make

# Include the progress variables for this target.
include src/CMakeFiles/spm_encode.dir/progress.make

# Include the compile flags for this target's objects.
include src/CMakeFiles/spm_encode.dir/flags.make

src/CMakeFiles/spm_encode.dir/codegen:
.PHONY : src/CMakeFiles/spm_encode.dir/codegen

src/CMakeFiles/spm_encode.dir/spm_encode_main.cc.o: src/CMakeFiles/spm_encode.dir/flags.make
src/CMakeFiles/spm_encode.dir/spm_encode_main.cc.o: /home/<USER>/Documents/log-anomaly/sentencepiece/src/spm_encode_main.cc
src/CMakeFiles/spm_encode.dir/spm_encode_main.cc.o: src/CMakeFiles/spm_encode.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/python/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object src/CMakeFiles/spm_encode.dir/spm_encode_main.cc.o"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/spm_encode.dir/spm_encode_main.cc.o -MF CMakeFiles/spm_encode.dir/spm_encode_main.cc.o.d -o CMakeFiles/spm_encode.dir/spm_encode_main.cc.o -c /home/<USER>/Documents/log-anomaly/sentencepiece/src/spm_encode_main.cc

src/CMakeFiles/spm_encode.dir/spm_encode_main.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/spm_encode.dir/spm_encode_main.cc.i"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/log-anomaly/sentencepiece/src/spm_encode_main.cc > CMakeFiles/spm_encode.dir/spm_encode_main.cc.i

src/CMakeFiles/spm_encode.dir/spm_encode_main.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/spm_encode.dir/spm_encode_main.cc.s"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/log-anomaly/sentencepiece/src/spm_encode_main.cc -o CMakeFiles/spm_encode.dir/spm_encode_main.cc.s

# Object files for target spm_encode
spm_encode_OBJECTS = \
"CMakeFiles/spm_encode.dir/spm_encode_main.cc.o"

# External object files for target spm_encode
spm_encode_EXTERNAL_OBJECTS =

src/spm_encode: src/CMakeFiles/spm_encode.dir/spm_encode_main.cc.o
src/spm_encode: src/CMakeFiles/spm_encode.dir/build.make
src/spm_encode: src/CMakeFiles/spm_encode.dir/compiler_depend.ts
src/spm_encode: src/libsentencepiece.a
src/spm_encode: /usr/lib/libtcmalloc_minimal.so
src/spm_encode: src/CMakeFiles/spm_encode.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/python/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable spm_encode"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build/src && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/spm_encode.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
src/CMakeFiles/spm_encode.dir/build: src/spm_encode
.PHONY : src/CMakeFiles/spm_encode.dir/build

src/CMakeFiles/spm_encode.dir/clean:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build/src && $(CMAKE_COMMAND) -P CMakeFiles/spm_encode.dir/cmake_clean.cmake
.PHONY : src/CMakeFiles/spm_encode.dir/clean

src/CMakeFiles/spm_encode.dir/depend:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Documents/log-anomaly/sentencepiece /home/<USER>/Documents/log-anomaly/sentencepiece/src /home/<USER>/Documents/log-anomaly/sentencepiece/python/build /home/<USER>/Documents/log-anomaly/sentencepiece/python/build/src /home/<USER>/Documents/log-anomaly/sentencepiece/python/build/src/CMakeFiles/spm_encode.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : src/CMakeFiles/spm_encode.dir/depend

