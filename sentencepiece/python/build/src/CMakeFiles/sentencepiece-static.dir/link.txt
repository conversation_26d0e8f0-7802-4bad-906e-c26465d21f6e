/usr/bin/ar qc libsentencepiece.a "CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/arena.cc.o" "CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/arenastring.cc.o" "CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/bytestream.cc.o" "CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/coded_stream.cc.o" "CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/common.cc.o" "CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/extension_set.cc.o" "CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/generated_enum_util.cc.o" "CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/generated_message_table_driven_lite.cc.o" "CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/generated_message_util.cc.o" "CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/implicit_weak_message.cc.o" "CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/int128.cc.o" "CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/io_win32.cc.o" "CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/message_lite.cc.o" "CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/parse_context.cc.o" "CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/repeated_field.cc.o" "CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/status.cc.o" "CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/statusor.cc.o" "CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/stringpiece.cc.o" "CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/stringprintf.cc.o" "CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/structurally_valid.cc.o" "CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/strutil.cc.o" "CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/time.cc.o" "CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/wire_format_lite.cc.o" "CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/zero_copy_stream.cc.o" "CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/zero_copy_stream_impl.cc.o" "CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/zero_copy_stream_impl_lite.cc.o" "CMakeFiles/sentencepiece-static.dir/builtin_pb/sentencepiece.pb.cc.o" "CMakeFiles/sentencepiece-static.dir/builtin_pb/sentencepiece_model.pb.cc.o" "CMakeFiles/sentencepiece-static.dir/bpe_model.cc.o" "CMakeFiles/sentencepiece-static.dir/char_model.cc.o" "CMakeFiles/sentencepiece-static.dir/error.cc.o" "CMakeFiles/sentencepiece-static.dir/filesystem.cc.o" "CMakeFiles/sentencepiece-static.dir/model_factory.cc.o" "CMakeFiles/sentencepiece-static.dir/model_interface.cc.o" "CMakeFiles/sentencepiece-static.dir/normalizer.cc.o" "CMakeFiles/sentencepiece-static.dir/sentencepiece_processor.cc.o" "CMakeFiles/sentencepiece-static.dir/unigram_model.cc.o" "CMakeFiles/sentencepiece-static.dir/util.cc.o" "CMakeFiles/sentencepiece-static.dir/word_model.cc.o" "CMakeFiles/sentencepiece-static.dir/__/third_party/absl/flags/flag.cc.o"
/usr/bin/ranlib libsentencepiece.a
