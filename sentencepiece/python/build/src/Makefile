# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Documents/log-anomaly/sentencepiece

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Documents/log-anomaly/sentencepiece/python/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target package
package: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Run CPack packaging tool..."
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && /usr/bin/cpack --config ./CPackConfig.cmake
.PHONY : package

# Special rule for the target package
package/fast: package
.PHONY : package/fast

# Special rule for the target package_source
package_source:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Run CPack packaging tool for source..."
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && /usr/bin/cpack --config ./CPackSourceConfig.cmake /home/<USER>/Documents/log-anomaly/sentencepiece/python/build/CPackSourceConfig.cmake
.PHONY : package_source

# Special rule for the target package_source
package_source/fast: package_source
.PHONY : package_source/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/usr/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Documents/log-anomaly/sentencepiece/python/build/CMakeFiles /home/<USER>/Documents/log-anomaly/sentencepiece/python/build/src//CMakeFiles/progress.marks
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Documents/log-anomaly/sentencepiece/python/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
src/CMakeFiles/sentencepiece-static.dir/rule:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/CMakeFiles/sentencepiece-static.dir/rule
.PHONY : src/CMakeFiles/sentencepiece-static.dir/rule

# Convenience name for target.
sentencepiece-static: src/CMakeFiles/sentencepiece-static.dir/rule
.PHONY : sentencepiece-static

# fast build rule for target.
sentencepiece-static/fast:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/build
.PHONY : sentencepiece-static/fast

# Convenience name for target.
src/CMakeFiles/sentencepiece_train-static.dir/rule:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/CMakeFiles/sentencepiece_train-static.dir/rule
.PHONY : src/CMakeFiles/sentencepiece_train-static.dir/rule

# Convenience name for target.
sentencepiece_train-static: src/CMakeFiles/sentencepiece_train-static.dir/rule
.PHONY : sentencepiece_train-static

# fast build rule for target.
sentencepiece_train-static/fast:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece_train-static.dir/build.make src/CMakeFiles/sentencepiece_train-static.dir/build
.PHONY : sentencepiece_train-static/fast

# Convenience name for target.
src/CMakeFiles/spm_encode.dir/rule:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/CMakeFiles/spm_encode.dir/rule
.PHONY : src/CMakeFiles/spm_encode.dir/rule

# Convenience name for target.
spm_encode: src/CMakeFiles/spm_encode.dir/rule
.PHONY : spm_encode

# fast build rule for target.
spm_encode/fast:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/spm_encode.dir/build.make src/CMakeFiles/spm_encode.dir/build
.PHONY : spm_encode/fast

# Convenience name for target.
src/CMakeFiles/spm_decode.dir/rule:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/CMakeFiles/spm_decode.dir/rule
.PHONY : src/CMakeFiles/spm_decode.dir/rule

# Convenience name for target.
spm_decode: src/CMakeFiles/spm_decode.dir/rule
.PHONY : spm_decode

# fast build rule for target.
spm_decode/fast:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/spm_decode.dir/build.make src/CMakeFiles/spm_decode.dir/build
.PHONY : spm_decode/fast

# Convenience name for target.
src/CMakeFiles/spm_normalize.dir/rule:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/CMakeFiles/spm_normalize.dir/rule
.PHONY : src/CMakeFiles/spm_normalize.dir/rule

# Convenience name for target.
spm_normalize: src/CMakeFiles/spm_normalize.dir/rule
.PHONY : spm_normalize

# fast build rule for target.
spm_normalize/fast:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/spm_normalize.dir/build.make src/CMakeFiles/spm_normalize.dir/build
.PHONY : spm_normalize/fast

# Convenience name for target.
src/CMakeFiles/spm_train.dir/rule:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/CMakeFiles/spm_train.dir/rule
.PHONY : src/CMakeFiles/spm_train.dir/rule

# Convenience name for target.
spm_train: src/CMakeFiles/spm_train.dir/rule
.PHONY : spm_train

# fast build rule for target.
spm_train/fast:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/spm_train.dir/build.make src/CMakeFiles/spm_train.dir/build
.PHONY : spm_train/fast

# Convenience name for target.
src/CMakeFiles/spm_export_vocab.dir/rule:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/CMakeFiles/spm_export_vocab.dir/rule
.PHONY : src/CMakeFiles/spm_export_vocab.dir/rule

# Convenience name for target.
spm_export_vocab: src/CMakeFiles/spm_export_vocab.dir/rule
.PHONY : spm_export_vocab

# fast build rule for target.
spm_export_vocab/fast:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/spm_export_vocab.dir/build.make src/CMakeFiles/spm_export_vocab.dir/build
.PHONY : spm_export_vocab/fast

__/third_party/absl/flags/flag.o: __/third_party/absl/flags/flag.cc.o
.PHONY : __/third_party/absl/flags/flag.o

# target to build an object file
__/third_party/absl/flags/flag.cc.o:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/absl/flags/flag.cc.o
.PHONY : __/third_party/absl/flags/flag.cc.o

__/third_party/absl/flags/flag.i: __/third_party/absl/flags/flag.cc.i
.PHONY : __/third_party/absl/flags/flag.i

# target to preprocess a source file
__/third_party/absl/flags/flag.cc.i:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/absl/flags/flag.cc.i
.PHONY : __/third_party/absl/flags/flag.cc.i

__/third_party/absl/flags/flag.s: __/third_party/absl/flags/flag.cc.s
.PHONY : __/third_party/absl/flags/flag.s

# target to generate assembly for a file
__/third_party/absl/flags/flag.cc.s:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/absl/flags/flag.cc.s
.PHONY : __/third_party/absl/flags/flag.cc.s

__/third_party/protobuf-lite/arena.o: __/third_party/protobuf-lite/arena.cc.o
.PHONY : __/third_party/protobuf-lite/arena.o

# target to build an object file
__/third_party/protobuf-lite/arena.cc.o:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/arena.cc.o
.PHONY : __/third_party/protobuf-lite/arena.cc.o

__/third_party/protobuf-lite/arena.i: __/third_party/protobuf-lite/arena.cc.i
.PHONY : __/third_party/protobuf-lite/arena.i

# target to preprocess a source file
__/third_party/protobuf-lite/arena.cc.i:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/arena.cc.i
.PHONY : __/third_party/protobuf-lite/arena.cc.i

__/third_party/protobuf-lite/arena.s: __/third_party/protobuf-lite/arena.cc.s
.PHONY : __/third_party/protobuf-lite/arena.s

# target to generate assembly for a file
__/third_party/protobuf-lite/arena.cc.s:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/arena.cc.s
.PHONY : __/third_party/protobuf-lite/arena.cc.s

__/third_party/protobuf-lite/arenastring.o: __/third_party/protobuf-lite/arenastring.cc.o
.PHONY : __/third_party/protobuf-lite/arenastring.o

# target to build an object file
__/third_party/protobuf-lite/arenastring.cc.o:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/arenastring.cc.o
.PHONY : __/third_party/protobuf-lite/arenastring.cc.o

__/third_party/protobuf-lite/arenastring.i: __/third_party/protobuf-lite/arenastring.cc.i
.PHONY : __/third_party/protobuf-lite/arenastring.i

# target to preprocess a source file
__/third_party/protobuf-lite/arenastring.cc.i:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/arenastring.cc.i
.PHONY : __/third_party/protobuf-lite/arenastring.cc.i

__/third_party/protobuf-lite/arenastring.s: __/third_party/protobuf-lite/arenastring.cc.s
.PHONY : __/third_party/protobuf-lite/arenastring.s

# target to generate assembly for a file
__/third_party/protobuf-lite/arenastring.cc.s:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/arenastring.cc.s
.PHONY : __/third_party/protobuf-lite/arenastring.cc.s

__/third_party/protobuf-lite/bytestream.o: __/third_party/protobuf-lite/bytestream.cc.o
.PHONY : __/third_party/protobuf-lite/bytestream.o

# target to build an object file
__/third_party/protobuf-lite/bytestream.cc.o:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/bytestream.cc.o
.PHONY : __/third_party/protobuf-lite/bytestream.cc.o

__/third_party/protobuf-lite/bytestream.i: __/third_party/protobuf-lite/bytestream.cc.i
.PHONY : __/third_party/protobuf-lite/bytestream.i

# target to preprocess a source file
__/third_party/protobuf-lite/bytestream.cc.i:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/bytestream.cc.i
.PHONY : __/third_party/protobuf-lite/bytestream.cc.i

__/third_party/protobuf-lite/bytestream.s: __/third_party/protobuf-lite/bytestream.cc.s
.PHONY : __/third_party/protobuf-lite/bytestream.s

# target to generate assembly for a file
__/third_party/protobuf-lite/bytestream.cc.s:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/bytestream.cc.s
.PHONY : __/third_party/protobuf-lite/bytestream.cc.s

__/third_party/protobuf-lite/coded_stream.o: __/third_party/protobuf-lite/coded_stream.cc.o
.PHONY : __/third_party/protobuf-lite/coded_stream.o

# target to build an object file
__/third_party/protobuf-lite/coded_stream.cc.o:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/coded_stream.cc.o
.PHONY : __/third_party/protobuf-lite/coded_stream.cc.o

__/third_party/protobuf-lite/coded_stream.i: __/third_party/protobuf-lite/coded_stream.cc.i
.PHONY : __/third_party/protobuf-lite/coded_stream.i

# target to preprocess a source file
__/third_party/protobuf-lite/coded_stream.cc.i:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/coded_stream.cc.i
.PHONY : __/third_party/protobuf-lite/coded_stream.cc.i

__/third_party/protobuf-lite/coded_stream.s: __/third_party/protobuf-lite/coded_stream.cc.s
.PHONY : __/third_party/protobuf-lite/coded_stream.s

# target to generate assembly for a file
__/third_party/protobuf-lite/coded_stream.cc.s:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/coded_stream.cc.s
.PHONY : __/third_party/protobuf-lite/coded_stream.cc.s

__/third_party/protobuf-lite/common.o: __/third_party/protobuf-lite/common.cc.o
.PHONY : __/third_party/protobuf-lite/common.o

# target to build an object file
__/third_party/protobuf-lite/common.cc.o:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/common.cc.o
.PHONY : __/third_party/protobuf-lite/common.cc.o

__/third_party/protobuf-lite/common.i: __/third_party/protobuf-lite/common.cc.i
.PHONY : __/third_party/protobuf-lite/common.i

# target to preprocess a source file
__/third_party/protobuf-lite/common.cc.i:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/common.cc.i
.PHONY : __/third_party/protobuf-lite/common.cc.i

__/third_party/protobuf-lite/common.s: __/third_party/protobuf-lite/common.cc.s
.PHONY : __/third_party/protobuf-lite/common.s

# target to generate assembly for a file
__/third_party/protobuf-lite/common.cc.s:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/common.cc.s
.PHONY : __/third_party/protobuf-lite/common.cc.s

__/third_party/protobuf-lite/extension_set.o: __/third_party/protobuf-lite/extension_set.cc.o
.PHONY : __/third_party/protobuf-lite/extension_set.o

# target to build an object file
__/third_party/protobuf-lite/extension_set.cc.o:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/extension_set.cc.o
.PHONY : __/third_party/protobuf-lite/extension_set.cc.o

__/third_party/protobuf-lite/extension_set.i: __/third_party/protobuf-lite/extension_set.cc.i
.PHONY : __/third_party/protobuf-lite/extension_set.i

# target to preprocess a source file
__/third_party/protobuf-lite/extension_set.cc.i:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/extension_set.cc.i
.PHONY : __/third_party/protobuf-lite/extension_set.cc.i

__/third_party/protobuf-lite/extension_set.s: __/third_party/protobuf-lite/extension_set.cc.s
.PHONY : __/third_party/protobuf-lite/extension_set.s

# target to generate assembly for a file
__/third_party/protobuf-lite/extension_set.cc.s:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/extension_set.cc.s
.PHONY : __/third_party/protobuf-lite/extension_set.cc.s

__/third_party/protobuf-lite/generated_enum_util.o: __/third_party/protobuf-lite/generated_enum_util.cc.o
.PHONY : __/third_party/protobuf-lite/generated_enum_util.o

# target to build an object file
__/third_party/protobuf-lite/generated_enum_util.cc.o:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/generated_enum_util.cc.o
.PHONY : __/third_party/protobuf-lite/generated_enum_util.cc.o

__/third_party/protobuf-lite/generated_enum_util.i: __/third_party/protobuf-lite/generated_enum_util.cc.i
.PHONY : __/third_party/protobuf-lite/generated_enum_util.i

# target to preprocess a source file
__/third_party/protobuf-lite/generated_enum_util.cc.i:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/generated_enum_util.cc.i
.PHONY : __/third_party/protobuf-lite/generated_enum_util.cc.i

__/third_party/protobuf-lite/generated_enum_util.s: __/third_party/protobuf-lite/generated_enum_util.cc.s
.PHONY : __/third_party/protobuf-lite/generated_enum_util.s

# target to generate assembly for a file
__/third_party/protobuf-lite/generated_enum_util.cc.s:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/generated_enum_util.cc.s
.PHONY : __/third_party/protobuf-lite/generated_enum_util.cc.s

__/third_party/protobuf-lite/generated_message_table_driven_lite.o: __/third_party/protobuf-lite/generated_message_table_driven_lite.cc.o
.PHONY : __/third_party/protobuf-lite/generated_message_table_driven_lite.o

# target to build an object file
__/third_party/protobuf-lite/generated_message_table_driven_lite.cc.o:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/generated_message_table_driven_lite.cc.o
.PHONY : __/third_party/protobuf-lite/generated_message_table_driven_lite.cc.o

__/third_party/protobuf-lite/generated_message_table_driven_lite.i: __/third_party/protobuf-lite/generated_message_table_driven_lite.cc.i
.PHONY : __/third_party/protobuf-lite/generated_message_table_driven_lite.i

# target to preprocess a source file
__/third_party/protobuf-lite/generated_message_table_driven_lite.cc.i:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/generated_message_table_driven_lite.cc.i
.PHONY : __/third_party/protobuf-lite/generated_message_table_driven_lite.cc.i

__/third_party/protobuf-lite/generated_message_table_driven_lite.s: __/third_party/protobuf-lite/generated_message_table_driven_lite.cc.s
.PHONY : __/third_party/protobuf-lite/generated_message_table_driven_lite.s

# target to generate assembly for a file
__/third_party/protobuf-lite/generated_message_table_driven_lite.cc.s:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/generated_message_table_driven_lite.cc.s
.PHONY : __/third_party/protobuf-lite/generated_message_table_driven_lite.cc.s

__/third_party/protobuf-lite/generated_message_util.o: __/third_party/protobuf-lite/generated_message_util.cc.o
.PHONY : __/third_party/protobuf-lite/generated_message_util.o

# target to build an object file
__/third_party/protobuf-lite/generated_message_util.cc.o:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/generated_message_util.cc.o
.PHONY : __/third_party/protobuf-lite/generated_message_util.cc.o

__/third_party/protobuf-lite/generated_message_util.i: __/third_party/protobuf-lite/generated_message_util.cc.i
.PHONY : __/third_party/protobuf-lite/generated_message_util.i

# target to preprocess a source file
__/third_party/protobuf-lite/generated_message_util.cc.i:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/generated_message_util.cc.i
.PHONY : __/third_party/protobuf-lite/generated_message_util.cc.i

__/third_party/protobuf-lite/generated_message_util.s: __/third_party/protobuf-lite/generated_message_util.cc.s
.PHONY : __/third_party/protobuf-lite/generated_message_util.s

# target to generate assembly for a file
__/third_party/protobuf-lite/generated_message_util.cc.s:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/generated_message_util.cc.s
.PHONY : __/third_party/protobuf-lite/generated_message_util.cc.s

__/third_party/protobuf-lite/implicit_weak_message.o: __/third_party/protobuf-lite/implicit_weak_message.cc.o
.PHONY : __/third_party/protobuf-lite/implicit_weak_message.o

# target to build an object file
__/third_party/protobuf-lite/implicit_weak_message.cc.o:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/implicit_weak_message.cc.o
.PHONY : __/third_party/protobuf-lite/implicit_weak_message.cc.o

__/third_party/protobuf-lite/implicit_weak_message.i: __/third_party/protobuf-lite/implicit_weak_message.cc.i
.PHONY : __/third_party/protobuf-lite/implicit_weak_message.i

# target to preprocess a source file
__/third_party/protobuf-lite/implicit_weak_message.cc.i:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/implicit_weak_message.cc.i
.PHONY : __/third_party/protobuf-lite/implicit_weak_message.cc.i

__/third_party/protobuf-lite/implicit_weak_message.s: __/third_party/protobuf-lite/implicit_weak_message.cc.s
.PHONY : __/third_party/protobuf-lite/implicit_weak_message.s

# target to generate assembly for a file
__/third_party/protobuf-lite/implicit_weak_message.cc.s:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/implicit_weak_message.cc.s
.PHONY : __/third_party/protobuf-lite/implicit_weak_message.cc.s

__/third_party/protobuf-lite/int128.o: __/third_party/protobuf-lite/int128.cc.o
.PHONY : __/third_party/protobuf-lite/int128.o

# target to build an object file
__/third_party/protobuf-lite/int128.cc.o:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/int128.cc.o
.PHONY : __/third_party/protobuf-lite/int128.cc.o

__/third_party/protobuf-lite/int128.i: __/third_party/protobuf-lite/int128.cc.i
.PHONY : __/third_party/protobuf-lite/int128.i

# target to preprocess a source file
__/third_party/protobuf-lite/int128.cc.i:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/int128.cc.i
.PHONY : __/third_party/protobuf-lite/int128.cc.i

__/third_party/protobuf-lite/int128.s: __/third_party/protobuf-lite/int128.cc.s
.PHONY : __/third_party/protobuf-lite/int128.s

# target to generate assembly for a file
__/third_party/protobuf-lite/int128.cc.s:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/int128.cc.s
.PHONY : __/third_party/protobuf-lite/int128.cc.s

__/third_party/protobuf-lite/io_win32.o: __/third_party/protobuf-lite/io_win32.cc.o
.PHONY : __/third_party/protobuf-lite/io_win32.o

# target to build an object file
__/third_party/protobuf-lite/io_win32.cc.o:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/io_win32.cc.o
.PHONY : __/third_party/protobuf-lite/io_win32.cc.o

__/third_party/protobuf-lite/io_win32.i: __/third_party/protobuf-lite/io_win32.cc.i
.PHONY : __/third_party/protobuf-lite/io_win32.i

# target to preprocess a source file
__/third_party/protobuf-lite/io_win32.cc.i:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/io_win32.cc.i
.PHONY : __/third_party/protobuf-lite/io_win32.cc.i

__/third_party/protobuf-lite/io_win32.s: __/third_party/protobuf-lite/io_win32.cc.s
.PHONY : __/third_party/protobuf-lite/io_win32.s

# target to generate assembly for a file
__/third_party/protobuf-lite/io_win32.cc.s:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/io_win32.cc.s
.PHONY : __/third_party/protobuf-lite/io_win32.cc.s

__/third_party/protobuf-lite/message_lite.o: __/third_party/protobuf-lite/message_lite.cc.o
.PHONY : __/third_party/protobuf-lite/message_lite.o

# target to build an object file
__/third_party/protobuf-lite/message_lite.cc.o:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/message_lite.cc.o
.PHONY : __/third_party/protobuf-lite/message_lite.cc.o

__/third_party/protobuf-lite/message_lite.i: __/third_party/protobuf-lite/message_lite.cc.i
.PHONY : __/third_party/protobuf-lite/message_lite.i

# target to preprocess a source file
__/third_party/protobuf-lite/message_lite.cc.i:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/message_lite.cc.i
.PHONY : __/third_party/protobuf-lite/message_lite.cc.i

__/third_party/protobuf-lite/message_lite.s: __/third_party/protobuf-lite/message_lite.cc.s
.PHONY : __/third_party/protobuf-lite/message_lite.s

# target to generate assembly for a file
__/third_party/protobuf-lite/message_lite.cc.s:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/message_lite.cc.s
.PHONY : __/third_party/protobuf-lite/message_lite.cc.s

__/third_party/protobuf-lite/parse_context.o: __/third_party/protobuf-lite/parse_context.cc.o
.PHONY : __/third_party/protobuf-lite/parse_context.o

# target to build an object file
__/third_party/protobuf-lite/parse_context.cc.o:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/parse_context.cc.o
.PHONY : __/third_party/protobuf-lite/parse_context.cc.o

__/third_party/protobuf-lite/parse_context.i: __/third_party/protobuf-lite/parse_context.cc.i
.PHONY : __/third_party/protobuf-lite/parse_context.i

# target to preprocess a source file
__/third_party/protobuf-lite/parse_context.cc.i:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/parse_context.cc.i
.PHONY : __/third_party/protobuf-lite/parse_context.cc.i

__/third_party/protobuf-lite/parse_context.s: __/third_party/protobuf-lite/parse_context.cc.s
.PHONY : __/third_party/protobuf-lite/parse_context.s

# target to generate assembly for a file
__/third_party/protobuf-lite/parse_context.cc.s:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/parse_context.cc.s
.PHONY : __/third_party/protobuf-lite/parse_context.cc.s

__/third_party/protobuf-lite/repeated_field.o: __/third_party/protobuf-lite/repeated_field.cc.o
.PHONY : __/third_party/protobuf-lite/repeated_field.o

# target to build an object file
__/third_party/protobuf-lite/repeated_field.cc.o:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/repeated_field.cc.o
.PHONY : __/third_party/protobuf-lite/repeated_field.cc.o

__/third_party/protobuf-lite/repeated_field.i: __/third_party/protobuf-lite/repeated_field.cc.i
.PHONY : __/third_party/protobuf-lite/repeated_field.i

# target to preprocess a source file
__/third_party/protobuf-lite/repeated_field.cc.i:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/repeated_field.cc.i
.PHONY : __/third_party/protobuf-lite/repeated_field.cc.i

__/third_party/protobuf-lite/repeated_field.s: __/third_party/protobuf-lite/repeated_field.cc.s
.PHONY : __/third_party/protobuf-lite/repeated_field.s

# target to generate assembly for a file
__/third_party/protobuf-lite/repeated_field.cc.s:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/repeated_field.cc.s
.PHONY : __/third_party/protobuf-lite/repeated_field.cc.s

__/third_party/protobuf-lite/status.o: __/third_party/protobuf-lite/status.cc.o
.PHONY : __/third_party/protobuf-lite/status.o

# target to build an object file
__/third_party/protobuf-lite/status.cc.o:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/status.cc.o
.PHONY : __/third_party/protobuf-lite/status.cc.o

__/third_party/protobuf-lite/status.i: __/third_party/protobuf-lite/status.cc.i
.PHONY : __/third_party/protobuf-lite/status.i

# target to preprocess a source file
__/third_party/protobuf-lite/status.cc.i:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/status.cc.i
.PHONY : __/third_party/protobuf-lite/status.cc.i

__/third_party/protobuf-lite/status.s: __/third_party/protobuf-lite/status.cc.s
.PHONY : __/third_party/protobuf-lite/status.s

# target to generate assembly for a file
__/third_party/protobuf-lite/status.cc.s:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/status.cc.s
.PHONY : __/third_party/protobuf-lite/status.cc.s

__/third_party/protobuf-lite/statusor.o: __/third_party/protobuf-lite/statusor.cc.o
.PHONY : __/third_party/protobuf-lite/statusor.o

# target to build an object file
__/third_party/protobuf-lite/statusor.cc.o:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/statusor.cc.o
.PHONY : __/third_party/protobuf-lite/statusor.cc.o

__/third_party/protobuf-lite/statusor.i: __/third_party/protobuf-lite/statusor.cc.i
.PHONY : __/third_party/protobuf-lite/statusor.i

# target to preprocess a source file
__/third_party/protobuf-lite/statusor.cc.i:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/statusor.cc.i
.PHONY : __/third_party/protobuf-lite/statusor.cc.i

__/third_party/protobuf-lite/statusor.s: __/third_party/protobuf-lite/statusor.cc.s
.PHONY : __/third_party/protobuf-lite/statusor.s

# target to generate assembly for a file
__/third_party/protobuf-lite/statusor.cc.s:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/statusor.cc.s
.PHONY : __/third_party/protobuf-lite/statusor.cc.s

__/third_party/protobuf-lite/stringpiece.o: __/third_party/protobuf-lite/stringpiece.cc.o
.PHONY : __/third_party/protobuf-lite/stringpiece.o

# target to build an object file
__/third_party/protobuf-lite/stringpiece.cc.o:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/stringpiece.cc.o
.PHONY : __/third_party/protobuf-lite/stringpiece.cc.o

__/third_party/protobuf-lite/stringpiece.i: __/third_party/protobuf-lite/stringpiece.cc.i
.PHONY : __/third_party/protobuf-lite/stringpiece.i

# target to preprocess a source file
__/third_party/protobuf-lite/stringpiece.cc.i:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/stringpiece.cc.i
.PHONY : __/third_party/protobuf-lite/stringpiece.cc.i

__/third_party/protobuf-lite/stringpiece.s: __/third_party/protobuf-lite/stringpiece.cc.s
.PHONY : __/third_party/protobuf-lite/stringpiece.s

# target to generate assembly for a file
__/third_party/protobuf-lite/stringpiece.cc.s:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/stringpiece.cc.s
.PHONY : __/third_party/protobuf-lite/stringpiece.cc.s

__/third_party/protobuf-lite/stringprintf.o: __/third_party/protobuf-lite/stringprintf.cc.o
.PHONY : __/third_party/protobuf-lite/stringprintf.o

# target to build an object file
__/third_party/protobuf-lite/stringprintf.cc.o:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/stringprintf.cc.o
.PHONY : __/third_party/protobuf-lite/stringprintf.cc.o

__/third_party/protobuf-lite/stringprintf.i: __/third_party/protobuf-lite/stringprintf.cc.i
.PHONY : __/third_party/protobuf-lite/stringprintf.i

# target to preprocess a source file
__/third_party/protobuf-lite/stringprintf.cc.i:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/stringprintf.cc.i
.PHONY : __/third_party/protobuf-lite/stringprintf.cc.i

__/third_party/protobuf-lite/stringprintf.s: __/third_party/protobuf-lite/stringprintf.cc.s
.PHONY : __/third_party/protobuf-lite/stringprintf.s

# target to generate assembly for a file
__/third_party/protobuf-lite/stringprintf.cc.s:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/stringprintf.cc.s
.PHONY : __/third_party/protobuf-lite/stringprintf.cc.s

__/third_party/protobuf-lite/structurally_valid.o: __/third_party/protobuf-lite/structurally_valid.cc.o
.PHONY : __/third_party/protobuf-lite/structurally_valid.o

# target to build an object file
__/third_party/protobuf-lite/structurally_valid.cc.o:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/structurally_valid.cc.o
.PHONY : __/third_party/protobuf-lite/structurally_valid.cc.o

__/third_party/protobuf-lite/structurally_valid.i: __/third_party/protobuf-lite/structurally_valid.cc.i
.PHONY : __/third_party/protobuf-lite/structurally_valid.i

# target to preprocess a source file
__/third_party/protobuf-lite/structurally_valid.cc.i:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/structurally_valid.cc.i
.PHONY : __/third_party/protobuf-lite/structurally_valid.cc.i

__/third_party/protobuf-lite/structurally_valid.s: __/third_party/protobuf-lite/structurally_valid.cc.s
.PHONY : __/third_party/protobuf-lite/structurally_valid.s

# target to generate assembly for a file
__/third_party/protobuf-lite/structurally_valid.cc.s:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/structurally_valid.cc.s
.PHONY : __/third_party/protobuf-lite/structurally_valid.cc.s

__/third_party/protobuf-lite/strutil.o: __/third_party/protobuf-lite/strutil.cc.o
.PHONY : __/third_party/protobuf-lite/strutil.o

# target to build an object file
__/third_party/protobuf-lite/strutil.cc.o:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/strutil.cc.o
.PHONY : __/third_party/protobuf-lite/strutil.cc.o

__/third_party/protobuf-lite/strutil.i: __/third_party/protobuf-lite/strutil.cc.i
.PHONY : __/third_party/protobuf-lite/strutil.i

# target to preprocess a source file
__/third_party/protobuf-lite/strutil.cc.i:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/strutil.cc.i
.PHONY : __/third_party/protobuf-lite/strutil.cc.i

__/third_party/protobuf-lite/strutil.s: __/third_party/protobuf-lite/strutil.cc.s
.PHONY : __/third_party/protobuf-lite/strutil.s

# target to generate assembly for a file
__/third_party/protobuf-lite/strutil.cc.s:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/strutil.cc.s
.PHONY : __/third_party/protobuf-lite/strutil.cc.s

__/third_party/protobuf-lite/time.o: __/third_party/protobuf-lite/time.cc.o
.PHONY : __/third_party/protobuf-lite/time.o

# target to build an object file
__/third_party/protobuf-lite/time.cc.o:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/time.cc.o
.PHONY : __/third_party/protobuf-lite/time.cc.o

__/third_party/protobuf-lite/time.i: __/third_party/protobuf-lite/time.cc.i
.PHONY : __/third_party/protobuf-lite/time.i

# target to preprocess a source file
__/third_party/protobuf-lite/time.cc.i:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/time.cc.i
.PHONY : __/third_party/protobuf-lite/time.cc.i

__/third_party/protobuf-lite/time.s: __/third_party/protobuf-lite/time.cc.s
.PHONY : __/third_party/protobuf-lite/time.s

# target to generate assembly for a file
__/third_party/protobuf-lite/time.cc.s:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/time.cc.s
.PHONY : __/third_party/protobuf-lite/time.cc.s

__/third_party/protobuf-lite/wire_format_lite.o: __/third_party/protobuf-lite/wire_format_lite.cc.o
.PHONY : __/third_party/protobuf-lite/wire_format_lite.o

# target to build an object file
__/third_party/protobuf-lite/wire_format_lite.cc.o:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/wire_format_lite.cc.o
.PHONY : __/third_party/protobuf-lite/wire_format_lite.cc.o

__/third_party/protobuf-lite/wire_format_lite.i: __/third_party/protobuf-lite/wire_format_lite.cc.i
.PHONY : __/third_party/protobuf-lite/wire_format_lite.i

# target to preprocess a source file
__/third_party/protobuf-lite/wire_format_lite.cc.i:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/wire_format_lite.cc.i
.PHONY : __/third_party/protobuf-lite/wire_format_lite.cc.i

__/third_party/protobuf-lite/wire_format_lite.s: __/third_party/protobuf-lite/wire_format_lite.cc.s
.PHONY : __/third_party/protobuf-lite/wire_format_lite.s

# target to generate assembly for a file
__/third_party/protobuf-lite/wire_format_lite.cc.s:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/wire_format_lite.cc.s
.PHONY : __/third_party/protobuf-lite/wire_format_lite.cc.s

__/third_party/protobuf-lite/zero_copy_stream.o: __/third_party/protobuf-lite/zero_copy_stream.cc.o
.PHONY : __/third_party/protobuf-lite/zero_copy_stream.o

# target to build an object file
__/third_party/protobuf-lite/zero_copy_stream.cc.o:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/zero_copy_stream.cc.o
.PHONY : __/third_party/protobuf-lite/zero_copy_stream.cc.o

__/third_party/protobuf-lite/zero_copy_stream.i: __/third_party/protobuf-lite/zero_copy_stream.cc.i
.PHONY : __/third_party/protobuf-lite/zero_copy_stream.i

# target to preprocess a source file
__/third_party/protobuf-lite/zero_copy_stream.cc.i:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/zero_copy_stream.cc.i
.PHONY : __/third_party/protobuf-lite/zero_copy_stream.cc.i

__/third_party/protobuf-lite/zero_copy_stream.s: __/third_party/protobuf-lite/zero_copy_stream.cc.s
.PHONY : __/third_party/protobuf-lite/zero_copy_stream.s

# target to generate assembly for a file
__/third_party/protobuf-lite/zero_copy_stream.cc.s:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/zero_copy_stream.cc.s
.PHONY : __/third_party/protobuf-lite/zero_copy_stream.cc.s

__/third_party/protobuf-lite/zero_copy_stream_impl.o: __/third_party/protobuf-lite/zero_copy_stream_impl.cc.o
.PHONY : __/third_party/protobuf-lite/zero_copy_stream_impl.o

# target to build an object file
__/third_party/protobuf-lite/zero_copy_stream_impl.cc.o:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/zero_copy_stream_impl.cc.o
.PHONY : __/third_party/protobuf-lite/zero_copy_stream_impl.cc.o

__/third_party/protobuf-lite/zero_copy_stream_impl.i: __/third_party/protobuf-lite/zero_copy_stream_impl.cc.i
.PHONY : __/third_party/protobuf-lite/zero_copy_stream_impl.i

# target to preprocess a source file
__/third_party/protobuf-lite/zero_copy_stream_impl.cc.i:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/zero_copy_stream_impl.cc.i
.PHONY : __/third_party/protobuf-lite/zero_copy_stream_impl.cc.i

__/third_party/protobuf-lite/zero_copy_stream_impl.s: __/third_party/protobuf-lite/zero_copy_stream_impl.cc.s
.PHONY : __/third_party/protobuf-lite/zero_copy_stream_impl.s

# target to generate assembly for a file
__/third_party/protobuf-lite/zero_copy_stream_impl.cc.s:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/zero_copy_stream_impl.cc.s
.PHONY : __/third_party/protobuf-lite/zero_copy_stream_impl.cc.s

__/third_party/protobuf-lite/zero_copy_stream_impl_lite.o: __/third_party/protobuf-lite/zero_copy_stream_impl_lite.cc.o
.PHONY : __/third_party/protobuf-lite/zero_copy_stream_impl_lite.o

# target to build an object file
__/third_party/protobuf-lite/zero_copy_stream_impl_lite.cc.o:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/zero_copy_stream_impl_lite.cc.o
.PHONY : __/third_party/protobuf-lite/zero_copy_stream_impl_lite.cc.o

__/third_party/protobuf-lite/zero_copy_stream_impl_lite.i: __/third_party/protobuf-lite/zero_copy_stream_impl_lite.cc.i
.PHONY : __/third_party/protobuf-lite/zero_copy_stream_impl_lite.i

# target to preprocess a source file
__/third_party/protobuf-lite/zero_copy_stream_impl_lite.cc.i:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/zero_copy_stream_impl_lite.cc.i
.PHONY : __/third_party/protobuf-lite/zero_copy_stream_impl_lite.cc.i

__/third_party/protobuf-lite/zero_copy_stream_impl_lite.s: __/third_party/protobuf-lite/zero_copy_stream_impl_lite.cc.s
.PHONY : __/third_party/protobuf-lite/zero_copy_stream_impl_lite.s

# target to generate assembly for a file
__/third_party/protobuf-lite/zero_copy_stream_impl_lite.cc.s:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/__/third_party/protobuf-lite/zero_copy_stream_impl_lite.cc.s
.PHONY : __/third_party/protobuf-lite/zero_copy_stream_impl_lite.cc.s

bpe_model.o: bpe_model.cc.o
.PHONY : bpe_model.o

# target to build an object file
bpe_model.cc.o:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/bpe_model.cc.o
.PHONY : bpe_model.cc.o

bpe_model.i: bpe_model.cc.i
.PHONY : bpe_model.i

# target to preprocess a source file
bpe_model.cc.i:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/bpe_model.cc.i
.PHONY : bpe_model.cc.i

bpe_model.s: bpe_model.cc.s
.PHONY : bpe_model.s

# target to generate assembly for a file
bpe_model.cc.s:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/bpe_model.cc.s
.PHONY : bpe_model.cc.s

bpe_model_trainer.o: bpe_model_trainer.cc.o
.PHONY : bpe_model_trainer.o

# target to build an object file
bpe_model_trainer.cc.o:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece_train-static.dir/build.make src/CMakeFiles/sentencepiece_train-static.dir/bpe_model_trainer.cc.o
.PHONY : bpe_model_trainer.cc.o

bpe_model_trainer.i: bpe_model_trainer.cc.i
.PHONY : bpe_model_trainer.i

# target to preprocess a source file
bpe_model_trainer.cc.i:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece_train-static.dir/build.make src/CMakeFiles/sentencepiece_train-static.dir/bpe_model_trainer.cc.i
.PHONY : bpe_model_trainer.cc.i

bpe_model_trainer.s: bpe_model_trainer.cc.s
.PHONY : bpe_model_trainer.s

# target to generate assembly for a file
bpe_model_trainer.cc.s:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece_train-static.dir/build.make src/CMakeFiles/sentencepiece_train-static.dir/bpe_model_trainer.cc.s
.PHONY : bpe_model_trainer.cc.s

builder.o: builder.cc.o
.PHONY : builder.o

# target to build an object file
builder.cc.o:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece_train-static.dir/build.make src/CMakeFiles/sentencepiece_train-static.dir/builder.cc.o
.PHONY : builder.cc.o

builder.i: builder.cc.i
.PHONY : builder.i

# target to preprocess a source file
builder.cc.i:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece_train-static.dir/build.make src/CMakeFiles/sentencepiece_train-static.dir/builder.cc.i
.PHONY : builder.cc.i

builder.s: builder.cc.s
.PHONY : builder.s

# target to generate assembly for a file
builder.cc.s:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece_train-static.dir/build.make src/CMakeFiles/sentencepiece_train-static.dir/builder.cc.s
.PHONY : builder.cc.s

builtin_pb/sentencepiece.pb.o: builtin_pb/sentencepiece.pb.cc.o
.PHONY : builtin_pb/sentencepiece.pb.o

# target to build an object file
builtin_pb/sentencepiece.pb.cc.o:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/builtin_pb/sentencepiece.pb.cc.o
.PHONY : builtin_pb/sentencepiece.pb.cc.o

builtin_pb/sentencepiece.pb.i: builtin_pb/sentencepiece.pb.cc.i
.PHONY : builtin_pb/sentencepiece.pb.i

# target to preprocess a source file
builtin_pb/sentencepiece.pb.cc.i:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/builtin_pb/sentencepiece.pb.cc.i
.PHONY : builtin_pb/sentencepiece.pb.cc.i

builtin_pb/sentencepiece.pb.s: builtin_pb/sentencepiece.pb.cc.s
.PHONY : builtin_pb/sentencepiece.pb.s

# target to generate assembly for a file
builtin_pb/sentencepiece.pb.cc.s:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/builtin_pb/sentencepiece.pb.cc.s
.PHONY : builtin_pb/sentencepiece.pb.cc.s

builtin_pb/sentencepiece_model.pb.o: builtin_pb/sentencepiece_model.pb.cc.o
.PHONY : builtin_pb/sentencepiece_model.pb.o

# target to build an object file
builtin_pb/sentencepiece_model.pb.cc.o:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/builtin_pb/sentencepiece_model.pb.cc.o
.PHONY : builtin_pb/sentencepiece_model.pb.cc.o

builtin_pb/sentencepiece_model.pb.i: builtin_pb/sentencepiece_model.pb.cc.i
.PHONY : builtin_pb/sentencepiece_model.pb.i

# target to preprocess a source file
builtin_pb/sentencepiece_model.pb.cc.i:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/builtin_pb/sentencepiece_model.pb.cc.i
.PHONY : builtin_pb/sentencepiece_model.pb.cc.i

builtin_pb/sentencepiece_model.pb.s: builtin_pb/sentencepiece_model.pb.cc.s
.PHONY : builtin_pb/sentencepiece_model.pb.s

# target to generate assembly for a file
builtin_pb/sentencepiece_model.pb.cc.s:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/builtin_pb/sentencepiece_model.pb.cc.s
.PHONY : builtin_pb/sentencepiece_model.pb.cc.s

char_model.o: char_model.cc.o
.PHONY : char_model.o

# target to build an object file
char_model.cc.o:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/char_model.cc.o
.PHONY : char_model.cc.o

char_model.i: char_model.cc.i
.PHONY : char_model.i

# target to preprocess a source file
char_model.cc.i:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/char_model.cc.i
.PHONY : char_model.cc.i

char_model.s: char_model.cc.s
.PHONY : char_model.s

# target to generate assembly for a file
char_model.cc.s:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/char_model.cc.s
.PHONY : char_model.cc.s

char_model_trainer.o: char_model_trainer.cc.o
.PHONY : char_model_trainer.o

# target to build an object file
char_model_trainer.cc.o:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece_train-static.dir/build.make src/CMakeFiles/sentencepiece_train-static.dir/char_model_trainer.cc.o
.PHONY : char_model_trainer.cc.o

char_model_trainer.i: char_model_trainer.cc.i
.PHONY : char_model_trainer.i

# target to preprocess a source file
char_model_trainer.cc.i:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece_train-static.dir/build.make src/CMakeFiles/sentencepiece_train-static.dir/char_model_trainer.cc.i
.PHONY : char_model_trainer.cc.i

char_model_trainer.s: char_model_trainer.cc.s
.PHONY : char_model_trainer.s

# target to generate assembly for a file
char_model_trainer.cc.s:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece_train-static.dir/build.make src/CMakeFiles/sentencepiece_train-static.dir/char_model_trainer.cc.s
.PHONY : char_model_trainer.cc.s

error.o: error.cc.o
.PHONY : error.o

# target to build an object file
error.cc.o:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/error.cc.o
.PHONY : error.cc.o

error.i: error.cc.i
.PHONY : error.i

# target to preprocess a source file
error.cc.i:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/error.cc.i
.PHONY : error.cc.i

error.s: error.cc.s
.PHONY : error.s

# target to generate assembly for a file
error.cc.s:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/error.cc.s
.PHONY : error.cc.s

filesystem.o: filesystem.cc.o
.PHONY : filesystem.o

# target to build an object file
filesystem.cc.o:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/filesystem.cc.o
.PHONY : filesystem.cc.o

filesystem.i: filesystem.cc.i
.PHONY : filesystem.i

# target to preprocess a source file
filesystem.cc.i:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/filesystem.cc.i
.PHONY : filesystem.cc.i

filesystem.s: filesystem.cc.s
.PHONY : filesystem.s

# target to generate assembly for a file
filesystem.cc.s:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/filesystem.cc.s
.PHONY : filesystem.cc.s

model_factory.o: model_factory.cc.o
.PHONY : model_factory.o

# target to build an object file
model_factory.cc.o:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/model_factory.cc.o
.PHONY : model_factory.cc.o

model_factory.i: model_factory.cc.i
.PHONY : model_factory.i

# target to preprocess a source file
model_factory.cc.i:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/model_factory.cc.i
.PHONY : model_factory.cc.i

model_factory.s: model_factory.cc.s
.PHONY : model_factory.s

# target to generate assembly for a file
model_factory.cc.s:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/model_factory.cc.s
.PHONY : model_factory.cc.s

model_interface.o: model_interface.cc.o
.PHONY : model_interface.o

# target to build an object file
model_interface.cc.o:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/model_interface.cc.o
.PHONY : model_interface.cc.o

model_interface.i: model_interface.cc.i
.PHONY : model_interface.i

# target to preprocess a source file
model_interface.cc.i:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/model_interface.cc.i
.PHONY : model_interface.cc.i

model_interface.s: model_interface.cc.s
.PHONY : model_interface.s

# target to generate assembly for a file
model_interface.cc.s:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/model_interface.cc.s
.PHONY : model_interface.cc.s

normalizer.o: normalizer.cc.o
.PHONY : normalizer.o

# target to build an object file
normalizer.cc.o:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/normalizer.cc.o
.PHONY : normalizer.cc.o

normalizer.i: normalizer.cc.i
.PHONY : normalizer.i

# target to preprocess a source file
normalizer.cc.i:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/normalizer.cc.i
.PHONY : normalizer.cc.i

normalizer.s: normalizer.cc.s
.PHONY : normalizer.s

# target to generate assembly for a file
normalizer.cc.s:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/normalizer.cc.s
.PHONY : normalizer.cc.s

pretokenizer_for_training.o: pretokenizer_for_training.cc.o
.PHONY : pretokenizer_for_training.o

# target to build an object file
pretokenizer_for_training.cc.o:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece_train-static.dir/build.make src/CMakeFiles/sentencepiece_train-static.dir/pretokenizer_for_training.cc.o
.PHONY : pretokenizer_for_training.cc.o

pretokenizer_for_training.i: pretokenizer_for_training.cc.i
.PHONY : pretokenizer_for_training.i

# target to preprocess a source file
pretokenizer_for_training.cc.i:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece_train-static.dir/build.make src/CMakeFiles/sentencepiece_train-static.dir/pretokenizer_for_training.cc.i
.PHONY : pretokenizer_for_training.cc.i

pretokenizer_for_training.s: pretokenizer_for_training.cc.s
.PHONY : pretokenizer_for_training.s

# target to generate assembly for a file
pretokenizer_for_training.cc.s:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece_train-static.dir/build.make src/CMakeFiles/sentencepiece_train-static.dir/pretokenizer_for_training.cc.s
.PHONY : pretokenizer_for_training.cc.s

sentencepiece_processor.o: sentencepiece_processor.cc.o
.PHONY : sentencepiece_processor.o

# target to build an object file
sentencepiece_processor.cc.o:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/sentencepiece_processor.cc.o
.PHONY : sentencepiece_processor.cc.o

sentencepiece_processor.i: sentencepiece_processor.cc.i
.PHONY : sentencepiece_processor.i

# target to preprocess a source file
sentencepiece_processor.cc.i:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/sentencepiece_processor.cc.i
.PHONY : sentencepiece_processor.cc.i

sentencepiece_processor.s: sentencepiece_processor.cc.s
.PHONY : sentencepiece_processor.s

# target to generate assembly for a file
sentencepiece_processor.cc.s:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/sentencepiece_processor.cc.s
.PHONY : sentencepiece_processor.cc.s

sentencepiece_trainer.o: sentencepiece_trainer.cc.o
.PHONY : sentencepiece_trainer.o

# target to build an object file
sentencepiece_trainer.cc.o:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece_train-static.dir/build.make src/CMakeFiles/sentencepiece_train-static.dir/sentencepiece_trainer.cc.o
.PHONY : sentencepiece_trainer.cc.o

sentencepiece_trainer.i: sentencepiece_trainer.cc.i
.PHONY : sentencepiece_trainer.i

# target to preprocess a source file
sentencepiece_trainer.cc.i:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece_train-static.dir/build.make src/CMakeFiles/sentencepiece_train-static.dir/sentencepiece_trainer.cc.i
.PHONY : sentencepiece_trainer.cc.i

sentencepiece_trainer.s: sentencepiece_trainer.cc.s
.PHONY : sentencepiece_trainer.s

# target to generate assembly for a file
sentencepiece_trainer.cc.s:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece_train-static.dir/build.make src/CMakeFiles/sentencepiece_train-static.dir/sentencepiece_trainer.cc.s
.PHONY : sentencepiece_trainer.cc.s

spm_decode_main.o: spm_decode_main.cc.o
.PHONY : spm_decode_main.o

# target to build an object file
spm_decode_main.cc.o:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/spm_decode.dir/build.make src/CMakeFiles/spm_decode.dir/spm_decode_main.cc.o
.PHONY : spm_decode_main.cc.o

spm_decode_main.i: spm_decode_main.cc.i
.PHONY : spm_decode_main.i

# target to preprocess a source file
spm_decode_main.cc.i:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/spm_decode.dir/build.make src/CMakeFiles/spm_decode.dir/spm_decode_main.cc.i
.PHONY : spm_decode_main.cc.i

spm_decode_main.s: spm_decode_main.cc.s
.PHONY : spm_decode_main.s

# target to generate assembly for a file
spm_decode_main.cc.s:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/spm_decode.dir/build.make src/CMakeFiles/spm_decode.dir/spm_decode_main.cc.s
.PHONY : spm_decode_main.cc.s

spm_encode_main.o: spm_encode_main.cc.o
.PHONY : spm_encode_main.o

# target to build an object file
spm_encode_main.cc.o:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/spm_encode.dir/build.make src/CMakeFiles/spm_encode.dir/spm_encode_main.cc.o
.PHONY : spm_encode_main.cc.o

spm_encode_main.i: spm_encode_main.cc.i
.PHONY : spm_encode_main.i

# target to preprocess a source file
spm_encode_main.cc.i:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/spm_encode.dir/build.make src/CMakeFiles/spm_encode.dir/spm_encode_main.cc.i
.PHONY : spm_encode_main.cc.i

spm_encode_main.s: spm_encode_main.cc.s
.PHONY : spm_encode_main.s

# target to generate assembly for a file
spm_encode_main.cc.s:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/spm_encode.dir/build.make src/CMakeFiles/spm_encode.dir/spm_encode_main.cc.s
.PHONY : spm_encode_main.cc.s

spm_export_vocab_main.o: spm_export_vocab_main.cc.o
.PHONY : spm_export_vocab_main.o

# target to build an object file
spm_export_vocab_main.cc.o:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/spm_export_vocab.dir/build.make src/CMakeFiles/spm_export_vocab.dir/spm_export_vocab_main.cc.o
.PHONY : spm_export_vocab_main.cc.o

spm_export_vocab_main.i: spm_export_vocab_main.cc.i
.PHONY : spm_export_vocab_main.i

# target to preprocess a source file
spm_export_vocab_main.cc.i:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/spm_export_vocab.dir/build.make src/CMakeFiles/spm_export_vocab.dir/spm_export_vocab_main.cc.i
.PHONY : spm_export_vocab_main.cc.i

spm_export_vocab_main.s: spm_export_vocab_main.cc.s
.PHONY : spm_export_vocab_main.s

# target to generate assembly for a file
spm_export_vocab_main.cc.s:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/spm_export_vocab.dir/build.make src/CMakeFiles/spm_export_vocab.dir/spm_export_vocab_main.cc.s
.PHONY : spm_export_vocab_main.cc.s

spm_normalize_main.o: spm_normalize_main.cc.o
.PHONY : spm_normalize_main.o

# target to build an object file
spm_normalize_main.cc.o:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/spm_normalize.dir/build.make src/CMakeFiles/spm_normalize.dir/spm_normalize_main.cc.o
.PHONY : spm_normalize_main.cc.o

spm_normalize_main.i: spm_normalize_main.cc.i
.PHONY : spm_normalize_main.i

# target to preprocess a source file
spm_normalize_main.cc.i:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/spm_normalize.dir/build.make src/CMakeFiles/spm_normalize.dir/spm_normalize_main.cc.i
.PHONY : spm_normalize_main.cc.i

spm_normalize_main.s: spm_normalize_main.cc.s
.PHONY : spm_normalize_main.s

# target to generate assembly for a file
spm_normalize_main.cc.s:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/spm_normalize.dir/build.make src/CMakeFiles/spm_normalize.dir/spm_normalize_main.cc.s
.PHONY : spm_normalize_main.cc.s

spm_train_main.o: spm_train_main.cc.o
.PHONY : spm_train_main.o

# target to build an object file
spm_train_main.cc.o:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/spm_train.dir/build.make src/CMakeFiles/spm_train.dir/spm_train_main.cc.o
.PHONY : spm_train_main.cc.o

spm_train_main.i: spm_train_main.cc.i
.PHONY : spm_train_main.i

# target to preprocess a source file
spm_train_main.cc.i:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/spm_train.dir/build.make src/CMakeFiles/spm_train.dir/spm_train_main.cc.i
.PHONY : spm_train_main.cc.i

spm_train_main.s: spm_train_main.cc.s
.PHONY : spm_train_main.s

# target to generate assembly for a file
spm_train_main.cc.s:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/spm_train.dir/build.make src/CMakeFiles/spm_train.dir/spm_train_main.cc.s
.PHONY : spm_train_main.cc.s

trainer_factory.o: trainer_factory.cc.o
.PHONY : trainer_factory.o

# target to build an object file
trainer_factory.cc.o:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece_train-static.dir/build.make src/CMakeFiles/sentencepiece_train-static.dir/trainer_factory.cc.o
.PHONY : trainer_factory.cc.o

trainer_factory.i: trainer_factory.cc.i
.PHONY : trainer_factory.i

# target to preprocess a source file
trainer_factory.cc.i:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece_train-static.dir/build.make src/CMakeFiles/sentencepiece_train-static.dir/trainer_factory.cc.i
.PHONY : trainer_factory.cc.i

trainer_factory.s: trainer_factory.cc.s
.PHONY : trainer_factory.s

# target to generate assembly for a file
trainer_factory.cc.s:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece_train-static.dir/build.make src/CMakeFiles/sentencepiece_train-static.dir/trainer_factory.cc.s
.PHONY : trainer_factory.cc.s

trainer_interface.o: trainer_interface.cc.o
.PHONY : trainer_interface.o

# target to build an object file
trainer_interface.cc.o:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece_train-static.dir/build.make src/CMakeFiles/sentencepiece_train-static.dir/trainer_interface.cc.o
.PHONY : trainer_interface.cc.o

trainer_interface.i: trainer_interface.cc.i
.PHONY : trainer_interface.i

# target to preprocess a source file
trainer_interface.cc.i:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece_train-static.dir/build.make src/CMakeFiles/sentencepiece_train-static.dir/trainer_interface.cc.i
.PHONY : trainer_interface.cc.i

trainer_interface.s: trainer_interface.cc.s
.PHONY : trainer_interface.s

# target to generate assembly for a file
trainer_interface.cc.s:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece_train-static.dir/build.make src/CMakeFiles/sentencepiece_train-static.dir/trainer_interface.cc.s
.PHONY : trainer_interface.cc.s

unicode_script.o: unicode_script.cc.o
.PHONY : unicode_script.o

# target to build an object file
unicode_script.cc.o:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece_train-static.dir/build.make src/CMakeFiles/sentencepiece_train-static.dir/unicode_script.cc.o
.PHONY : unicode_script.cc.o

unicode_script.i: unicode_script.cc.i
.PHONY : unicode_script.i

# target to preprocess a source file
unicode_script.cc.i:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece_train-static.dir/build.make src/CMakeFiles/sentencepiece_train-static.dir/unicode_script.cc.i
.PHONY : unicode_script.cc.i

unicode_script.s: unicode_script.cc.s
.PHONY : unicode_script.s

# target to generate assembly for a file
unicode_script.cc.s:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece_train-static.dir/build.make src/CMakeFiles/sentencepiece_train-static.dir/unicode_script.cc.s
.PHONY : unicode_script.cc.s

unigram_model.o: unigram_model.cc.o
.PHONY : unigram_model.o

# target to build an object file
unigram_model.cc.o:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/unigram_model.cc.o
.PHONY : unigram_model.cc.o

unigram_model.i: unigram_model.cc.i
.PHONY : unigram_model.i

# target to preprocess a source file
unigram_model.cc.i:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/unigram_model.cc.i
.PHONY : unigram_model.cc.i

unigram_model.s: unigram_model.cc.s
.PHONY : unigram_model.s

# target to generate assembly for a file
unigram_model.cc.s:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/unigram_model.cc.s
.PHONY : unigram_model.cc.s

unigram_model_trainer.o: unigram_model_trainer.cc.o
.PHONY : unigram_model_trainer.o

# target to build an object file
unigram_model_trainer.cc.o:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece_train-static.dir/build.make src/CMakeFiles/sentencepiece_train-static.dir/unigram_model_trainer.cc.o
.PHONY : unigram_model_trainer.cc.o

unigram_model_trainer.i: unigram_model_trainer.cc.i
.PHONY : unigram_model_trainer.i

# target to preprocess a source file
unigram_model_trainer.cc.i:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece_train-static.dir/build.make src/CMakeFiles/sentencepiece_train-static.dir/unigram_model_trainer.cc.i
.PHONY : unigram_model_trainer.cc.i

unigram_model_trainer.s: unigram_model_trainer.cc.s
.PHONY : unigram_model_trainer.s

# target to generate assembly for a file
unigram_model_trainer.cc.s:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece_train-static.dir/build.make src/CMakeFiles/sentencepiece_train-static.dir/unigram_model_trainer.cc.s
.PHONY : unigram_model_trainer.cc.s

util.o: util.cc.o
.PHONY : util.o

# target to build an object file
util.cc.o:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/util.cc.o
.PHONY : util.cc.o

util.i: util.cc.i
.PHONY : util.i

# target to preprocess a source file
util.cc.i:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/util.cc.i
.PHONY : util.cc.i

util.s: util.cc.s
.PHONY : util.s

# target to generate assembly for a file
util.cc.s:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/util.cc.s
.PHONY : util.cc.s

word_model.o: word_model.cc.o
.PHONY : word_model.o

# target to build an object file
word_model.cc.o:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/word_model.cc.o
.PHONY : word_model.cc.o

word_model.i: word_model.cc.i
.PHONY : word_model.i

# target to preprocess a source file
word_model.cc.i:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/word_model.cc.i
.PHONY : word_model.cc.i

word_model.s: word_model.cc.s
.PHONY : word_model.s

# target to generate assembly for a file
word_model.cc.s:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/word_model.cc.s
.PHONY : word_model.cc.s

word_model_trainer.o: word_model_trainer.cc.o
.PHONY : word_model_trainer.o

# target to build an object file
word_model_trainer.cc.o:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece_train-static.dir/build.make src/CMakeFiles/sentencepiece_train-static.dir/word_model_trainer.cc.o
.PHONY : word_model_trainer.cc.o

word_model_trainer.i: word_model_trainer.cc.i
.PHONY : word_model_trainer.i

# target to preprocess a source file
word_model_trainer.cc.i:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece_train-static.dir/build.make src/CMakeFiles/sentencepiece_train-static.dir/word_model_trainer.cc.i
.PHONY : word_model_trainer.cc.i

word_model_trainer.s: word_model_trainer.cc.s
.PHONY : word_model_trainer.s

# target to generate assembly for a file
word_model_trainer.cc.s:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece_train-static.dir/build.make src/CMakeFiles/sentencepiece_train-static.dir/word_model_trainer.cc.s
.PHONY : word_model_trainer.cc.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... package"
	@echo "... package_source"
	@echo "... rebuild_cache"
	@echo "... sentencepiece-static"
	@echo "... sentencepiece_train-static"
	@echo "... spm_decode"
	@echo "... spm_encode"
	@echo "... spm_export_vocab"
	@echo "... spm_normalize"
	@echo "... spm_train"
	@echo "... __/third_party/absl/flags/flag.o"
	@echo "... __/third_party/absl/flags/flag.i"
	@echo "... __/third_party/absl/flags/flag.s"
	@echo "... __/third_party/protobuf-lite/arena.o"
	@echo "... __/third_party/protobuf-lite/arena.i"
	@echo "... __/third_party/protobuf-lite/arena.s"
	@echo "... __/third_party/protobuf-lite/arenastring.o"
	@echo "... __/third_party/protobuf-lite/arenastring.i"
	@echo "... __/third_party/protobuf-lite/arenastring.s"
	@echo "... __/third_party/protobuf-lite/bytestream.o"
	@echo "... __/third_party/protobuf-lite/bytestream.i"
	@echo "... __/third_party/protobuf-lite/bytestream.s"
	@echo "... __/third_party/protobuf-lite/coded_stream.o"
	@echo "... __/third_party/protobuf-lite/coded_stream.i"
	@echo "... __/third_party/protobuf-lite/coded_stream.s"
	@echo "... __/third_party/protobuf-lite/common.o"
	@echo "... __/third_party/protobuf-lite/common.i"
	@echo "... __/third_party/protobuf-lite/common.s"
	@echo "... __/third_party/protobuf-lite/extension_set.o"
	@echo "... __/third_party/protobuf-lite/extension_set.i"
	@echo "... __/third_party/protobuf-lite/extension_set.s"
	@echo "... __/third_party/protobuf-lite/generated_enum_util.o"
	@echo "... __/third_party/protobuf-lite/generated_enum_util.i"
	@echo "... __/third_party/protobuf-lite/generated_enum_util.s"
	@echo "... __/third_party/protobuf-lite/generated_message_table_driven_lite.o"
	@echo "... __/third_party/protobuf-lite/generated_message_table_driven_lite.i"
	@echo "... __/third_party/protobuf-lite/generated_message_table_driven_lite.s"
	@echo "... __/third_party/protobuf-lite/generated_message_util.o"
	@echo "... __/third_party/protobuf-lite/generated_message_util.i"
	@echo "... __/third_party/protobuf-lite/generated_message_util.s"
	@echo "... __/third_party/protobuf-lite/implicit_weak_message.o"
	@echo "... __/third_party/protobuf-lite/implicit_weak_message.i"
	@echo "... __/third_party/protobuf-lite/implicit_weak_message.s"
	@echo "... __/third_party/protobuf-lite/int128.o"
	@echo "... __/third_party/protobuf-lite/int128.i"
	@echo "... __/third_party/protobuf-lite/int128.s"
	@echo "... __/third_party/protobuf-lite/io_win32.o"
	@echo "... __/third_party/protobuf-lite/io_win32.i"
	@echo "... __/third_party/protobuf-lite/io_win32.s"
	@echo "... __/third_party/protobuf-lite/message_lite.o"
	@echo "... __/third_party/protobuf-lite/message_lite.i"
	@echo "... __/third_party/protobuf-lite/message_lite.s"
	@echo "... __/third_party/protobuf-lite/parse_context.o"
	@echo "... __/third_party/protobuf-lite/parse_context.i"
	@echo "... __/third_party/protobuf-lite/parse_context.s"
	@echo "... __/third_party/protobuf-lite/repeated_field.o"
	@echo "... __/third_party/protobuf-lite/repeated_field.i"
	@echo "... __/third_party/protobuf-lite/repeated_field.s"
	@echo "... __/third_party/protobuf-lite/status.o"
	@echo "... __/third_party/protobuf-lite/status.i"
	@echo "... __/third_party/protobuf-lite/status.s"
	@echo "... __/third_party/protobuf-lite/statusor.o"
	@echo "... __/third_party/protobuf-lite/statusor.i"
	@echo "... __/third_party/protobuf-lite/statusor.s"
	@echo "... __/third_party/protobuf-lite/stringpiece.o"
	@echo "... __/third_party/protobuf-lite/stringpiece.i"
	@echo "... __/third_party/protobuf-lite/stringpiece.s"
	@echo "... __/third_party/protobuf-lite/stringprintf.o"
	@echo "... __/third_party/protobuf-lite/stringprintf.i"
	@echo "... __/third_party/protobuf-lite/stringprintf.s"
	@echo "... __/third_party/protobuf-lite/structurally_valid.o"
	@echo "... __/third_party/protobuf-lite/structurally_valid.i"
	@echo "... __/third_party/protobuf-lite/structurally_valid.s"
	@echo "... __/third_party/protobuf-lite/strutil.o"
	@echo "... __/third_party/protobuf-lite/strutil.i"
	@echo "... __/third_party/protobuf-lite/strutil.s"
	@echo "... __/third_party/protobuf-lite/time.o"
	@echo "... __/third_party/protobuf-lite/time.i"
	@echo "... __/third_party/protobuf-lite/time.s"
	@echo "... __/third_party/protobuf-lite/wire_format_lite.o"
	@echo "... __/third_party/protobuf-lite/wire_format_lite.i"
	@echo "... __/third_party/protobuf-lite/wire_format_lite.s"
	@echo "... __/third_party/protobuf-lite/zero_copy_stream.o"
	@echo "... __/third_party/protobuf-lite/zero_copy_stream.i"
	@echo "... __/third_party/protobuf-lite/zero_copy_stream.s"
	@echo "... __/third_party/protobuf-lite/zero_copy_stream_impl.o"
	@echo "... __/third_party/protobuf-lite/zero_copy_stream_impl.i"
	@echo "... __/third_party/protobuf-lite/zero_copy_stream_impl.s"
	@echo "... __/third_party/protobuf-lite/zero_copy_stream_impl_lite.o"
	@echo "... __/third_party/protobuf-lite/zero_copy_stream_impl_lite.i"
	@echo "... __/third_party/protobuf-lite/zero_copy_stream_impl_lite.s"
	@echo "... bpe_model.o"
	@echo "... bpe_model.i"
	@echo "... bpe_model.s"
	@echo "... bpe_model_trainer.o"
	@echo "... bpe_model_trainer.i"
	@echo "... bpe_model_trainer.s"
	@echo "... builder.o"
	@echo "... builder.i"
	@echo "... builder.s"
	@echo "... builtin_pb/sentencepiece.pb.o"
	@echo "... builtin_pb/sentencepiece.pb.i"
	@echo "... builtin_pb/sentencepiece.pb.s"
	@echo "... builtin_pb/sentencepiece_model.pb.o"
	@echo "... builtin_pb/sentencepiece_model.pb.i"
	@echo "... builtin_pb/sentencepiece_model.pb.s"
	@echo "... char_model.o"
	@echo "... char_model.i"
	@echo "... char_model.s"
	@echo "... char_model_trainer.o"
	@echo "... char_model_trainer.i"
	@echo "... char_model_trainer.s"
	@echo "... error.o"
	@echo "... error.i"
	@echo "... error.s"
	@echo "... filesystem.o"
	@echo "... filesystem.i"
	@echo "... filesystem.s"
	@echo "... model_factory.o"
	@echo "... model_factory.i"
	@echo "... model_factory.s"
	@echo "... model_interface.o"
	@echo "... model_interface.i"
	@echo "... model_interface.s"
	@echo "... normalizer.o"
	@echo "... normalizer.i"
	@echo "... normalizer.s"
	@echo "... pretokenizer_for_training.o"
	@echo "... pretokenizer_for_training.i"
	@echo "... pretokenizer_for_training.s"
	@echo "... sentencepiece_processor.o"
	@echo "... sentencepiece_processor.i"
	@echo "... sentencepiece_processor.s"
	@echo "... sentencepiece_trainer.o"
	@echo "... sentencepiece_trainer.i"
	@echo "... sentencepiece_trainer.s"
	@echo "... spm_decode_main.o"
	@echo "... spm_decode_main.i"
	@echo "... spm_decode_main.s"
	@echo "... spm_encode_main.o"
	@echo "... spm_encode_main.i"
	@echo "... spm_encode_main.s"
	@echo "... spm_export_vocab_main.o"
	@echo "... spm_export_vocab_main.i"
	@echo "... spm_export_vocab_main.s"
	@echo "... spm_normalize_main.o"
	@echo "... spm_normalize_main.i"
	@echo "... spm_normalize_main.s"
	@echo "... spm_train_main.o"
	@echo "... spm_train_main.i"
	@echo "... spm_train_main.s"
	@echo "... trainer_factory.o"
	@echo "... trainer_factory.i"
	@echo "... trainer_factory.s"
	@echo "... trainer_interface.o"
	@echo "... trainer_interface.i"
	@echo "... trainer_interface.s"
	@echo "... unicode_script.o"
	@echo "... unicode_script.i"
	@echo "... unicode_script.s"
	@echo "... unigram_model.o"
	@echo "... unigram_model.i"
	@echo "... unigram_model.s"
	@echo "... unigram_model_trainer.o"
	@echo "... unigram_model_trainer.i"
	@echo "... unigram_model_trainer.s"
	@echo "... util.o"
	@echo "... util.i"
	@echo "... util.s"
	@echo "... word_model.o"
	@echo "... word_model.i"
	@echo "... word_model.s"
	@echo "... word_model_trainer.o"
	@echo "... word_model_trainer.i"
	@echo "... word_model_trainer.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/python/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

