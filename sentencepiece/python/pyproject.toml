[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "sentencepiece"
version = "0.2.1"
authors = [
  { name="Ta<PERSON> Kudo", email="<EMAIL>" },
]
description = "Unsupervised text tokenizer and detokenizer."
readme = "README.md"
requires-python = ">=3.9"
urls = { "Homepage" = "https://github.com/google/sentencepiece" }
classifiers = [
  "Programming Language :: Python :: 3",
  "Development Status :: 5 - Production/Stable",
  "Environment :: Console",
  "Intended Audience :: Developers",
  "Intended Audience :: Science/Research",
  "Operating System :: MacOS :: MacOS X",
  "Operating System :: Microsoft :: Windows",
  "Operating System :: POSIX :: Linux",
  "Programming Language :: Python",
  "Programming Language :: Python :: 3.9",
  "Programming Language :: Python :: 3.10",
  "Programming Language :: Python :: 3.11",
  "Programming Language :: Python :: 3.12",
  "Programming Language :: Python :: 3.13",
  "Programming Language :: Python :: 3.14",
  "Programming Language :: Python :: Free Threading :: 2 - Beta",
  "Topic :: Text Processing :: Linguistic",
  "Topic :: Software Development :: Libraries :: Python Modules",
]

[project.optional-dependencies]
test = ["pytest"]
testpaths = ["test"]

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-data]
sentencepiece = ["*.bin"]

[tool.cibuildwheel]
test-requires = ["pytest"]
test-command = "pytest -v {project}/test"
