# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Documents/log-anomaly/sentencepiece

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Documents/log-anomaly/sentencepiece/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: src/all
all: third_party/all
.PHONY : all

# The main recursive "codegen" target.
codegen: src/codegen
codegen: third_party/codegen
.PHONY : codegen

# The main recursive "preinstall" target.
preinstall: src/preinstall
preinstall: third_party/preinstall
.PHONY : preinstall

# The main recursive "clean" target.
clean: src/clean
clean: third_party/clean
.PHONY : clean

#=============================================================================
# Directory level rules for directory src

# Recursive "all" directory target.
src/all: src/CMakeFiles/sentencepiece.dir/all
src/all: src/CMakeFiles/sentencepiece_train.dir/all
src/all: src/CMakeFiles/sentencepiece-static.dir/all
src/all: src/CMakeFiles/sentencepiece_train-static.dir/all
src/all: src/CMakeFiles/spm_encode.dir/all
src/all: src/CMakeFiles/spm_decode.dir/all
src/all: src/CMakeFiles/spm_normalize.dir/all
src/all: src/CMakeFiles/spm_train.dir/all
src/all: src/CMakeFiles/spm_export_vocab.dir/all
.PHONY : src/all

# Recursive "codegen" directory target.
src/codegen: src/CMakeFiles/sentencepiece.dir/codegen
src/codegen: src/CMakeFiles/sentencepiece_train.dir/codegen
src/codegen: src/CMakeFiles/sentencepiece-static.dir/codegen
src/codegen: src/CMakeFiles/sentencepiece_train-static.dir/codegen
src/codegen: src/CMakeFiles/spm_encode.dir/codegen
src/codegen: src/CMakeFiles/spm_decode.dir/codegen
src/codegen: src/CMakeFiles/spm_normalize.dir/codegen
src/codegen: src/CMakeFiles/spm_train.dir/codegen
src/codegen: src/CMakeFiles/spm_export_vocab.dir/codegen
.PHONY : src/codegen

# Recursive "preinstall" directory target.
src/preinstall:
.PHONY : src/preinstall

# Recursive "clean" directory target.
src/clean: src/CMakeFiles/sentencepiece.dir/clean
src/clean: src/CMakeFiles/sentencepiece_train.dir/clean
src/clean: src/CMakeFiles/sentencepiece-static.dir/clean
src/clean: src/CMakeFiles/sentencepiece_train-static.dir/clean
src/clean: src/CMakeFiles/spm_encode.dir/clean
src/clean: src/CMakeFiles/spm_decode.dir/clean
src/clean: src/CMakeFiles/spm_normalize.dir/clean
src/clean: src/CMakeFiles/spm_train.dir/clean
src/clean: src/CMakeFiles/spm_export_vocab.dir/clean
.PHONY : src/clean

#=============================================================================
# Directory level rules for directory third_party

# Recursive "all" directory target.
third_party/all:
.PHONY : third_party/all

# Recursive "codegen" directory target.
third_party/codegen:
.PHONY : third_party/codegen

# Recursive "preinstall" directory target.
third_party/preinstall:
.PHONY : third_party/preinstall

# Recursive "clean" directory target.
third_party/clean:
.PHONY : third_party/clean

#=============================================================================
# Target rules for target src/CMakeFiles/sentencepiece.dir

# All Build rule for target.
src/CMakeFiles/sentencepiece.dir/all:
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece.dir/build.make src/CMakeFiles/sentencepiece.dir/depend
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece.dir/build.make src/CMakeFiles/sentencepiece.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35 "Built target sentencepiece"
.PHONY : src/CMakeFiles/sentencepiece.dir/all

# Build rule for subdir invocation for target.
src/CMakeFiles/sentencepiece.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles 35
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/CMakeFiles/sentencepiece.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles 0
.PHONY : src/CMakeFiles/sentencepiece.dir/rule

# Convenience name for target.
sentencepiece: src/CMakeFiles/sentencepiece.dir/rule
.PHONY : sentencepiece

# codegen rule for target.
src/CMakeFiles/sentencepiece.dir/codegen:
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece.dir/build.make src/CMakeFiles/sentencepiece.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35 "Finished codegen for target sentencepiece"
.PHONY : src/CMakeFiles/sentencepiece.dir/codegen

# clean rule for target.
src/CMakeFiles/sentencepiece.dir/clean:
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece.dir/build.make src/CMakeFiles/sentencepiece.dir/clean
.PHONY : src/CMakeFiles/sentencepiece.dir/clean

#=============================================================================
# Target rules for target src/CMakeFiles/sentencepiece_train.dir

# All Build rule for target.
src/CMakeFiles/sentencepiece_train.dir/all: src/CMakeFiles/sentencepiece.dir/all
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece_train.dir/build.make src/CMakeFiles/sentencepiece_train.dir/depend
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece_train.dir/build.make src/CMakeFiles/sentencepiece_train.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=72,73,74,75,76,77,78,79,80,81 "Built target sentencepiece_train"
.PHONY : src/CMakeFiles/sentencepiece_train.dir/all

# Build rule for subdir invocation for target.
src/CMakeFiles/sentencepiece_train.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles 45
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/CMakeFiles/sentencepiece_train.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles 0
.PHONY : src/CMakeFiles/sentencepiece_train.dir/rule

# Convenience name for target.
sentencepiece_train: src/CMakeFiles/sentencepiece_train.dir/rule
.PHONY : sentencepiece_train

# codegen rule for target.
src/CMakeFiles/sentencepiece_train.dir/codegen:
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece_train.dir/build.make src/CMakeFiles/sentencepiece_train.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=72,73,74,75,76,77,78,79,80,81 "Finished codegen for target sentencepiece_train"
.PHONY : src/CMakeFiles/sentencepiece_train.dir/codegen

# clean rule for target.
src/CMakeFiles/sentencepiece_train.dir/clean:
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece_train.dir/build.make src/CMakeFiles/sentencepiece_train.dir/clean
.PHONY : src/CMakeFiles/sentencepiece_train.dir/clean

#=============================================================================
# Target rules for target src/CMakeFiles/sentencepiece-static.dir

# All Build rule for target.
src/CMakeFiles/sentencepiece-static.dir/all:
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/depend
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71 "Built target sentencepiece-static"
.PHONY : src/CMakeFiles/sentencepiece-static.dir/all

# Build rule for subdir invocation for target.
src/CMakeFiles/sentencepiece-static.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles 36
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/CMakeFiles/sentencepiece-static.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles 0
.PHONY : src/CMakeFiles/sentencepiece-static.dir/rule

# Convenience name for target.
sentencepiece-static: src/CMakeFiles/sentencepiece-static.dir/rule
.PHONY : sentencepiece-static

# codegen rule for target.
src/CMakeFiles/sentencepiece-static.dir/codegen:
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71 "Finished codegen for target sentencepiece-static"
.PHONY : src/CMakeFiles/sentencepiece-static.dir/codegen

# clean rule for target.
src/CMakeFiles/sentencepiece-static.dir/clean:
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece-static.dir/build.make src/CMakeFiles/sentencepiece-static.dir/clean
.PHONY : src/CMakeFiles/sentencepiece-static.dir/clean

#=============================================================================
# Target rules for target src/CMakeFiles/sentencepiece_train-static.dir

# All Build rule for target.
src/CMakeFiles/sentencepiece_train-static.dir/all:
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece_train-static.dir/build.make src/CMakeFiles/sentencepiece_train-static.dir/depend
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece_train-static.dir/build.make src/CMakeFiles/sentencepiece_train-static.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=82,83,84,85,86,87,88,89,90,91 "Built target sentencepiece_train-static"
.PHONY : src/CMakeFiles/sentencepiece_train-static.dir/all

# Build rule for subdir invocation for target.
src/CMakeFiles/sentencepiece_train-static.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles 10
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/CMakeFiles/sentencepiece_train-static.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles 0
.PHONY : src/CMakeFiles/sentencepiece_train-static.dir/rule

# Convenience name for target.
sentencepiece_train-static: src/CMakeFiles/sentencepiece_train-static.dir/rule
.PHONY : sentencepiece_train-static

# codegen rule for target.
src/CMakeFiles/sentencepiece_train-static.dir/codegen:
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece_train-static.dir/build.make src/CMakeFiles/sentencepiece_train-static.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=82,83,84,85,86,87,88,89,90,91 "Finished codegen for target sentencepiece_train-static"
.PHONY : src/CMakeFiles/sentencepiece_train-static.dir/codegen

# clean rule for target.
src/CMakeFiles/sentencepiece_train-static.dir/clean:
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/sentencepiece_train-static.dir/build.make src/CMakeFiles/sentencepiece_train-static.dir/clean
.PHONY : src/CMakeFiles/sentencepiece_train-static.dir/clean

#=============================================================================
# Target rules for target src/CMakeFiles/spm_encode.dir

# All Build rule for target.
src/CMakeFiles/spm_encode.dir/all: src/CMakeFiles/sentencepiece.dir/all
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/spm_encode.dir/build.make src/CMakeFiles/spm_encode.dir/depend
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/spm_encode.dir/build.make src/CMakeFiles/spm_encode.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=93,94 "Built target spm_encode"
.PHONY : src/CMakeFiles/spm_encode.dir/all

# Build rule for subdir invocation for target.
src/CMakeFiles/spm_encode.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles 37
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/CMakeFiles/spm_encode.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles 0
.PHONY : src/CMakeFiles/spm_encode.dir/rule

# Convenience name for target.
spm_encode: src/CMakeFiles/spm_encode.dir/rule
.PHONY : spm_encode

# codegen rule for target.
src/CMakeFiles/spm_encode.dir/codegen:
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/spm_encode.dir/build.make src/CMakeFiles/spm_encode.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=93,94 "Finished codegen for target spm_encode"
.PHONY : src/CMakeFiles/spm_encode.dir/codegen

# clean rule for target.
src/CMakeFiles/spm_encode.dir/clean:
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/spm_encode.dir/build.make src/CMakeFiles/spm_encode.dir/clean
.PHONY : src/CMakeFiles/spm_encode.dir/clean

#=============================================================================
# Target rules for target src/CMakeFiles/spm_decode.dir

# All Build rule for target.
src/CMakeFiles/spm_decode.dir/all: src/CMakeFiles/sentencepiece.dir/all
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/spm_decode.dir/build.make src/CMakeFiles/spm_decode.dir/depend
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/spm_decode.dir/build.make src/CMakeFiles/spm_decode.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=92 "Built target spm_decode"
.PHONY : src/CMakeFiles/spm_decode.dir/all

# Build rule for subdir invocation for target.
src/CMakeFiles/spm_decode.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles 36
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/CMakeFiles/spm_decode.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles 0
.PHONY : src/CMakeFiles/spm_decode.dir/rule

# Convenience name for target.
spm_decode: src/CMakeFiles/spm_decode.dir/rule
.PHONY : spm_decode

# codegen rule for target.
src/CMakeFiles/spm_decode.dir/codegen:
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/spm_decode.dir/build.make src/CMakeFiles/spm_decode.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=92 "Finished codegen for target spm_decode"
.PHONY : src/CMakeFiles/spm_decode.dir/codegen

# clean rule for target.
src/CMakeFiles/spm_decode.dir/clean:
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/spm_decode.dir/build.make src/CMakeFiles/spm_decode.dir/clean
.PHONY : src/CMakeFiles/spm_decode.dir/clean

#=============================================================================
# Target rules for target src/CMakeFiles/spm_normalize.dir

# All Build rule for target.
src/CMakeFiles/spm_normalize.dir/all: src/CMakeFiles/sentencepiece.dir/all
src/CMakeFiles/spm_normalize.dir/all: src/CMakeFiles/sentencepiece_train.dir/all
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/spm_normalize.dir/build.make src/CMakeFiles/spm_normalize.dir/depend
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/spm_normalize.dir/build.make src/CMakeFiles/spm_normalize.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=97,98 "Built target spm_normalize"
.PHONY : src/CMakeFiles/spm_normalize.dir/all

# Build rule for subdir invocation for target.
src/CMakeFiles/spm_normalize.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles 47
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/CMakeFiles/spm_normalize.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles 0
.PHONY : src/CMakeFiles/spm_normalize.dir/rule

# Convenience name for target.
spm_normalize: src/CMakeFiles/spm_normalize.dir/rule
.PHONY : spm_normalize

# codegen rule for target.
src/CMakeFiles/spm_normalize.dir/codegen:
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/spm_normalize.dir/build.make src/CMakeFiles/spm_normalize.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=97,98 "Finished codegen for target spm_normalize"
.PHONY : src/CMakeFiles/spm_normalize.dir/codegen

# clean rule for target.
src/CMakeFiles/spm_normalize.dir/clean:
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/spm_normalize.dir/build.make src/CMakeFiles/spm_normalize.dir/clean
.PHONY : src/CMakeFiles/spm_normalize.dir/clean

#=============================================================================
# Target rules for target src/CMakeFiles/spm_train.dir

# All Build rule for target.
src/CMakeFiles/spm_train.dir/all: src/CMakeFiles/sentencepiece.dir/all
src/CMakeFiles/spm_train.dir/all: src/CMakeFiles/sentencepiece_train.dir/all
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/spm_train.dir/build.make src/CMakeFiles/spm_train.dir/depend
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/spm_train.dir/build.make src/CMakeFiles/spm_train.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=99,100 "Built target spm_train"
.PHONY : src/CMakeFiles/spm_train.dir/all

# Build rule for subdir invocation for target.
src/CMakeFiles/spm_train.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles 47
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/CMakeFiles/spm_train.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles 0
.PHONY : src/CMakeFiles/spm_train.dir/rule

# Convenience name for target.
spm_train: src/CMakeFiles/spm_train.dir/rule
.PHONY : spm_train

# codegen rule for target.
src/CMakeFiles/spm_train.dir/codegen:
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/spm_train.dir/build.make src/CMakeFiles/spm_train.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=99,100 "Finished codegen for target spm_train"
.PHONY : src/CMakeFiles/spm_train.dir/codegen

# clean rule for target.
src/CMakeFiles/spm_train.dir/clean:
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/spm_train.dir/build.make src/CMakeFiles/spm_train.dir/clean
.PHONY : src/CMakeFiles/spm_train.dir/clean

#=============================================================================
# Target rules for target src/CMakeFiles/spm_export_vocab.dir

# All Build rule for target.
src/CMakeFiles/spm_export_vocab.dir/all: src/CMakeFiles/sentencepiece.dir/all
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/spm_export_vocab.dir/build.make src/CMakeFiles/spm_export_vocab.dir/depend
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/spm_export_vocab.dir/build.make src/CMakeFiles/spm_export_vocab.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=95,96 "Built target spm_export_vocab"
.PHONY : src/CMakeFiles/spm_export_vocab.dir/all

# Build rule for subdir invocation for target.
src/CMakeFiles/spm_export_vocab.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles 37
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/CMakeFiles/spm_export_vocab.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles 0
.PHONY : src/CMakeFiles/spm_export_vocab.dir/rule

# Convenience name for target.
spm_export_vocab: src/CMakeFiles/spm_export_vocab.dir/rule
.PHONY : spm_export_vocab

# codegen rule for target.
src/CMakeFiles/spm_export_vocab.dir/codegen:
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/spm_export_vocab.dir/build.make src/CMakeFiles/spm_export_vocab.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=95,96 "Finished codegen for target spm_export_vocab"
.PHONY : src/CMakeFiles/spm_export_vocab.dir/codegen

# clean rule for target.
src/CMakeFiles/spm_export_vocab.dir/clean:
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/spm_export_vocab.dir/build.make src/CMakeFiles/spm_export_vocab.dir/clean
.PHONY : src/CMakeFiles/spm_export_vocab.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

