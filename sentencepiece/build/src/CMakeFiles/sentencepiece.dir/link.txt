/usr/bin/c++ -fPIC -O3 -Wall -fPIC  -fmacro-prefix-map=/home/<USER>/Documents/log-anomaly/sentencepiece/='' -Wl,--dependency-file=CMakeFiles/sentencepiece.dir/link.d -shared -Wl,-soname,libsentencepiece.so.0 -o libsentencepiece.so.0.0.0 "CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/arena.cc.o" "CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/arenastring.cc.o" "CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/bytestream.cc.o" "CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/coded_stream.cc.o" "CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/common.cc.o" "CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/extension_set.cc.o" "CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/generated_enum_util.cc.o" "CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/generated_message_table_driven_lite.cc.o" "CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/generated_message_util.cc.o" "CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/implicit_weak_message.cc.o" "CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/int128.cc.o" "CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/io_win32.cc.o" "CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/message_lite.cc.o" "CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/parse_context.cc.o" "CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/repeated_field.cc.o" "CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/status.cc.o" "CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/statusor.cc.o" "CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/stringpiece.cc.o" "CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/stringprintf.cc.o" "CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/structurally_valid.cc.o" "CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/strutil.cc.o" "CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/time.cc.o" "CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/wire_format_lite.cc.o" "CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/zero_copy_stream.cc.o" "CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/zero_copy_stream_impl.cc.o" "CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/zero_copy_stream_impl_lite.cc.o" CMakeFiles/sentencepiece.dir/builtin_pb/sentencepiece.pb.cc.o CMakeFiles/sentencepiece.dir/builtin_pb/sentencepiece_model.pb.cc.o CMakeFiles/sentencepiece.dir/bpe_model.cc.o CMakeFiles/sentencepiece.dir/char_model.cc.o CMakeFiles/sentencepiece.dir/error.cc.o CMakeFiles/sentencepiece.dir/filesystem.cc.o CMakeFiles/sentencepiece.dir/model_factory.cc.o CMakeFiles/sentencepiece.dir/model_interface.cc.o CMakeFiles/sentencepiece.dir/normalizer.cc.o CMakeFiles/sentencepiece.dir/sentencepiece_processor.cc.o CMakeFiles/sentencepiece.dir/unigram_model.cc.o CMakeFiles/sentencepiece.dir/util.cc.o CMakeFiles/sentencepiece.dir/word_model.cc.o CMakeFiles/sentencepiece.dir/__/third_party/absl/flags/flag.cc.o  /usr/lib/libtcmalloc_minimal.so
