# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Documents/log-anomaly/sentencepiece

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Documents/log-anomaly/sentencepiece/build

# Include any dependencies generated for this target.
include src/CMakeFiles/sentencepiece.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include src/CMakeFiles/sentencepiece.dir/compiler_depend.make

# Include the progress variables for this target.
include src/CMakeFiles/sentencepiece.dir/progress.make

# Include the compile flags for this target's objects.
include src/CMakeFiles/sentencepiece.dir/flags.make

src/CMakeFiles/sentencepiece.dir/codegen:
.PHONY : src/CMakeFiles/sentencepiece.dir/codegen

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/arena.cc.o: src/CMakeFiles/sentencepiece.dir/flags.make
src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/arena.cc.o: /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/arena.cc
src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/arena.cc.o: src/CMakeFiles/sentencepiece.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/arena.cc.o"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/arena.cc.o -MF CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/arena.cc.o.d -o CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/arena.cc.o -c /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/arena.cc

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/arena.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/arena.cc.i"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/arena.cc > CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/arena.cc.i

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/arena.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/arena.cc.s"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/arena.cc -o CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/arena.cc.s

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/arenastring.cc.o: src/CMakeFiles/sentencepiece.dir/flags.make
src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/arenastring.cc.o: /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/arenastring.cc
src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/arenastring.cc.o: src/CMakeFiles/sentencepiece.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/arenastring.cc.o"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/arenastring.cc.o -MF CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/arenastring.cc.o.d -o CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/arenastring.cc.o -c /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/arenastring.cc

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/arenastring.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/arenastring.cc.i"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/arenastring.cc > CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/arenastring.cc.i

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/arenastring.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/arenastring.cc.s"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/arenastring.cc -o CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/arenastring.cc.s

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/bytestream.cc.o: src/CMakeFiles/sentencepiece.dir/flags.make
src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/bytestream.cc.o: /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/bytestream.cc
src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/bytestream.cc.o: src/CMakeFiles/sentencepiece.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/bytestream.cc.o"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/bytestream.cc.o -MF CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/bytestream.cc.o.d -o CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/bytestream.cc.o -c /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/bytestream.cc

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/bytestream.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/bytestream.cc.i"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/bytestream.cc > CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/bytestream.cc.i

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/bytestream.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/bytestream.cc.s"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/bytestream.cc -o CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/bytestream.cc.s

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/coded_stream.cc.o: src/CMakeFiles/sentencepiece.dir/flags.make
src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/coded_stream.cc.o: /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/coded_stream.cc
src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/coded_stream.cc.o: src/CMakeFiles/sentencepiece.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/coded_stream.cc.o"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/coded_stream.cc.o -MF CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/coded_stream.cc.o.d -o CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/coded_stream.cc.o -c /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/coded_stream.cc

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/coded_stream.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/coded_stream.cc.i"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/coded_stream.cc > CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/coded_stream.cc.i

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/coded_stream.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/coded_stream.cc.s"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/coded_stream.cc -o CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/coded_stream.cc.s

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/common.cc.o: src/CMakeFiles/sentencepiece.dir/flags.make
src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/common.cc.o: /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/common.cc
src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/common.cc.o: src/CMakeFiles/sentencepiece.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/common.cc.o"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/common.cc.o -MF CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/common.cc.o.d -o CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/common.cc.o -c /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/common.cc

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/common.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/common.cc.i"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/common.cc > CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/common.cc.i

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/common.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/common.cc.s"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/common.cc -o CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/common.cc.s

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/extension_set.cc.o: src/CMakeFiles/sentencepiece.dir/flags.make
src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/extension_set.cc.o: /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/extension_set.cc
src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/extension_set.cc.o: src/CMakeFiles/sentencepiece.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/extension_set.cc.o"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/extension_set.cc.o -MF CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/extension_set.cc.o.d -o CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/extension_set.cc.o -c /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/extension_set.cc

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/extension_set.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/extension_set.cc.i"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/extension_set.cc > CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/extension_set.cc.i

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/extension_set.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/extension_set.cc.s"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/extension_set.cc -o CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/extension_set.cc.s

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/generated_enum_util.cc.o: src/CMakeFiles/sentencepiece.dir/flags.make
src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/generated_enum_util.cc.o: /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/generated_enum_util.cc
src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/generated_enum_util.cc.o: src/CMakeFiles/sentencepiece.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/generated_enum_util.cc.o"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/generated_enum_util.cc.o -MF CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/generated_enum_util.cc.o.d -o CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/generated_enum_util.cc.o -c /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/generated_enum_util.cc

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/generated_enum_util.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/generated_enum_util.cc.i"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/generated_enum_util.cc > CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/generated_enum_util.cc.i

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/generated_enum_util.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/generated_enum_util.cc.s"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/generated_enum_util.cc -o CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/generated_enum_util.cc.s

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/generated_message_table_driven_lite.cc.o: src/CMakeFiles/sentencepiece.dir/flags.make
src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/generated_message_table_driven_lite.cc.o: /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/generated_message_table_driven_lite.cc
src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/generated_message_table_driven_lite.cc.o: src/CMakeFiles/sentencepiece.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/generated_message_table_driven_lite.cc.o"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/generated_message_table_driven_lite.cc.o -MF CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/generated_message_table_driven_lite.cc.o.d -o CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/generated_message_table_driven_lite.cc.o -c /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/generated_message_table_driven_lite.cc

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/generated_message_table_driven_lite.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/generated_message_table_driven_lite.cc.i"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/generated_message_table_driven_lite.cc > CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/generated_message_table_driven_lite.cc.i

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/generated_message_table_driven_lite.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/generated_message_table_driven_lite.cc.s"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/generated_message_table_driven_lite.cc -o CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/generated_message_table_driven_lite.cc.s

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/generated_message_util.cc.o: src/CMakeFiles/sentencepiece.dir/flags.make
src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/generated_message_util.cc.o: /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/generated_message_util.cc
src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/generated_message_util.cc.o: src/CMakeFiles/sentencepiece.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building CXX object src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/generated_message_util.cc.o"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/generated_message_util.cc.o -MF CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/generated_message_util.cc.o.d -o CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/generated_message_util.cc.o -c /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/generated_message_util.cc

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/generated_message_util.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/generated_message_util.cc.i"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/generated_message_util.cc > CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/generated_message_util.cc.i

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/generated_message_util.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/generated_message_util.cc.s"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/generated_message_util.cc -o CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/generated_message_util.cc.s

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/implicit_weak_message.cc.o: src/CMakeFiles/sentencepiece.dir/flags.make
src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/implicit_weak_message.cc.o: /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/implicit_weak_message.cc
src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/implicit_weak_message.cc.o: src/CMakeFiles/sentencepiece.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building CXX object src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/implicit_weak_message.cc.o"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/implicit_weak_message.cc.o -MF CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/implicit_weak_message.cc.o.d -o CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/implicit_weak_message.cc.o -c /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/implicit_weak_message.cc

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/implicit_weak_message.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/implicit_weak_message.cc.i"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/implicit_weak_message.cc > CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/implicit_weak_message.cc.i

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/implicit_weak_message.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/implicit_weak_message.cc.s"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/implicit_weak_message.cc -o CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/implicit_weak_message.cc.s

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/int128.cc.o: src/CMakeFiles/sentencepiece.dir/flags.make
src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/int128.cc.o: /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/int128.cc
src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/int128.cc.o: src/CMakeFiles/sentencepiece.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building CXX object src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/int128.cc.o"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/int128.cc.o -MF CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/int128.cc.o.d -o CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/int128.cc.o -c /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/int128.cc

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/int128.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/int128.cc.i"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/int128.cc > CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/int128.cc.i

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/int128.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/int128.cc.s"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/int128.cc -o CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/int128.cc.s

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/io_win32.cc.o: src/CMakeFiles/sentencepiece.dir/flags.make
src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/io_win32.cc.o: /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/io_win32.cc
src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/io_win32.cc.o: src/CMakeFiles/sentencepiece.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building CXX object src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/io_win32.cc.o"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/io_win32.cc.o -MF CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/io_win32.cc.o.d -o CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/io_win32.cc.o -c /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/io_win32.cc

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/io_win32.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/io_win32.cc.i"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/io_win32.cc > CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/io_win32.cc.i

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/io_win32.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/io_win32.cc.s"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/io_win32.cc -o CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/io_win32.cc.s

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/message_lite.cc.o: src/CMakeFiles/sentencepiece.dir/flags.make
src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/message_lite.cc.o: /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/message_lite.cc
src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/message_lite.cc.o: src/CMakeFiles/sentencepiece.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building CXX object src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/message_lite.cc.o"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/message_lite.cc.o -MF CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/message_lite.cc.o.d -o CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/message_lite.cc.o -c /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/message_lite.cc

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/message_lite.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/message_lite.cc.i"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/message_lite.cc > CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/message_lite.cc.i

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/message_lite.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/message_lite.cc.s"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/message_lite.cc -o CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/message_lite.cc.s

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/parse_context.cc.o: src/CMakeFiles/sentencepiece.dir/flags.make
src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/parse_context.cc.o: /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/parse_context.cc
src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/parse_context.cc.o: src/CMakeFiles/sentencepiece.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building CXX object src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/parse_context.cc.o"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/parse_context.cc.o -MF CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/parse_context.cc.o.d -o CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/parse_context.cc.o -c /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/parse_context.cc

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/parse_context.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/parse_context.cc.i"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/parse_context.cc > CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/parse_context.cc.i

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/parse_context.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/parse_context.cc.s"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/parse_context.cc -o CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/parse_context.cc.s

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/repeated_field.cc.o: src/CMakeFiles/sentencepiece.dir/flags.make
src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/repeated_field.cc.o: /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/repeated_field.cc
src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/repeated_field.cc.o: src/CMakeFiles/sentencepiece.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building CXX object src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/repeated_field.cc.o"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/repeated_field.cc.o -MF CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/repeated_field.cc.o.d -o CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/repeated_field.cc.o -c /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/repeated_field.cc

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/repeated_field.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/repeated_field.cc.i"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/repeated_field.cc > CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/repeated_field.cc.i

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/repeated_field.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/repeated_field.cc.s"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/repeated_field.cc -o CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/repeated_field.cc.s

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/status.cc.o: src/CMakeFiles/sentencepiece.dir/flags.make
src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/status.cc.o: /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/status.cc
src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/status.cc.o: src/CMakeFiles/sentencepiece.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Building CXX object src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/status.cc.o"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/status.cc.o -MF CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/status.cc.o.d -o CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/status.cc.o -c /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/status.cc

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/status.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/status.cc.i"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/status.cc > CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/status.cc.i

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/status.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/status.cc.s"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/status.cc -o CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/status.cc.s

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/statusor.cc.o: src/CMakeFiles/sentencepiece.dir/flags.make
src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/statusor.cc.o: /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/statusor.cc
src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/statusor.cc.o: src/CMakeFiles/sentencepiece.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_17) "Building CXX object src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/statusor.cc.o"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/statusor.cc.o -MF CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/statusor.cc.o.d -o CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/statusor.cc.o -c /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/statusor.cc

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/statusor.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/statusor.cc.i"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/statusor.cc > CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/statusor.cc.i

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/statusor.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/statusor.cc.s"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/statusor.cc -o CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/statusor.cc.s

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/stringpiece.cc.o: src/CMakeFiles/sentencepiece.dir/flags.make
src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/stringpiece.cc.o: /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/stringpiece.cc
src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/stringpiece.cc.o: src/CMakeFiles/sentencepiece.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_18) "Building CXX object src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/stringpiece.cc.o"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/stringpiece.cc.o -MF CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/stringpiece.cc.o.d -o CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/stringpiece.cc.o -c /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/stringpiece.cc

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/stringpiece.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/stringpiece.cc.i"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/stringpiece.cc > CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/stringpiece.cc.i

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/stringpiece.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/stringpiece.cc.s"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/stringpiece.cc -o CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/stringpiece.cc.s

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/stringprintf.cc.o: src/CMakeFiles/sentencepiece.dir/flags.make
src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/stringprintf.cc.o: /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/stringprintf.cc
src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/stringprintf.cc.o: src/CMakeFiles/sentencepiece.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_19) "Building CXX object src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/stringprintf.cc.o"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/stringprintf.cc.o -MF CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/stringprintf.cc.o.d -o CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/stringprintf.cc.o -c /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/stringprintf.cc

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/stringprintf.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/stringprintf.cc.i"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/stringprintf.cc > CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/stringprintf.cc.i

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/stringprintf.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/stringprintf.cc.s"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/stringprintf.cc -o CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/stringprintf.cc.s

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/structurally_valid.cc.o: src/CMakeFiles/sentencepiece.dir/flags.make
src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/structurally_valid.cc.o: /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/structurally_valid.cc
src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/structurally_valid.cc.o: src/CMakeFiles/sentencepiece.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_20) "Building CXX object src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/structurally_valid.cc.o"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/structurally_valid.cc.o -MF CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/structurally_valid.cc.o.d -o CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/structurally_valid.cc.o -c /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/structurally_valid.cc

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/structurally_valid.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/structurally_valid.cc.i"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/structurally_valid.cc > CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/structurally_valid.cc.i

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/structurally_valid.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/structurally_valid.cc.s"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/structurally_valid.cc -o CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/structurally_valid.cc.s

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/strutil.cc.o: src/CMakeFiles/sentencepiece.dir/flags.make
src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/strutil.cc.o: /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/strutil.cc
src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/strutil.cc.o: src/CMakeFiles/sentencepiece.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_21) "Building CXX object src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/strutil.cc.o"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/strutil.cc.o -MF CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/strutil.cc.o.d -o CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/strutil.cc.o -c /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/strutil.cc

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/strutil.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/strutil.cc.i"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/strutil.cc > CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/strutil.cc.i

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/strutil.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/strutil.cc.s"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/strutil.cc -o CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/strutil.cc.s

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/time.cc.o: src/CMakeFiles/sentencepiece.dir/flags.make
src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/time.cc.o: /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/time.cc
src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/time.cc.o: src/CMakeFiles/sentencepiece.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_22) "Building CXX object src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/time.cc.o"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/time.cc.o -MF CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/time.cc.o.d -o CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/time.cc.o -c /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/time.cc

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/time.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/time.cc.i"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/time.cc > CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/time.cc.i

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/time.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/time.cc.s"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/time.cc -o CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/time.cc.s

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/wire_format_lite.cc.o: src/CMakeFiles/sentencepiece.dir/flags.make
src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/wire_format_lite.cc.o: /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/wire_format_lite.cc
src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/wire_format_lite.cc.o: src/CMakeFiles/sentencepiece.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_23) "Building CXX object src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/wire_format_lite.cc.o"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/wire_format_lite.cc.o -MF CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/wire_format_lite.cc.o.d -o CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/wire_format_lite.cc.o -c /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/wire_format_lite.cc

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/wire_format_lite.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/wire_format_lite.cc.i"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/wire_format_lite.cc > CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/wire_format_lite.cc.i

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/wire_format_lite.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/wire_format_lite.cc.s"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/wire_format_lite.cc -o CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/wire_format_lite.cc.s

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/zero_copy_stream.cc.o: src/CMakeFiles/sentencepiece.dir/flags.make
src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/zero_copy_stream.cc.o: /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/zero_copy_stream.cc
src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/zero_copy_stream.cc.o: src/CMakeFiles/sentencepiece.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_24) "Building CXX object src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/zero_copy_stream.cc.o"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/zero_copy_stream.cc.o -MF CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/zero_copy_stream.cc.o.d -o CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/zero_copy_stream.cc.o -c /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/zero_copy_stream.cc

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/zero_copy_stream.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/zero_copy_stream.cc.i"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/zero_copy_stream.cc > CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/zero_copy_stream.cc.i

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/zero_copy_stream.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/zero_copy_stream.cc.s"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/zero_copy_stream.cc -o CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/zero_copy_stream.cc.s

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/zero_copy_stream_impl.cc.o: src/CMakeFiles/sentencepiece.dir/flags.make
src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/zero_copy_stream_impl.cc.o: /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/zero_copy_stream_impl.cc
src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/zero_copy_stream_impl.cc.o: src/CMakeFiles/sentencepiece.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_25) "Building CXX object src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/zero_copy_stream_impl.cc.o"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/zero_copy_stream_impl.cc.o -MF CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/zero_copy_stream_impl.cc.o.d -o CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/zero_copy_stream_impl.cc.o -c /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/zero_copy_stream_impl.cc

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/zero_copy_stream_impl.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/zero_copy_stream_impl.cc.i"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/zero_copy_stream_impl.cc > CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/zero_copy_stream_impl.cc.i

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/zero_copy_stream_impl.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/zero_copy_stream_impl.cc.s"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/zero_copy_stream_impl.cc -o CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/zero_copy_stream_impl.cc.s

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/zero_copy_stream_impl_lite.cc.o: src/CMakeFiles/sentencepiece.dir/flags.make
src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/zero_copy_stream_impl_lite.cc.o: /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/zero_copy_stream_impl_lite.cc
src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/zero_copy_stream_impl_lite.cc.o: src/CMakeFiles/sentencepiece.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_26) "Building CXX object src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/zero_copy_stream_impl_lite.cc.o"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/zero_copy_stream_impl_lite.cc.o -MF CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/zero_copy_stream_impl_lite.cc.o.d -o CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/zero_copy_stream_impl_lite.cc.o -c /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/zero_copy_stream_impl_lite.cc

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/zero_copy_stream_impl_lite.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/zero_copy_stream_impl_lite.cc.i"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/zero_copy_stream_impl_lite.cc > CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/zero_copy_stream_impl_lite.cc.i

src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/zero_copy_stream_impl_lite.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/zero_copy_stream_impl_lite.cc.s"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/zero_copy_stream_impl_lite.cc -o CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/zero_copy_stream_impl_lite.cc.s

src/CMakeFiles/sentencepiece.dir/builtin_pb/sentencepiece.pb.cc.o: src/CMakeFiles/sentencepiece.dir/flags.make
src/CMakeFiles/sentencepiece.dir/builtin_pb/sentencepiece.pb.cc.o: /home/<USER>/Documents/log-anomaly/sentencepiece/src/builtin_pb/sentencepiece.pb.cc
src/CMakeFiles/sentencepiece.dir/builtin_pb/sentencepiece.pb.cc.o: src/CMakeFiles/sentencepiece.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_27) "Building CXX object src/CMakeFiles/sentencepiece.dir/builtin_pb/sentencepiece.pb.cc.o"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/sentencepiece.dir/builtin_pb/sentencepiece.pb.cc.o -MF CMakeFiles/sentencepiece.dir/builtin_pb/sentencepiece.pb.cc.o.d -o CMakeFiles/sentencepiece.dir/builtin_pb/sentencepiece.pb.cc.o -c /home/<USER>/Documents/log-anomaly/sentencepiece/src/builtin_pb/sentencepiece.pb.cc

src/CMakeFiles/sentencepiece.dir/builtin_pb/sentencepiece.pb.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sentencepiece.dir/builtin_pb/sentencepiece.pb.cc.i"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/log-anomaly/sentencepiece/src/builtin_pb/sentencepiece.pb.cc > CMakeFiles/sentencepiece.dir/builtin_pb/sentencepiece.pb.cc.i

src/CMakeFiles/sentencepiece.dir/builtin_pb/sentencepiece.pb.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sentencepiece.dir/builtin_pb/sentencepiece.pb.cc.s"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/log-anomaly/sentencepiece/src/builtin_pb/sentencepiece.pb.cc -o CMakeFiles/sentencepiece.dir/builtin_pb/sentencepiece.pb.cc.s

src/CMakeFiles/sentencepiece.dir/builtin_pb/sentencepiece_model.pb.cc.o: src/CMakeFiles/sentencepiece.dir/flags.make
src/CMakeFiles/sentencepiece.dir/builtin_pb/sentencepiece_model.pb.cc.o: /home/<USER>/Documents/log-anomaly/sentencepiece/src/builtin_pb/sentencepiece_model.pb.cc
src/CMakeFiles/sentencepiece.dir/builtin_pb/sentencepiece_model.pb.cc.o: src/CMakeFiles/sentencepiece.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_28) "Building CXX object src/CMakeFiles/sentencepiece.dir/builtin_pb/sentencepiece_model.pb.cc.o"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/sentencepiece.dir/builtin_pb/sentencepiece_model.pb.cc.o -MF CMakeFiles/sentencepiece.dir/builtin_pb/sentencepiece_model.pb.cc.o.d -o CMakeFiles/sentencepiece.dir/builtin_pb/sentencepiece_model.pb.cc.o -c /home/<USER>/Documents/log-anomaly/sentencepiece/src/builtin_pb/sentencepiece_model.pb.cc

src/CMakeFiles/sentencepiece.dir/builtin_pb/sentencepiece_model.pb.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sentencepiece.dir/builtin_pb/sentencepiece_model.pb.cc.i"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/log-anomaly/sentencepiece/src/builtin_pb/sentencepiece_model.pb.cc > CMakeFiles/sentencepiece.dir/builtin_pb/sentencepiece_model.pb.cc.i

src/CMakeFiles/sentencepiece.dir/builtin_pb/sentencepiece_model.pb.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sentencepiece.dir/builtin_pb/sentencepiece_model.pb.cc.s"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/log-anomaly/sentencepiece/src/builtin_pb/sentencepiece_model.pb.cc -o CMakeFiles/sentencepiece.dir/builtin_pb/sentencepiece_model.pb.cc.s

src/CMakeFiles/sentencepiece.dir/bpe_model.cc.o: src/CMakeFiles/sentencepiece.dir/flags.make
src/CMakeFiles/sentencepiece.dir/bpe_model.cc.o: /home/<USER>/Documents/log-anomaly/sentencepiece/src/bpe_model.cc
src/CMakeFiles/sentencepiece.dir/bpe_model.cc.o: src/CMakeFiles/sentencepiece.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_29) "Building CXX object src/CMakeFiles/sentencepiece.dir/bpe_model.cc.o"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/sentencepiece.dir/bpe_model.cc.o -MF CMakeFiles/sentencepiece.dir/bpe_model.cc.o.d -o CMakeFiles/sentencepiece.dir/bpe_model.cc.o -c /home/<USER>/Documents/log-anomaly/sentencepiece/src/bpe_model.cc

src/CMakeFiles/sentencepiece.dir/bpe_model.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sentencepiece.dir/bpe_model.cc.i"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/log-anomaly/sentencepiece/src/bpe_model.cc > CMakeFiles/sentencepiece.dir/bpe_model.cc.i

src/CMakeFiles/sentencepiece.dir/bpe_model.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sentencepiece.dir/bpe_model.cc.s"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/log-anomaly/sentencepiece/src/bpe_model.cc -o CMakeFiles/sentencepiece.dir/bpe_model.cc.s

src/CMakeFiles/sentencepiece.dir/char_model.cc.o: src/CMakeFiles/sentencepiece.dir/flags.make
src/CMakeFiles/sentencepiece.dir/char_model.cc.o: /home/<USER>/Documents/log-anomaly/sentencepiece/src/char_model.cc
src/CMakeFiles/sentencepiece.dir/char_model.cc.o: src/CMakeFiles/sentencepiece.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_30) "Building CXX object src/CMakeFiles/sentencepiece.dir/char_model.cc.o"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/sentencepiece.dir/char_model.cc.o -MF CMakeFiles/sentencepiece.dir/char_model.cc.o.d -o CMakeFiles/sentencepiece.dir/char_model.cc.o -c /home/<USER>/Documents/log-anomaly/sentencepiece/src/char_model.cc

src/CMakeFiles/sentencepiece.dir/char_model.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sentencepiece.dir/char_model.cc.i"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/log-anomaly/sentencepiece/src/char_model.cc > CMakeFiles/sentencepiece.dir/char_model.cc.i

src/CMakeFiles/sentencepiece.dir/char_model.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sentencepiece.dir/char_model.cc.s"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/log-anomaly/sentencepiece/src/char_model.cc -o CMakeFiles/sentencepiece.dir/char_model.cc.s

src/CMakeFiles/sentencepiece.dir/error.cc.o: src/CMakeFiles/sentencepiece.dir/flags.make
src/CMakeFiles/sentencepiece.dir/error.cc.o: /home/<USER>/Documents/log-anomaly/sentencepiece/src/error.cc
src/CMakeFiles/sentencepiece.dir/error.cc.o: src/CMakeFiles/sentencepiece.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_31) "Building CXX object src/CMakeFiles/sentencepiece.dir/error.cc.o"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/sentencepiece.dir/error.cc.o -MF CMakeFiles/sentencepiece.dir/error.cc.o.d -o CMakeFiles/sentencepiece.dir/error.cc.o -c /home/<USER>/Documents/log-anomaly/sentencepiece/src/error.cc

src/CMakeFiles/sentencepiece.dir/error.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sentencepiece.dir/error.cc.i"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/log-anomaly/sentencepiece/src/error.cc > CMakeFiles/sentencepiece.dir/error.cc.i

src/CMakeFiles/sentencepiece.dir/error.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sentencepiece.dir/error.cc.s"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/log-anomaly/sentencepiece/src/error.cc -o CMakeFiles/sentencepiece.dir/error.cc.s

src/CMakeFiles/sentencepiece.dir/filesystem.cc.o: src/CMakeFiles/sentencepiece.dir/flags.make
src/CMakeFiles/sentencepiece.dir/filesystem.cc.o: /home/<USER>/Documents/log-anomaly/sentencepiece/src/filesystem.cc
src/CMakeFiles/sentencepiece.dir/filesystem.cc.o: src/CMakeFiles/sentencepiece.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_32) "Building CXX object src/CMakeFiles/sentencepiece.dir/filesystem.cc.o"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/sentencepiece.dir/filesystem.cc.o -MF CMakeFiles/sentencepiece.dir/filesystem.cc.o.d -o CMakeFiles/sentencepiece.dir/filesystem.cc.o -c /home/<USER>/Documents/log-anomaly/sentencepiece/src/filesystem.cc

src/CMakeFiles/sentencepiece.dir/filesystem.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sentencepiece.dir/filesystem.cc.i"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/log-anomaly/sentencepiece/src/filesystem.cc > CMakeFiles/sentencepiece.dir/filesystem.cc.i

src/CMakeFiles/sentencepiece.dir/filesystem.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sentencepiece.dir/filesystem.cc.s"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/log-anomaly/sentencepiece/src/filesystem.cc -o CMakeFiles/sentencepiece.dir/filesystem.cc.s

src/CMakeFiles/sentencepiece.dir/model_factory.cc.o: src/CMakeFiles/sentencepiece.dir/flags.make
src/CMakeFiles/sentencepiece.dir/model_factory.cc.o: /home/<USER>/Documents/log-anomaly/sentencepiece/src/model_factory.cc
src/CMakeFiles/sentencepiece.dir/model_factory.cc.o: src/CMakeFiles/sentencepiece.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_33) "Building CXX object src/CMakeFiles/sentencepiece.dir/model_factory.cc.o"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/sentencepiece.dir/model_factory.cc.o -MF CMakeFiles/sentencepiece.dir/model_factory.cc.o.d -o CMakeFiles/sentencepiece.dir/model_factory.cc.o -c /home/<USER>/Documents/log-anomaly/sentencepiece/src/model_factory.cc

src/CMakeFiles/sentencepiece.dir/model_factory.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sentencepiece.dir/model_factory.cc.i"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/log-anomaly/sentencepiece/src/model_factory.cc > CMakeFiles/sentencepiece.dir/model_factory.cc.i

src/CMakeFiles/sentencepiece.dir/model_factory.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sentencepiece.dir/model_factory.cc.s"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/log-anomaly/sentencepiece/src/model_factory.cc -o CMakeFiles/sentencepiece.dir/model_factory.cc.s

src/CMakeFiles/sentencepiece.dir/model_interface.cc.o: src/CMakeFiles/sentencepiece.dir/flags.make
src/CMakeFiles/sentencepiece.dir/model_interface.cc.o: /home/<USER>/Documents/log-anomaly/sentencepiece/src/model_interface.cc
src/CMakeFiles/sentencepiece.dir/model_interface.cc.o: src/CMakeFiles/sentencepiece.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_34) "Building CXX object src/CMakeFiles/sentencepiece.dir/model_interface.cc.o"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/sentencepiece.dir/model_interface.cc.o -MF CMakeFiles/sentencepiece.dir/model_interface.cc.o.d -o CMakeFiles/sentencepiece.dir/model_interface.cc.o -c /home/<USER>/Documents/log-anomaly/sentencepiece/src/model_interface.cc

src/CMakeFiles/sentencepiece.dir/model_interface.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sentencepiece.dir/model_interface.cc.i"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/log-anomaly/sentencepiece/src/model_interface.cc > CMakeFiles/sentencepiece.dir/model_interface.cc.i

src/CMakeFiles/sentencepiece.dir/model_interface.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sentencepiece.dir/model_interface.cc.s"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/log-anomaly/sentencepiece/src/model_interface.cc -o CMakeFiles/sentencepiece.dir/model_interface.cc.s

src/CMakeFiles/sentencepiece.dir/normalizer.cc.o: src/CMakeFiles/sentencepiece.dir/flags.make
src/CMakeFiles/sentencepiece.dir/normalizer.cc.o: /home/<USER>/Documents/log-anomaly/sentencepiece/src/normalizer.cc
src/CMakeFiles/sentencepiece.dir/normalizer.cc.o: src/CMakeFiles/sentencepiece.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_35) "Building CXX object src/CMakeFiles/sentencepiece.dir/normalizer.cc.o"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/sentencepiece.dir/normalizer.cc.o -MF CMakeFiles/sentencepiece.dir/normalizer.cc.o.d -o CMakeFiles/sentencepiece.dir/normalizer.cc.o -c /home/<USER>/Documents/log-anomaly/sentencepiece/src/normalizer.cc

src/CMakeFiles/sentencepiece.dir/normalizer.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sentencepiece.dir/normalizer.cc.i"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/log-anomaly/sentencepiece/src/normalizer.cc > CMakeFiles/sentencepiece.dir/normalizer.cc.i

src/CMakeFiles/sentencepiece.dir/normalizer.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sentencepiece.dir/normalizer.cc.s"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/log-anomaly/sentencepiece/src/normalizer.cc -o CMakeFiles/sentencepiece.dir/normalizer.cc.s

src/CMakeFiles/sentencepiece.dir/sentencepiece_processor.cc.o: src/CMakeFiles/sentencepiece.dir/flags.make
src/CMakeFiles/sentencepiece.dir/sentencepiece_processor.cc.o: /home/<USER>/Documents/log-anomaly/sentencepiece/src/sentencepiece_processor.cc
src/CMakeFiles/sentencepiece.dir/sentencepiece_processor.cc.o: src/CMakeFiles/sentencepiece.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_36) "Building CXX object src/CMakeFiles/sentencepiece.dir/sentencepiece_processor.cc.o"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/sentencepiece.dir/sentencepiece_processor.cc.o -MF CMakeFiles/sentencepiece.dir/sentencepiece_processor.cc.o.d -o CMakeFiles/sentencepiece.dir/sentencepiece_processor.cc.o -c /home/<USER>/Documents/log-anomaly/sentencepiece/src/sentencepiece_processor.cc

src/CMakeFiles/sentencepiece.dir/sentencepiece_processor.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sentencepiece.dir/sentencepiece_processor.cc.i"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/log-anomaly/sentencepiece/src/sentencepiece_processor.cc > CMakeFiles/sentencepiece.dir/sentencepiece_processor.cc.i

src/CMakeFiles/sentencepiece.dir/sentencepiece_processor.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sentencepiece.dir/sentencepiece_processor.cc.s"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/log-anomaly/sentencepiece/src/sentencepiece_processor.cc -o CMakeFiles/sentencepiece.dir/sentencepiece_processor.cc.s

src/CMakeFiles/sentencepiece.dir/unigram_model.cc.o: src/CMakeFiles/sentencepiece.dir/flags.make
src/CMakeFiles/sentencepiece.dir/unigram_model.cc.o: /home/<USER>/Documents/log-anomaly/sentencepiece/src/unigram_model.cc
src/CMakeFiles/sentencepiece.dir/unigram_model.cc.o: src/CMakeFiles/sentencepiece.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_37) "Building CXX object src/CMakeFiles/sentencepiece.dir/unigram_model.cc.o"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/sentencepiece.dir/unigram_model.cc.o -MF CMakeFiles/sentencepiece.dir/unigram_model.cc.o.d -o CMakeFiles/sentencepiece.dir/unigram_model.cc.o -c /home/<USER>/Documents/log-anomaly/sentencepiece/src/unigram_model.cc

src/CMakeFiles/sentencepiece.dir/unigram_model.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sentencepiece.dir/unigram_model.cc.i"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/log-anomaly/sentencepiece/src/unigram_model.cc > CMakeFiles/sentencepiece.dir/unigram_model.cc.i

src/CMakeFiles/sentencepiece.dir/unigram_model.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sentencepiece.dir/unigram_model.cc.s"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/log-anomaly/sentencepiece/src/unigram_model.cc -o CMakeFiles/sentencepiece.dir/unigram_model.cc.s

src/CMakeFiles/sentencepiece.dir/util.cc.o: src/CMakeFiles/sentencepiece.dir/flags.make
src/CMakeFiles/sentencepiece.dir/util.cc.o: /home/<USER>/Documents/log-anomaly/sentencepiece/src/util.cc
src/CMakeFiles/sentencepiece.dir/util.cc.o: src/CMakeFiles/sentencepiece.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_38) "Building CXX object src/CMakeFiles/sentencepiece.dir/util.cc.o"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/sentencepiece.dir/util.cc.o -MF CMakeFiles/sentencepiece.dir/util.cc.o.d -o CMakeFiles/sentencepiece.dir/util.cc.o -c /home/<USER>/Documents/log-anomaly/sentencepiece/src/util.cc

src/CMakeFiles/sentencepiece.dir/util.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sentencepiece.dir/util.cc.i"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/log-anomaly/sentencepiece/src/util.cc > CMakeFiles/sentencepiece.dir/util.cc.i

src/CMakeFiles/sentencepiece.dir/util.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sentencepiece.dir/util.cc.s"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/log-anomaly/sentencepiece/src/util.cc -o CMakeFiles/sentencepiece.dir/util.cc.s

src/CMakeFiles/sentencepiece.dir/word_model.cc.o: src/CMakeFiles/sentencepiece.dir/flags.make
src/CMakeFiles/sentencepiece.dir/word_model.cc.o: /home/<USER>/Documents/log-anomaly/sentencepiece/src/word_model.cc
src/CMakeFiles/sentencepiece.dir/word_model.cc.o: src/CMakeFiles/sentencepiece.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_39) "Building CXX object src/CMakeFiles/sentencepiece.dir/word_model.cc.o"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/sentencepiece.dir/word_model.cc.o -MF CMakeFiles/sentencepiece.dir/word_model.cc.o.d -o CMakeFiles/sentencepiece.dir/word_model.cc.o -c /home/<USER>/Documents/log-anomaly/sentencepiece/src/word_model.cc

src/CMakeFiles/sentencepiece.dir/word_model.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sentencepiece.dir/word_model.cc.i"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/log-anomaly/sentencepiece/src/word_model.cc > CMakeFiles/sentencepiece.dir/word_model.cc.i

src/CMakeFiles/sentencepiece.dir/word_model.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sentencepiece.dir/word_model.cc.s"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/log-anomaly/sentencepiece/src/word_model.cc -o CMakeFiles/sentencepiece.dir/word_model.cc.s

src/CMakeFiles/sentencepiece.dir/__/third_party/absl/flags/flag.cc.o: src/CMakeFiles/sentencepiece.dir/flags.make
src/CMakeFiles/sentencepiece.dir/__/third_party/absl/flags/flag.cc.o: /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/absl/flags/flag.cc
src/CMakeFiles/sentencepiece.dir/__/third_party/absl/flags/flag.cc.o: src/CMakeFiles/sentencepiece.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_40) "Building CXX object src/CMakeFiles/sentencepiece.dir/__/third_party/absl/flags/flag.cc.o"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/sentencepiece.dir/__/third_party/absl/flags/flag.cc.o -MF CMakeFiles/sentencepiece.dir/__/third_party/absl/flags/flag.cc.o.d -o CMakeFiles/sentencepiece.dir/__/third_party/absl/flags/flag.cc.o -c /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/absl/flags/flag.cc

src/CMakeFiles/sentencepiece.dir/__/third_party/absl/flags/flag.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sentencepiece.dir/__/third_party/absl/flags/flag.cc.i"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/absl/flags/flag.cc > CMakeFiles/sentencepiece.dir/__/third_party/absl/flags/flag.cc.i

src/CMakeFiles/sentencepiece.dir/__/third_party/absl/flags/flag.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sentencepiece.dir/__/third_party/absl/flags/flag.cc.s"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/absl/flags/flag.cc -o CMakeFiles/sentencepiece.dir/__/third_party/absl/flags/flag.cc.s

# Object files for target sentencepiece
sentencepiece_OBJECTS = \
"CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/arena.cc.o" \
"CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/arenastring.cc.o" \
"CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/bytestream.cc.o" \
"CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/coded_stream.cc.o" \
"CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/common.cc.o" \
"CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/extension_set.cc.o" \
"CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/generated_enum_util.cc.o" \
"CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/generated_message_table_driven_lite.cc.o" \
"CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/generated_message_util.cc.o" \
"CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/implicit_weak_message.cc.o" \
"CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/int128.cc.o" \
"CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/io_win32.cc.o" \
"CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/message_lite.cc.o" \
"CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/parse_context.cc.o" \
"CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/repeated_field.cc.o" \
"CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/status.cc.o" \
"CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/statusor.cc.o" \
"CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/stringpiece.cc.o" \
"CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/stringprintf.cc.o" \
"CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/structurally_valid.cc.o" \
"CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/strutil.cc.o" \
"CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/time.cc.o" \
"CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/wire_format_lite.cc.o" \
"CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/zero_copy_stream.cc.o" \
"CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/zero_copy_stream_impl.cc.o" \
"CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/zero_copy_stream_impl_lite.cc.o" \
"CMakeFiles/sentencepiece.dir/builtin_pb/sentencepiece.pb.cc.o" \
"CMakeFiles/sentencepiece.dir/builtin_pb/sentencepiece_model.pb.cc.o" \
"CMakeFiles/sentencepiece.dir/bpe_model.cc.o" \
"CMakeFiles/sentencepiece.dir/char_model.cc.o" \
"CMakeFiles/sentencepiece.dir/error.cc.o" \
"CMakeFiles/sentencepiece.dir/filesystem.cc.o" \
"CMakeFiles/sentencepiece.dir/model_factory.cc.o" \
"CMakeFiles/sentencepiece.dir/model_interface.cc.o" \
"CMakeFiles/sentencepiece.dir/normalizer.cc.o" \
"CMakeFiles/sentencepiece.dir/sentencepiece_processor.cc.o" \
"CMakeFiles/sentencepiece.dir/unigram_model.cc.o" \
"CMakeFiles/sentencepiece.dir/util.cc.o" \
"CMakeFiles/sentencepiece.dir/word_model.cc.o" \
"CMakeFiles/sentencepiece.dir/__/third_party/absl/flags/flag.cc.o"

# External object files for target sentencepiece
sentencepiece_EXTERNAL_OBJECTS =

src/libsentencepiece.so.0.0.0: src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/arena.cc.o
src/libsentencepiece.so.0.0.0: src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/arenastring.cc.o
src/libsentencepiece.so.0.0.0: src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/bytestream.cc.o
src/libsentencepiece.so.0.0.0: src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/coded_stream.cc.o
src/libsentencepiece.so.0.0.0: src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/common.cc.o
src/libsentencepiece.so.0.0.0: src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/extension_set.cc.o
src/libsentencepiece.so.0.0.0: src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/generated_enum_util.cc.o
src/libsentencepiece.so.0.0.0: src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/generated_message_table_driven_lite.cc.o
src/libsentencepiece.so.0.0.0: src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/generated_message_util.cc.o
src/libsentencepiece.so.0.0.0: src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/implicit_weak_message.cc.o
src/libsentencepiece.so.0.0.0: src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/int128.cc.o
src/libsentencepiece.so.0.0.0: src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/io_win32.cc.o
src/libsentencepiece.so.0.0.0: src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/message_lite.cc.o
src/libsentencepiece.so.0.0.0: src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/parse_context.cc.o
src/libsentencepiece.so.0.0.0: src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/repeated_field.cc.o
src/libsentencepiece.so.0.0.0: src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/status.cc.o
src/libsentencepiece.so.0.0.0: src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/statusor.cc.o
src/libsentencepiece.so.0.0.0: src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/stringpiece.cc.o
src/libsentencepiece.so.0.0.0: src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/stringprintf.cc.o
src/libsentencepiece.so.0.0.0: src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/structurally_valid.cc.o
src/libsentencepiece.so.0.0.0: src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/strutil.cc.o
src/libsentencepiece.so.0.0.0: src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/time.cc.o
src/libsentencepiece.so.0.0.0: src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/wire_format_lite.cc.o
src/libsentencepiece.so.0.0.0: src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/zero_copy_stream.cc.o
src/libsentencepiece.so.0.0.0: src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/zero_copy_stream_impl.cc.o
src/libsentencepiece.so.0.0.0: src/CMakeFiles/sentencepiece.dir/__/third_party/protobuf-lite/zero_copy_stream_impl_lite.cc.o
src/libsentencepiece.so.0.0.0: src/CMakeFiles/sentencepiece.dir/builtin_pb/sentencepiece.pb.cc.o
src/libsentencepiece.so.0.0.0: src/CMakeFiles/sentencepiece.dir/builtin_pb/sentencepiece_model.pb.cc.o
src/libsentencepiece.so.0.0.0: src/CMakeFiles/sentencepiece.dir/bpe_model.cc.o
src/libsentencepiece.so.0.0.0: src/CMakeFiles/sentencepiece.dir/char_model.cc.o
src/libsentencepiece.so.0.0.0: src/CMakeFiles/sentencepiece.dir/error.cc.o
src/libsentencepiece.so.0.0.0: src/CMakeFiles/sentencepiece.dir/filesystem.cc.o
src/libsentencepiece.so.0.0.0: src/CMakeFiles/sentencepiece.dir/model_factory.cc.o
src/libsentencepiece.so.0.0.0: src/CMakeFiles/sentencepiece.dir/model_interface.cc.o
src/libsentencepiece.so.0.0.0: src/CMakeFiles/sentencepiece.dir/normalizer.cc.o
src/libsentencepiece.so.0.0.0: src/CMakeFiles/sentencepiece.dir/sentencepiece_processor.cc.o
src/libsentencepiece.so.0.0.0: src/CMakeFiles/sentencepiece.dir/unigram_model.cc.o
src/libsentencepiece.so.0.0.0: src/CMakeFiles/sentencepiece.dir/util.cc.o
src/libsentencepiece.so.0.0.0: src/CMakeFiles/sentencepiece.dir/word_model.cc.o
src/libsentencepiece.so.0.0.0: src/CMakeFiles/sentencepiece.dir/__/third_party/absl/flags/flag.cc.o
src/libsentencepiece.so.0.0.0: src/CMakeFiles/sentencepiece.dir/build.make
src/libsentencepiece.so.0.0.0: src/CMakeFiles/sentencepiece.dir/compiler_depend.ts
src/libsentencepiece.so.0.0.0: /usr/lib/libtcmalloc_minimal.so
src/libsentencepiece.so.0.0.0: src/CMakeFiles/sentencepiece.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_41) "Linking CXX shared library libsentencepiece.so"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/sentencepiece.dir/link.txt --verbose=$(VERBOSE)
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && $(CMAKE_COMMAND) -E cmake_symlink_library libsentencepiece.so.0.0.0 libsentencepiece.so.0 libsentencepiece.so

src/libsentencepiece.so.0: src/libsentencepiece.so.0.0.0
	@$(CMAKE_COMMAND) -E touch_nocreate src/libsentencepiece.so.0

src/libsentencepiece.so: src/libsentencepiece.so.0.0.0
	@$(CMAKE_COMMAND) -E touch_nocreate src/libsentencepiece.so

# Rule to build all files generated by this target.
src/CMakeFiles/sentencepiece.dir/build: src/libsentencepiece.so
.PHONY : src/CMakeFiles/sentencepiece.dir/build

src/CMakeFiles/sentencepiece.dir/clean:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && $(CMAKE_COMMAND) -P CMakeFiles/sentencepiece.dir/cmake_clean.cmake
.PHONY : src/CMakeFiles/sentencepiece.dir/clean

src/CMakeFiles/sentencepiece.dir/depend:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Documents/log-anomaly/sentencepiece /home/<USER>/Documents/log-anomaly/sentencepiece/src /home/<USER>/Documents/log-anomaly/sentencepiece/build /home/<USER>/Documents/log-anomaly/sentencepiece/build/src /home/<USER>/Documents/log-anomaly/sentencepiece/build/src/CMakeFiles/sentencepiece.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : src/CMakeFiles/sentencepiece.dir/depend

