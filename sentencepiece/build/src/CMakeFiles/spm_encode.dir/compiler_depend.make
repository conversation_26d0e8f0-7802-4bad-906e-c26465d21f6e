# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

src/CMakeFiles/spm_encode.dir/spm_encode_main.cc.o: /home/<USER>/Documents/log-anomaly/sentencepiece/src/spm_encode_main.cc \
  config.h \
  /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/any.h \
  /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/arena.h \
  /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/arena_impl.h \
  /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/arenastring.h \
  /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/extension_set.h \
  /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/generated_enum_util.h \
  /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/generated_message_table_driven.h \
  /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/generated_message_util.h \
  /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/has_bits.h \
  /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/implicit_weak_message.h \
  /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/io/coded_stream.h \
  /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/io/zero_copy_stream.h \
  /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/map.h \
  /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/map_entry_lite.h \
  /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/map_field_lite.h \
  /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/map_type_handler.h \
  /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/message_lite.h \
  /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/metadata_lite.h \
  /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/parse_context.h \
  /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/port.h \
  /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/port_def.inc \
  /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/port_undef.inc \
  /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/repeated_field.h \
  /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/stubs/casts.h \
  /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/stubs/common.h \
  /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/stubs/hash.h \
  /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/stubs/logging.h \
  /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/stubs/macros.h \
  /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/stubs/once.h \
  /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/stubs/platform_macros.h \
  /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/stubs/port.h \
  /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/stubs/stringpiece.h \
  /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/stubs/strutil.h \
  /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/wire_format_lite.h \
  /home/<USER>/Documents/log-anomaly/sentencepiece/src/builtin_pb/sentencepiece.pb.h \
  /home/<USER>/Documents/log-anomaly/sentencepiece/src/builtin_pb/sentencepiece_model.pb.h \
  /home/<USER>/Documents/log-anomaly/sentencepiece/src/common.h \
  /home/<USER>/Documents/log-anomaly/sentencepiece/src/filesystem.h \
  /home/<USER>/Documents/log-anomaly/sentencepiece/src/init.h \
  /home/<USER>/Documents/log-anomaly/sentencepiece/src/sentencepiece_processor.h \
  /home/<USER>/Documents/log-anomaly/sentencepiece/src/sentencepiece_trainer.h \
  /home/<USER>/Documents/log-anomaly/sentencepiece/src/trainer_interface.h \
  /home/<USER>/Documents/log-anomaly/sentencepiece/src/util.h \
  /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/absl/container/flat_hash_map.h \
  /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/absl/flags/flag.h \
  /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/absl/flags/parse.h \
  /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/absl/flags/usage.h \
  /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/absl/strings/numbers.h \
  /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/absl/strings/str_cat.h \
  /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/absl/strings/str_join.h \
  /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/absl/strings/string_view.h \
  /home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/message_lite.h \
  /usr/include/alloca.h \
  /usr/include/asm-generic/bitsperlong.h \
  /usr/include/asm-generic/errno-base.h \
  /usr/include/asm-generic/errno.h \
  /usr/include/asm-generic/int-ll64.h \
  /usr/include/asm-generic/param.h \
  /usr/include/asm-generic/posix_types.h \
  /usr/include/asm-generic/types.h \
  /usr/include/asm/bitsperlong.h \
  /usr/include/asm/errno.h \
  /usr/include/asm/param.h \
  /usr/include/asm/posix_types.h \
  /usr/include/asm/posix_types_64.h \
  /usr/include/asm/types.h \
  /usr/include/assert.h \
  /usr/include/bits/atomic_wide_counter.h \
  /usr/include/bits/byteswap.h \
  /usr/include/bits/confname.h \
  /usr/include/bits/cpu-set.h \
  /usr/include/bits/endian.h \
  /usr/include/bits/endianness.h \
  /usr/include/bits/environments.h \
  /usr/include/bits/errno.h \
  /usr/include/bits/floatn-common.h \
  /usr/include/bits/floatn.h \
  /usr/include/bits/flt-eval-method.h \
  /usr/include/bits/fp-fast.h \
  /usr/include/bits/fp-logb.h \
  /usr/include/bits/getopt_core.h \
  /usr/include/bits/getopt_posix.h \
  /usr/include/bits/iscanonical.h \
  /usr/include/bits/libc-header-start.h \
  /usr/include/bits/libm-simd-decl-stubs.h \
  /usr/include/bits/local_lim.h \
  /usr/include/bits/locale.h \
  /usr/include/bits/long-double.h \
  /usr/include/bits/math-vector.h \
  /usr/include/bits/mathcalls-helper-functions.h \
  /usr/include/bits/mathcalls-macros.h \
  /usr/include/bits/mathcalls-narrow.h \
  /usr/include/bits/mathcalls.h \
  /usr/include/bits/param.h \
  /usr/include/bits/posix1_lim.h \
  /usr/include/bits/posix2_lim.h \
  /usr/include/bits/posix_opt.h \
  /usr/include/bits/pthread_stack_min-dynamic.h \
  /usr/include/bits/pthreadtypes-arch.h \
  /usr/include/bits/pthreadtypes.h \
  /usr/include/bits/sched.h \
  /usr/include/bits/select.h \
  /usr/include/bits/setjmp.h \
  /usr/include/bits/sigaction.h \
  /usr/include/bits/sigcontext.h \
  /usr/include/bits/sigevent-consts.h \
  /usr/include/bits/siginfo-arch.h \
  /usr/include/bits/siginfo-consts-arch.h \
  /usr/include/bits/siginfo-consts.h \
  /usr/include/bits/signal_ext.h \
  /usr/include/bits/signum-arch.h \
  /usr/include/bits/signum-generic.h \
  /usr/include/bits/sigstack.h \
  /usr/include/bits/sigstksz.h \
  /usr/include/bits/sigthread.h \
  /usr/include/bits/ss_flags.h \
  /usr/include/bits/stdint-intn.h \
  /usr/include/bits/stdint-least.h \
  /usr/include/bits/stdint-uintn.h \
  /usr/include/bits/stdio.h \
  /usr/include/bits/stdio_lim.h \
  /usr/include/bits/stdlib-bsearch.h \
  /usr/include/bits/stdlib-float.h \
  /usr/include/bits/struct_mutex.h \
  /usr/include/bits/struct_rwlock.h \
  /usr/include/bits/thread-shared-types.h \
  /usr/include/bits/time.h \
  /usr/include/bits/time64.h \
  /usr/include/bits/timesize.h \
  /usr/include/bits/timex.h \
  /usr/include/bits/types.h \
  /usr/include/bits/types/FILE.h \
  /usr/include/bits/types/__FILE.h \
  /usr/include/bits/types/__fpos64_t.h \
  /usr/include/bits/types/__fpos_t.h \
  /usr/include/bits/types/__locale_t.h \
  /usr/include/bits/types/__mbstate_t.h \
  /usr/include/bits/types/__sigset_t.h \
  /usr/include/bits/types/__sigval_t.h \
  /usr/include/bits/types/clock_t.h \
  /usr/include/bits/types/clockid_t.h \
  /usr/include/bits/types/cookie_io_functions_t.h \
  /usr/include/bits/types/error_t.h \
  /usr/include/bits/types/locale_t.h \
  /usr/include/bits/types/mbstate_t.h \
  /usr/include/bits/types/sig_atomic_t.h \
  /usr/include/bits/types/sigevent_t.h \
  /usr/include/bits/types/siginfo_t.h \
  /usr/include/bits/types/sigset_t.h \
  /usr/include/bits/types/sigval_t.h \
  /usr/include/bits/types/stack_t.h \
  /usr/include/bits/types/struct_FILE.h \
  /usr/include/bits/types/struct___jmp_buf_tag.h \
  /usr/include/bits/types/struct_itimerspec.h \
  /usr/include/bits/types/struct_sched_param.h \
  /usr/include/bits/types/struct_sigstack.h \
  /usr/include/bits/types/struct_timespec.h \
  /usr/include/bits/types/struct_timeval.h \
  /usr/include/bits/types/struct_tm.h \
  /usr/include/bits/types/time_t.h \
  /usr/include/bits/types/timer_t.h \
  /usr/include/bits/types/wint_t.h \
  /usr/include/bits/typesizes.h \
  /usr/include/bits/uintn-identity.h \
  /usr/include/bits/uio_lim.h \
  /usr/include/bits/unistd_ext.h \
  /usr/include/bits/waitflags.h \
  /usr/include/bits/waitstatus.h \
  /usr/include/bits/wchar.h \
  /usr/include/bits/wctype-wchar.h \
  /usr/include/bits/wordsize.h \
  /usr/include/bits/xopen_lim.h \
  /usr/include/byteswap.h \
  /usr/include/c++/15.1.1/algorithm \
  /usr/include/c++/15.1.1/array \
  /usr/include/c++/15.1.1/atomic \
  /usr/include/c++/15.1.1/backward/auto_ptr.h \
  /usr/include/c++/15.1.1/backward/binders.h \
  /usr/include/c++/15.1.1/bit \
  /usr/include/c++/15.1.1/bits/algorithmfwd.h \
  /usr/include/c++/15.1.1/bits/align.h \
  /usr/include/c++/15.1.1/bits/alloc_traits.h \
  /usr/include/c++/15.1.1/bits/allocated_ptr.h \
  /usr/include/c++/15.1.1/bits/allocator.h \
  /usr/include/c++/15.1.1/bits/atomic_base.h \
  /usr/include/c++/15.1.1/bits/atomic_lockfree_defines.h \
  /usr/include/c++/15.1.1/bits/basic_ios.h \
  /usr/include/c++/15.1.1/bits/basic_ios.tcc \
  /usr/include/c++/15.1.1/bits/basic_string.h \
  /usr/include/c++/15.1.1/bits/basic_string.tcc \
  /usr/include/c++/15.1.1/bits/char_traits.h \
  /usr/include/c++/15.1.1/bits/charconv.h \
  /usr/include/c++/15.1.1/bits/chrono.h \
  /usr/include/c++/15.1.1/bits/codecvt.h \
  /usr/include/c++/15.1.1/bits/concept_check.h \
  /usr/include/c++/15.1.1/bits/cpp_type_traits.h \
  /usr/include/c++/15.1.1/bits/cxxabi_forced.h \
  /usr/include/c++/15.1.1/bits/cxxabi_init_exception.h \
  /usr/include/c++/15.1.1/bits/enable_special_members.h \
  /usr/include/c++/15.1.1/bits/erase_if.h \
  /usr/include/c++/15.1.1/bits/exception.h \
  /usr/include/c++/15.1.1/bits/exception_defines.h \
  /usr/include/c++/15.1.1/bits/exception_ptr.h \
  /usr/include/c++/15.1.1/bits/fstream.tcc \
  /usr/include/c++/15.1.1/bits/functexcept.h \
  /usr/include/c++/15.1.1/bits/functional_hash.h \
  /usr/include/c++/15.1.1/bits/hash_bytes.h \
  /usr/include/c++/15.1.1/bits/hashtable.h \
  /usr/include/c++/15.1.1/bits/hashtable_policy.h \
  /usr/include/c++/15.1.1/bits/invoke.h \
  /usr/include/c++/15.1.1/bits/ios_base.h \
  /usr/include/c++/15.1.1/bits/istream.tcc \
  /usr/include/c++/15.1.1/bits/locale_classes.h \
  /usr/include/c++/15.1.1/bits/locale_classes.tcc \
  /usr/include/c++/15.1.1/bits/locale_facets.h \
  /usr/include/c++/15.1.1/bits/locale_facets.tcc \
  /usr/include/c++/15.1.1/bits/localefwd.h \
  /usr/include/c++/15.1.1/bits/memory_resource.h \
  /usr/include/c++/15.1.1/bits/memoryfwd.h \
  /usr/include/c++/15.1.1/bits/move.h \
  /usr/include/c++/15.1.1/bits/nested_exception.h \
  /usr/include/c++/15.1.1/bits/new_allocator.h \
  /usr/include/c++/15.1.1/bits/node_handle.h \
  /usr/include/c++/15.1.1/bits/ostream.h \
  /usr/include/c++/15.1.1/bits/ostream.tcc \
  /usr/include/c++/15.1.1/bits/ostream_insert.h \
  /usr/include/c++/15.1.1/bits/parse_numbers.h \
  /usr/include/c++/15.1.1/bits/postypes.h \
  /usr/include/c++/15.1.1/bits/predefined_ops.h \
  /usr/include/c++/15.1.1/bits/ptr_traits.h \
  /usr/include/c++/15.1.1/bits/random.h \
  /usr/include/c++/15.1.1/bits/random.tcc \
  /usr/include/c++/15.1.1/bits/range_access.h \
  /usr/include/c++/15.1.1/bits/refwrap.h \
  /usr/include/c++/15.1.1/bits/requires_hosted.h \
  /usr/include/c++/15.1.1/bits/shared_ptr.h \
  /usr/include/c++/15.1.1/bits/shared_ptr_atomic.h \
  /usr/include/c++/15.1.1/bits/shared_ptr_base.h \
  /usr/include/c++/15.1.1/bits/specfun.h \
  /usr/include/c++/15.1.1/bits/sstream.tcc \
  /usr/include/c++/15.1.1/bits/std_abs.h \
  /usr/include/c++/15.1.1/bits/std_function.h \
  /usr/include/c++/15.1.1/bits/std_mutex.h \
  /usr/include/c++/15.1.1/bits/std_thread.h \
  /usr/include/c++/15.1.1/bits/stl_algo.h \
  /usr/include/c++/15.1.1/bits/stl_algobase.h \
  /usr/include/c++/15.1.1/bits/stl_bvector.h \
  /usr/include/c++/15.1.1/bits/stl_construct.h \
  /usr/include/c++/15.1.1/bits/stl_function.h \
  /usr/include/c++/15.1.1/bits/stl_heap.h \
  /usr/include/c++/15.1.1/bits/stl_iterator.h \
  /usr/include/c++/15.1.1/bits/stl_iterator_base_funcs.h \
  /usr/include/c++/15.1.1/bits/stl_iterator_base_types.h \
  /usr/include/c++/15.1.1/bits/stl_map.h \
  /usr/include/c++/15.1.1/bits/stl_multimap.h \
  /usr/include/c++/15.1.1/bits/stl_multiset.h \
  /usr/include/c++/15.1.1/bits/stl_numeric.h \
  /usr/include/c++/15.1.1/bits/stl_pair.h \
  /usr/include/c++/15.1.1/bits/stl_raw_storage_iter.h \
  /usr/include/c++/15.1.1/bits/stl_relops.h \
  /usr/include/c++/15.1.1/bits/stl_set.h \
  /usr/include/c++/15.1.1/bits/stl_tempbuf.h \
  /usr/include/c++/15.1.1/bits/stl_tree.h \
  /usr/include/c++/15.1.1/bits/stl_uninitialized.h \
  /usr/include/c++/15.1.1/bits/stl_vector.h \
  /usr/include/c++/15.1.1/bits/stream_iterator.h \
  /usr/include/c++/15.1.1/bits/streambuf.tcc \
  /usr/include/c++/15.1.1/bits/streambuf_iterator.h \
  /usr/include/c++/15.1.1/bits/string_view.tcc \
  /usr/include/c++/15.1.1/bits/stringfwd.h \
  /usr/include/c++/15.1.1/bits/this_thread_sleep.h \
  /usr/include/c++/15.1.1/bits/uniform_int_dist.h \
  /usr/include/c++/15.1.1/bits/unique_lock.h \
  /usr/include/c++/15.1.1/bits/unique_ptr.h \
  /usr/include/c++/15.1.1/bits/unordered_map.h \
  /usr/include/c++/15.1.1/bits/unordered_set.h \
  /usr/include/c++/15.1.1/bits/uses_allocator.h \
  /usr/include/c++/15.1.1/bits/uses_allocator_args.h \
  /usr/include/c++/15.1.1/bits/utility.h \
  /usr/include/c++/15.1.1/bits/vector.tcc \
  /usr/include/c++/15.1.1/bits/version.h \
  /usr/include/c++/15.1.1/cassert \
  /usr/include/c++/15.1.1/cctype \
  /usr/include/c++/15.1.1/cerrno \
  /usr/include/c++/15.1.1/climits \
  /usr/include/c++/15.1.1/clocale \
  /usr/include/c++/15.1.1/cmath \
  /usr/include/c++/15.1.1/compare \
  /usr/include/c++/15.1.1/concepts \
  /usr/include/c++/15.1.1/cstddef \
  /usr/include/c++/15.1.1/cstdint \
  /usr/include/c++/15.1.1/cstdio \
  /usr/include/c++/15.1.1/cstdlib \
  /usr/include/c++/15.1.1/cstring \
  /usr/include/c++/15.1.1/ctime \
  /usr/include/c++/15.1.1/cwchar \
  /usr/include/c++/15.1.1/cwctype \
  /usr/include/c++/15.1.1/debug/assertions.h \
  /usr/include/c++/15.1.1/debug/debug.h \
  /usr/include/c++/15.1.1/exception \
  /usr/include/c++/15.1.1/ext/aligned_buffer.h \
  /usr/include/c++/15.1.1/ext/alloc_traits.h \
  /usr/include/c++/15.1.1/ext/atomicity.h \
  /usr/include/c++/15.1.1/ext/concurrence.h \
  /usr/include/c++/15.1.1/ext/numeric_traits.h \
  /usr/include/c++/15.1.1/ext/string_conversions.h \
  /usr/include/c++/15.1.1/ext/type_traits.h \
  /usr/include/c++/15.1.1/fstream \
  /usr/include/c++/15.1.1/functional \
  /usr/include/c++/15.1.1/initializer_list \
  /usr/include/c++/15.1.1/ios \
  /usr/include/c++/15.1.1/iosfwd \
  /usr/include/c++/15.1.1/iostream \
  /usr/include/c++/15.1.1/istream \
  /usr/include/c++/15.1.1/iterator \
  /usr/include/c++/15.1.1/limits \
  /usr/include/c++/15.1.1/map \
  /usr/include/c++/15.1.1/memory \
  /usr/include/c++/15.1.1/mutex \
  /usr/include/c++/15.1.1/new \
  /usr/include/c++/15.1.1/numeric \
  /usr/include/c++/15.1.1/ostream \
  /usr/include/c++/15.1.1/pstl/execution_defs.h \
  /usr/include/c++/15.1.1/pstl/glue_algorithm_defs.h \
  /usr/include/c++/15.1.1/pstl/glue_memory_defs.h \
  /usr/include/c++/15.1.1/pstl/glue_numeric_defs.h \
  /usr/include/c++/15.1.1/pstl/pstl_config.h \
  /usr/include/c++/15.1.1/random \
  /usr/include/c++/15.1.1/ratio \
  /usr/include/c++/15.1.1/set \
  /usr/include/c++/15.1.1/sstream \
  /usr/include/c++/15.1.1/stdexcept \
  /usr/include/c++/15.1.1/stdlib.h \
  /usr/include/c++/15.1.1/streambuf \
  /usr/include/c++/15.1.1/string \
  /usr/include/c++/15.1.1/string_view \
  /usr/include/c++/15.1.1/system_error \
  /usr/include/c++/15.1.1/thread \
  /usr/include/c++/15.1.1/tr1/bessel_function.tcc \
  /usr/include/c++/15.1.1/tr1/beta_function.tcc \
  /usr/include/c++/15.1.1/tr1/ell_integral.tcc \
  /usr/include/c++/15.1.1/tr1/exp_integral.tcc \
  /usr/include/c++/15.1.1/tr1/gamma.tcc \
  /usr/include/c++/15.1.1/tr1/hypergeometric.tcc \
  /usr/include/c++/15.1.1/tr1/legendre_function.tcc \
  /usr/include/c++/15.1.1/tr1/modified_bessel_func.tcc \
  /usr/include/c++/15.1.1/tr1/poly_hermite.tcc \
  /usr/include/c++/15.1.1/tr1/poly_laguerre.tcc \
  /usr/include/c++/15.1.1/tr1/riemann_zeta.tcc \
  /usr/include/c++/15.1.1/tr1/special_function_util.h \
  /usr/include/c++/15.1.1/tuple \
  /usr/include/c++/15.1.1/type_traits \
  /usr/include/c++/15.1.1/typeinfo \
  /usr/include/c++/15.1.1/unordered_map \
  /usr/include/c++/15.1.1/unordered_set \
  /usr/include/c++/15.1.1/utility \
  /usr/include/c++/15.1.1/vector \
  /usr/include/c++/15.1.1/x86_64-pc-linux-gnu/bits/atomic_word.h \
  /usr/include/c++/15.1.1/x86_64-pc-linux-gnu/bits/basic_file.h \
  /usr/include/c++/15.1.1/x86_64-pc-linux-gnu/bits/c++allocator.h \
  /usr/include/c++/15.1.1/x86_64-pc-linux-gnu/bits/c++config.h \
  /usr/include/c++/15.1.1/x86_64-pc-linux-gnu/bits/c++io.h \
  /usr/include/c++/15.1.1/x86_64-pc-linux-gnu/bits/c++locale.h \
  /usr/include/c++/15.1.1/x86_64-pc-linux-gnu/bits/cpu_defines.h \
  /usr/include/c++/15.1.1/x86_64-pc-linux-gnu/bits/ctype_base.h \
  /usr/include/c++/15.1.1/x86_64-pc-linux-gnu/bits/ctype_inline.h \
  /usr/include/c++/15.1.1/x86_64-pc-linux-gnu/bits/error_constants.h \
  /usr/include/c++/15.1.1/x86_64-pc-linux-gnu/bits/gthr-default.h \
  /usr/include/c++/15.1.1/x86_64-pc-linux-gnu/bits/gthr.h \
  /usr/include/c++/15.1.1/x86_64-pc-linux-gnu/bits/opt_random.h \
  /usr/include/c++/15.1.1/x86_64-pc-linux-gnu/bits/os_defines.h \
  /usr/include/ctype.h \
  /usr/include/endian.h \
  /usr/include/errno.h \
  /usr/include/features-time64.h \
  /usr/include/features.h \
  /usr/include/gnu/stubs-64.h \
  /usr/include/gnu/stubs.h \
  /usr/include/limits.h \
  /usr/include/linux/close_range.h \
  /usr/include/linux/errno.h \
  /usr/include/linux/limits.h \
  /usr/include/linux/param.h \
  /usr/include/linux/posix_types.h \
  /usr/include/linux/sched/types.h \
  /usr/include/linux/stddef.h \
  /usr/include/linux/types.h \
  /usr/include/locale.h \
  /usr/include/math.h \
  /usr/include/pthread.h \
  /usr/include/sched.h \
  /usr/include/signal.h \
  /usr/include/stdc-predef.h \
  /usr/include/stdint.h \
  /usr/include/stdio.h \
  /usr/include/stdlib.h \
  /usr/include/string.h \
  /usr/include/strings.h \
  /usr/include/sys/cdefs.h \
  /usr/include/sys/param.h \
  /usr/include/sys/select.h \
  /usr/include/sys/single_threaded.h \
  /usr/include/sys/types.h \
  /usr/include/sys/ucontext.h \
  /usr/include/time.h \
  /usr/include/unistd.h \
  /usr/include/wchar.h \
  /usr/include/wctype.h \
  /usr/lib/gcc/x86_64-pc-linux-gnu/15.1.1/include/limits.h \
  /usr/lib/gcc/x86_64-pc-linux-gnu/15.1.1/include/stdarg.h \
  /usr/lib/gcc/x86_64-pc-linux-gnu/15.1.1/include/stddef.h \
  /usr/lib/gcc/x86_64-pc-linux-gnu/15.1.1/include/stdint.h \
  /usr/lib/gcc/x86_64-pc-linux-gnu/15.1.1/include/syslimits.h

src/spm_encode: /usr/lib/Scrt1.o \
  /usr/lib/crti.o \
  /usr/lib/crtn.o \
  /usr/lib/libc.so \
  /usr/lib/libgcc_s.so \
  /usr/lib/libgcc_s.so.1 \
  /usr/lib/libm.so \
  /usr/lib/libstdc++.so \
  /usr/lib/gcc/x86_64-pc-linux-gnu/15.1.1/crtbeginS.o \
  /usr/lib/gcc/x86_64-pc-linux-gnu/15.1.1/crtendS.o \
  /usr/lib/gcc/x86_64-pc-linux-gnu/15.1.1/libgcc.a \
  /usr/lib/ld-linux-x86-64.so.2 \
  /usr/lib/libc.so.6 \
  /usr/lib/libc_nonshared.a \
  /usr/lib/libm.so.6 \
  /usr/lib/libmvec.so.1 \
  /usr/lib/libtcmalloc_minimal.so \
  src/CMakeFiles/spm_encode.dir/spm_encode_main.cc.o \
  src/libsentencepiece.so.0.0.0


src/libsentencepiece.so.0.0.0:

/usr/lib/libtcmalloc_minimal.so:

/usr/lib/libmvec.so.1:

/usr/lib/libm.so.6:

/usr/lib/libc_nonshared.a:

/usr/lib/ld-linux-x86-64.so.2:

/usr/lib/libm.so:

/usr/lib/libgcc_s.so.1:

src/CMakeFiles/spm_encode.dir/spm_encode_main.cc.o:

/usr/lib/crtn.o:

/usr/lib/crti.o:

/usr/lib/Scrt1.o:

/usr/lib/gcc/x86_64-pc-linux-gnu/15.1.1/include/stdint.h:

/usr/lib/gcc/x86_64-pc-linux-gnu/15.1.1/include/stddef.h:

/usr/lib/gcc/x86_64-pc-linux-gnu/15.1.1/include/stdarg.h:

/usr/include/unistd.h:

/usr/include/sys/types.h:

/usr/lib/gcc/x86_64-pc-linux-gnu/15.1.1/libgcc.a:

/usr/include/sys/cdefs.h:

/usr/include/stdlib.h:

/usr/include/sys/ucontext.h:

/usr/include/stdc-predef.h:

/usr/include/strings.h:

/usr/include/signal.h:

/usr/include/pthread.h:

/usr/include/math.h:

/usr/include/linux/types.h:

/usr/include/linux/stddef.h:

/usr/include/linux/limits.h:

/usr/include/features-time64.h:

/usr/include/ctype.h:

/usr/include/c++/15.1.1/x86_64-pc-linux-gnu/bits/opt_random.h:

/usr/include/c++/15.1.1/x86_64-pc-linux-gnu/bits/gthr.h:

/usr/include/sys/select.h:

/usr/include/c++/15.1.1/x86_64-pc-linux-gnu/bits/os_defines.h:

/usr/include/c++/15.1.1/x86_64-pc-linux-gnu/bits/gthr-default.h:

/usr/include/c++/15.1.1/x86_64-pc-linux-gnu/bits/error_constants.h:

/usr/include/c++/15.1.1/x86_64-pc-linux-gnu/bits/ctype_inline.h:

/usr/include/c++/15.1.1/x86_64-pc-linux-gnu/bits/c++locale.h:

/usr/include/c++/15.1.1/x86_64-pc-linux-gnu/bits/c++io.h:

/usr/include/c++/15.1.1/x86_64-pc-linux-gnu/bits/c++allocator.h:

/usr/include/c++/15.1.1/x86_64-pc-linux-gnu/bits/atomic_word.h:

/usr/include/c++/15.1.1/utility:

/usr/include/c++/15.1.1/unordered_set:

/usr/include/c++/15.1.1/unordered_map:

/usr/include/c++/15.1.1/typeinfo:

/usr/include/c++/15.1.1/type_traits:

/usr/include/c++/15.1.1/tuple:

/usr/include/c++/15.1.1/tr1/special_function_util.h:

/usr/include/c++/15.1.1/tr1/riemann_zeta.tcc:

/usr/include/c++/15.1.1/x86_64-pc-linux-gnu/bits/c++config.h:

/usr/include/c++/15.1.1/tr1/poly_laguerre.tcc:

/usr/include/c++/15.1.1/tr1/poly_hermite.tcc:

/usr/include/c++/15.1.1/tr1/exp_integral.tcc:

/usr/include/c++/15.1.1/tr1/ell_integral.tcc:

/usr/include/c++/15.1.1/system_error:

/usr/include/c++/15.1.1/stdlib.h:

/usr/lib/gcc/x86_64-pc-linux-gnu/15.1.1/crtendS.o:

/usr/include/c++/15.1.1/stdexcept:

/usr/include/c++/15.1.1/sstream:

/usr/include/c++/15.1.1/set:

/usr/include/c++/15.1.1/ratio:

/usr/include/c++/15.1.1/pstl/pstl_config.h:

/usr/include/gnu/stubs.h:

/usr/include/c++/15.1.1/pstl/glue_algorithm_defs.h:

/usr/include/c++/15.1.1/pstl/execution_defs.h:

/usr/include/c++/15.1.1/ostream:

/usr/lib/gcc/x86_64-pc-linux-gnu/15.1.1/include/syslimits.h:

/usr/include/c++/15.1.1/new:

/usr/include/c++/15.1.1/limits:

/usr/include/c++/15.1.1/iterator:

/usr/include/c++/15.1.1/ios:

/usr/include/c++/15.1.1/initializer_list:

/usr/include/c++/15.1.1/ext/type_traits.h:

/usr/include/c++/15.1.1/ext/concurrence.h:

/usr/include/c++/15.1.1/ext/atomicity.h:

/usr/include/c++/15.1.1/ext/alloc_traits.h:

/usr/include/c++/15.1.1/exception:

/usr/lib/libgcc_s.so:

/usr/include/c++/15.1.1/debug/debug.h:

/usr/include/c++/15.1.1/cwchar:

/usr/include/linux/close_range.h:

/usr/include/c++/15.1.1/ctime:

/usr/include/c++/15.1.1/cstring:

/usr/include/c++/15.1.1/cstdlib:

/usr/include/c++/15.1.1/cstdio:

/usr/include/c++/15.1.1/tr1/legendre_function.tcc:

/usr/include/c++/15.1.1/concepts:

/usr/include/c++/15.1.1/compare:

/usr/include/c++/15.1.1/cmath:

/usr/include/c++/15.1.1/clocale:

/usr/include/c++/15.1.1/climits:

/usr/include/c++/15.1.1/cctype:

/usr/include/c++/15.1.1/cassert:

/usr/include/c++/15.1.1/random:

/usr/include/c++/15.1.1/bits/vector.tcc:

/usr/include/c++/15.1.1/bits/uses_allocator_args.h:

/usr/include/c++/15.1.1/bits/uses_allocator.h:

/usr/include/linux/errno.h:

/usr/include/c++/15.1.1/bits/unordered_map.h:

/usr/include/c++/15.1.1/bits/unique_ptr.h:

/usr/include/c++/15.1.1/bits/uniform_int_dist.h:

/usr/include/c++/15.1.1/x86_64-pc-linux-gnu/bits/basic_file.h:

/usr/include/c++/15.1.1/bits/string_view.tcc:

/usr/include/c++/15.1.1/bits/streambuf_iterator.h:

/usr/include/c++/15.1.1/bits/streambuf.tcc:

/usr/include/c++/15.1.1/bits/stream_iterator.h:

/usr/include/c++/15.1.1/bits/stl_uninitialized.h:

/usr/include/c++/15.1.1/bits/stl_tree.h:

/usr/include/c++/15.1.1/bits/stl_set.h:

/usr/include/c++/15.1.1/bits/stl_raw_storage_iter.h:

/usr/include/c++/15.1.1/bits/stl_numeric.h:

/usr/include/bits/thread-shared-types.h:

/usr/include/bits/stdlib-float.h:

/home/<USER>/Documents/log-anomaly/sentencepiece/third_party/absl/container/flat_hash_map.h:

/usr/include/c++/15.1.1/tr1/bessel_function.tcc:

/usr/include/bits/types/__mbstate_t.h:

/usr/include/linux/param.h:

/usr/include/bits/stdio.h:

/usr/include/c++/15.1.1/istream:

/usr/include/c++/15.1.1/bits/atomic_lockfree_defines.h:

/home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/stubs/logging.h:

/usr/include/bits/signum-generic.h:

/usr/include/string.h:

/home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/implicit_weak_message.h:

/usr/include/bits/sigevent-consts.h:

/usr/include/c++/15.1.1/vector:

/usr/include/bits/posix2_lim.h:

/usr/include/c++/15.1.1/string:

/usr/include/bits/types/stack_t.h:

/usr/include/c++/15.1.1/backward/auto_ptr.h:

/usr/include/bits/select.h:

/home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/stubs/common.h:

/usr/include/bits/types/siginfo_t.h:

/usr/include/c++/15.1.1/bits/stl_function.h:

/usr/include/bits/posix_opt.h:

/usr/include/c++/15.1.1/bits/localefwd.h:

/usr/include/bits/types/clock_t.h:

/usr/include/c++/15.1.1/tr1/gamma.tcc:

/usr/include/bits/mathcalls-narrow.h:

/usr/include/c++/15.1.1/bits/functional_hash.h:

/usr/include/bits/mathcalls-macros.h:

/usr/include/c++/15.1.1/cwctype:

/usr/include/c++/15.1.1/bits/stl_vector.h:

/usr/include/asm-generic/int-ll64.h:

/usr/include/bits/types/struct_itimerspec.h:

/usr/include/c++/15.1.1/bits/stl_algobase.h:

/home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/any.h:

/usr/include/bits/mathcalls-helper-functions.h:

/usr/include/bits/long-double.h:

/usr/include/bits/libm-simd-decl-stubs.h:

/usr/lib/libstdc++.so:

/usr/include/locale.h:

/usr/include/c++/15.1.1/bits/memoryfwd.h:

/home/<USER>/Documents/log-anomaly/sentencepiece/src/init.h:

/usr/include/bits/siginfo-consts.h:

/usr/include/c++/15.1.1/bits/atomic_base.h:

/usr/include/bits/libc-header-start.h:

/usr/include/errno.h:

/usr/include/bits/getopt_posix.h:

/usr/include/bits/fp-logb.h:

/usr/include/c++/15.1.1/bits/node_handle.h:

/usr/include/asm-generic/errno.h:

/usr/include/c++/15.1.1/iosfwd:

/home/<USER>/Documents/log-anomaly/sentencepiece/third_party/absl/flags/flag.h:

/usr/include/bits/flt-eval-method.h:

/usr/include/linux/posix_types.h:

/usr/include/bits/types/FILE.h:

/usr/include/bits/floatn.h:

/usr/include/bits/floatn-common.h:

/usr/include/bits/endianness.h:

/usr/include/bits/cpu-set.h:

/usr/include/byteswap.h:

/usr/include/c++/15.1.1/bits/stl_bvector.h:

/usr/include/c++/15.1.1/ext/string_conversions.h:

/usr/include/bits/confname.h:

/usr/include/assert.h:

/usr/include/c++/15.1.1/fstream:

/usr/include/asm/types.h:

/usr/include/bits/setjmp.h:

/home/<USER>/Documents/log-anomaly/sentencepiece/third_party/absl/strings/string_view.h:

/usr/include/bits/ss_flags.h:

/usr/include/bits/environments.h:

/usr/include/bits/types/struct_FILE.h:

/usr/include/c++/15.1.1/thread:

/usr/include/c++/15.1.1/bits/sstream.tcc:

/usr/include/bits/time.h:

/usr/include/asm/posix_types.h:

/usr/lib/libc.so:

/usr/include/bits/pthreadtypes.h:

/usr/include/bits/sigcontext.h:

/usr/include/bits/time64.h:

/usr/include/c++/15.1.1/bits/hashtable.h:

/usr/include/c++/15.1.1/cstdint:

/usr/include/asm/bitsperlong.h:

/usr/include/asm-generic/errno-base.h:

/usr/include/c++/15.1.1/numeric:

/usr/include/c++/15.1.1/cerrno:

/usr/include/asm-generic/posix_types.h:

/usr/include/bits/sigstksz.h:

/home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/stubs/hash.h:

/usr/include/bits/struct_mutex.h:

/home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/stubs/once.h:

/usr/include/c++/15.1.1/bits/specfun.h:

/usr/include/c++/15.1.1/cstddef:

/usr/include/c++/15.1.1/bits/stl_pair.h:

/usr/include/bits/pthreadtypes-arch.h:

/home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/wire_format_lite.h:

/usr/include/bits/math-vector.h:

/usr/include/bits/uio_lim.h:

/home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/port_undef.inc:

/home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/stubs/macros.h:

/usr/include/c++/15.1.1/bits/exception_ptr.h:

/home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/port.h:

/usr/include/c++/15.1.1/bits/version.h:

/home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/io/zero_copy_stream.h:

/usr/include/bits/siginfo-arch.h:

/home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/arena.h:

/usr/include/time.h:

/home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/map_type_handler.h:

/usr/include/c++/15.1.1/bits/shared_ptr_base.h:

/home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/map_entry_lite.h:

/usr/include/c++/15.1.1/tr1/modified_bessel_func.tcc:

/home/<USER>/Documents/log-anomaly/sentencepiece/src/util.h:

/usr/include/bits/endian.h:

/usr/include/c++/15.1.1/bits/stl_iterator.h:

/usr/include/bits/stdint-uintn.h:

/usr/include/asm/errno.h:

/home/<USER>/Documents/log-anomaly/sentencepiece/src/builtin_pb/sentencepiece_model.pb.h:

/usr/include/bits/atomic_wide_counter.h:

/usr/include/c++/15.1.1/bits/alloc_traits.h:

/home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/parse_context.h:

/usr/include/c++/15.1.1/bits/fstream.tcc:

/home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/generated_message_table_driven.h:

/home/<USER>/Documents/log-anomaly/sentencepiece/src/sentencepiece_trainer.h:

/home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/io/coded_stream.h:

/usr/include/bits/stdint-least.h:

/usr/include/stdint.h:

/usr/include/endian.h:

/usr/include/c++/15.1.1/bits/stl_algo.h:

/usr/include/c++/15.1.1/bits/utility.h:

/home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/arena_impl.h:

/home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/stubs/platform_macros.h:

/usr/include/bits/pthread_stack_min-dynamic.h:

/usr/include/sys/single_threaded.h:

/usr/include/c++/15.1.1/map:

/home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/port_def.inc:

/usr/include/bits/getopt_core.h:

/usr/include/bits/siginfo-consts-arch.h:

/usr/include/bits/sigstack.h:

/home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/repeated_field.h:

/home/<USER>/Documents/log-anomaly/sentencepiece/src/trainer_interface.h:

/home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/extension_set.h:

/usr/include/c++/15.1.1/backward/binders.h:

/usr/include/c++/15.1.1/pstl/glue_numeric_defs.h:

/home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/arenastring.h:

/home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/generated_message_util.h:

/home/<USER>/Documents/log-anomaly/sentencepiece/third_party/absl/strings/numbers.h:

/home/<USER>/Documents/log-anomaly/sentencepiece/src/common.h:

/usr/include/c++/15.1.1/bits/parse_numbers.h:

/home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/map.h:

config.h:

/usr/include/bits/unistd_ext.h:

/home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/generated_enum_util.h:

/usr/include/c++/15.1.1/ext/aligned_buffer.h:

/usr/include/bits/stdlib-bsearch.h:

/usr/include/bits/locale.h:

/home/<USER>/Documents/log-anomaly/sentencepiece/src/spm_encode_main.cc:

/usr/include/bits/types/wint_t.h:

/home/<USER>/Documents/log-anomaly/sentencepiece/third_party/absl/strings/str_join.h:

/usr/include/bits/signal_ext.h:

/usr/include/bits/types/mbstate_t.h:

/home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/has_bits.h:

/home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/stubs/stringpiece.h:

/home/<USER>/Documents/log-anomaly/sentencepiece/src/builtin_pb/sentencepiece.pb.h:

/usr/include/c++/15.1.1/bits/exception.h:

/usr/include/bits/errno.h:

/usr/include/c++/15.1.1/bits/requires_hosted.h:

/usr/include/bits/iscanonical.h:

/usr/include/c++/15.1.1/string_view:

/home/<USER>/Documents/log-anomaly/sentencepiece/src/filesystem.h:

/usr/include/c++/15.1.1/bits/stl_multiset.h:

/usr/lib/gcc/x86_64-pc-linux-gnu/15.1.1/crtbeginS.o:

/usr/include/c++/15.1.1/bits/stringfwd.h:

/usr/include/asm-generic/types.h:

/usr/include/asm-generic/bitsperlong.h:

/home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/stubs/port.h:

/home/<USER>/Documents/log-anomaly/sentencepiece/third_party/absl/flags/parse.h:

/home/<USER>/Documents/log-anomaly/sentencepiece/third_party/absl/flags/usage.h:

/usr/include/c++/15.1.1/bits/basic_ios.tcc:

/usr/include/c++/15.1.1/bits/hashtable_policy.h:

/usr/include/c++/15.1.1/bits/hash_bytes.h:

/usr/include/wctype.h:

/usr/include/sched.h:

/usr/include/bits/fp-fast.h:

/home/<USER>/Documents/log-anomaly/sentencepiece/third_party/absl/strings/str_cat.h:

/usr/include/c++/15.1.1/x86_64-pc-linux-gnu/bits/cpu_defines.h:

/usr/include/asm-generic/param.h:

/usr/include/bits/sigthread.h:

/usr/include/alloca.h:

/usr/include/wchar.h:

/usr/include/bits/posix1_lim.h:

/home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/message_lite.h:

/usr/include/bits/timesize.h:

/usr/include/c++/15.1.1/bits/std_function.h:

/usr/include/bits/types.h:

/usr/include/c++/15.1.1/streambuf:

/usr/include/c++/15.1.1/bits/exception_defines.h:

/usr/include/features.h:

/usr/include/c++/15.1.1/ext/numeric_traits.h:

/usr/include/bits/sched.h:

/usr/include/bits/types/__FILE.h:

/usr/include/bits/types/__fpos64_t.h:

/usr/include/c++/15.1.1/bits/stl_relops.h:

/usr/include/c++/15.1.1/bits/ostream_insert.h:

/usr/include/bits/types/__locale_t.h:

/usr/lib/gcc/x86_64-pc-linux-gnu/15.1.1/include/limits.h:

/home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/map_field_lite.h:

/usr/include/bits/types/__sigset_t.h:

/usr/include/gnu/stubs-64.h:

/usr/include/c++/15.1.1/x86_64-pc-linux-gnu/bits/ctype_base.h:

/usr/include/bits/types/__sigval_t.h:

/usr/include/c++/15.1.1/bits/ostream.tcc:

/usr/include/bits/stdint-intn.h:

/usr/include/bits/types/clockid_t.h:

/usr/include/bits/types/cookie_io_functions_t.h:

/usr/include/c++/15.1.1/bits/unordered_set.h:

/usr/include/bits/types/error_t.h:

/usr/include/c++/15.1.1/tr1/beta_function.tcc:

/usr/include/bits/types/__fpos_t.h:

/usr/include/bits/types/sig_atomic_t.h:

/usr/include/bits/typesizes.h:

/usr/include/bits/types/sigevent_t.h:

/usr/include/bits/types/sigset_t.h:

/home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/stubs/casts.h:

/usr/include/bits/types/sigval_t.h:

/home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/metadata_lite.h:

/usr/include/bits/wchar.h:

/usr/include/bits/waitstatus.h:

/usr/include/bits/types/struct___jmp_buf_tag.h:

/usr/include/bits/types/struct_sched_param.h:

/usr/include/c++/15.1.1/bits/enable_special_members.h:

/usr/include/bits/types/struct_sigstack.h:

/usr/include/bits/types/struct_timespec.h:

/usr/include/c++/15.1.1/bits/locale_classes.h:

/usr/include/bits/types/struct_timeval.h:

/usr/include/bits/types/struct_tm.h:

/usr/include/c++/15.1.1/bits/cxxabi_forced.h:

/home/<USER>/Documents/log-anomaly/sentencepiece/src/sentencepiece_processor.h:

/usr/include/bits/types/time_t.h:

/usr/include/bits/types/timer_t.h:

/usr/include/bits/uintn-identity.h:

/usr/include/c++/15.1.1/bits/basic_ios.h:

/usr/include/bits/waitflags.h:

/usr/include/c++/15.1.1/mutex:

/usr/include/c++/15.1.1/functional:

/usr/include/bits/wctype-wchar.h:

/usr/include/bits/xopen_lim.h:

/usr/include/c++/15.1.1/algorithm:

/usr/include/c++/15.1.1/array:

/usr/include/c++/15.1.1/bit:

/usr/include/bits/sigaction.h:

/usr/include/c++/15.1.1/bits/algorithmfwd.h:

/usr/lib/libc.so.6:

/usr/include/c++/15.1.1/bits/align.h:

/usr/include/c++/15.1.1/bits/charconv.h:

/usr/include/bits/signum-arch.h:

/usr/include/c++/15.1.1/bits/cpp_type_traits.h:

/usr/include/bits/byteswap.h:

/usr/include/c++/15.1.1/bits/shared_ptr.h:

/usr/include/c++/15.1.1/bits/allocated_ptr.h:

/usr/include/stdio.h:

/usr/include/bits/wordsize.h:

/usr/include/c++/15.1.1/bits/allocator.h:

/usr/include/c++/15.1.1/bits/memory_resource.h:

/usr/include/c++/15.1.1/bits/basic_string.h:

/usr/include/linux/sched/types.h:

/usr/include/c++/15.1.1/bits/basic_string.tcc:

/usr/include/c++/15.1.1/bits/postypes.h:

/usr/include/limits.h:

/usr/include/c++/15.1.1/bits/char_traits.h:

/usr/include/c++/15.1.1/bits/chrono.h:

/usr/include/bits/timex.h:

/usr/include/c++/15.1.1/bits/codecvt.h:

/usr/include/c++/15.1.1/bits/nested_exception.h:

/usr/include/asm/posix_types_64.h:

/usr/include/c++/15.1.1/bits/concept_check.h:

/usr/include/bits/types/locale_t.h:

/usr/include/c++/15.1.1/atomic:

/usr/include/c++/15.1.1/bits/cxxabi_init_exception.h:

/usr/include/c++/15.1.1/bits/stl_iterator_base_funcs.h:

/usr/include/c++/15.1.1/bits/erase_if.h:

/usr/include/c++/15.1.1/bits/locale_facets.tcc:

/usr/include/c++/15.1.1/bits/invoke.h:

/usr/include/c++/15.1.1/bits/ios_base.h:

/usr/include/c++/15.1.1/bits/this_thread_sleep.h:

/usr/include/bits/local_lim.h:

/usr/include/c++/15.1.1/bits/functexcept.h:

/usr/include/c++/15.1.1/bits/istream.tcc:

/usr/include/c++/15.1.1/bits/locale_classes.tcc:

/usr/include/sys/param.h:

/usr/include/c++/15.1.1/bits/locale_facets.h:

/usr/include/c++/15.1.1/bits/move.h:

/usr/include/asm/param.h:

/usr/include/c++/15.1.1/bits/stl_heap.h:

/usr/include/c++/15.1.1/bits/new_allocator.h:

/usr/include/c++/15.1.1/bits/ostream.h:

/usr/include/c++/15.1.1/pstl/glue_memory_defs.h:

/usr/include/c++/15.1.1/iostream:

/usr/include/c++/15.1.1/bits/predefined_ops.h:

/usr/include/c++/15.1.1/bits/ptr_traits.h:

/home/<USER>/Documents/log-anomaly/sentencepiece/third_party/protobuf-lite/google/protobuf/stubs/strutil.h:

/usr/include/c++/15.1.1/bits/random.h:

/usr/include/c++/15.1.1/bits/random.tcc:

/usr/include/c++/15.1.1/tr1/hypergeometric.tcc:

/usr/include/c++/15.1.1/bits/stl_construct.h:

/usr/include/c++/15.1.1/bits/range_access.h:

/usr/include/bits/mathcalls.h:

/usr/include/c++/15.1.1/bits/refwrap.h:

/usr/include/c++/15.1.1/bits/stl_tempbuf.h:

/usr/include/bits/param.h:

/usr/include/c++/15.1.1/bits/shared_ptr_atomic.h:

/usr/include/bits/struct_rwlock.h:

/usr/include/c++/15.1.1/bits/std_thread.h:

/usr/include/c++/15.1.1/memory:

/usr/include/bits/stdio_lim.h:

/usr/include/c++/15.1.1/bits/std_abs.h:

/usr/include/c++/15.1.1/bits/std_mutex.h:

/usr/include/c++/15.1.1/bits/stl_iterator_base_types.h:

/usr/include/c++/15.1.1/bits/unique_lock.h:

/usr/include/c++/15.1.1/bits/stl_map.h:

/usr/include/c++/15.1.1/debug/assertions.h:

/usr/include/c++/15.1.1/bits/stl_multimap.h:
