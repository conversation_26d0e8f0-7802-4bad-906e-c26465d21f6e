# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Documents/log-anomaly/sentencepiece

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Documents/log-anomaly/sentencepiece/build

# Include any dependencies generated for this target.
include src/CMakeFiles/sentencepiece_train.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include src/CMakeFiles/sentencepiece_train.dir/compiler_depend.make

# Include the progress variables for this target.
include src/CMakeFiles/sentencepiece_train.dir/progress.make

# Include the compile flags for this target's objects.
include src/CMakeFiles/sentencepiece_train.dir/flags.make

src/CMakeFiles/sentencepiece_train.dir/codegen:
.PHONY : src/CMakeFiles/sentencepiece_train.dir/codegen

src/CMakeFiles/sentencepiece_train.dir/builder.cc.o: src/CMakeFiles/sentencepiece_train.dir/flags.make
src/CMakeFiles/sentencepiece_train.dir/builder.cc.o: /home/<USER>/Documents/log-anomaly/sentencepiece/src/builder.cc
src/CMakeFiles/sentencepiece_train.dir/builder.cc.o: src/CMakeFiles/sentencepiece_train.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object src/CMakeFiles/sentencepiece_train.dir/builder.cc.o"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/sentencepiece_train.dir/builder.cc.o -MF CMakeFiles/sentencepiece_train.dir/builder.cc.o.d -o CMakeFiles/sentencepiece_train.dir/builder.cc.o -c /home/<USER>/Documents/log-anomaly/sentencepiece/src/builder.cc

src/CMakeFiles/sentencepiece_train.dir/builder.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sentencepiece_train.dir/builder.cc.i"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/log-anomaly/sentencepiece/src/builder.cc > CMakeFiles/sentencepiece_train.dir/builder.cc.i

src/CMakeFiles/sentencepiece_train.dir/builder.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sentencepiece_train.dir/builder.cc.s"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/log-anomaly/sentencepiece/src/builder.cc -o CMakeFiles/sentencepiece_train.dir/builder.cc.s

src/CMakeFiles/sentencepiece_train.dir/unicode_script.cc.o: src/CMakeFiles/sentencepiece_train.dir/flags.make
src/CMakeFiles/sentencepiece_train.dir/unicode_script.cc.o: /home/<USER>/Documents/log-anomaly/sentencepiece/src/unicode_script.cc
src/CMakeFiles/sentencepiece_train.dir/unicode_script.cc.o: src/CMakeFiles/sentencepiece_train.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object src/CMakeFiles/sentencepiece_train.dir/unicode_script.cc.o"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/sentencepiece_train.dir/unicode_script.cc.o -MF CMakeFiles/sentencepiece_train.dir/unicode_script.cc.o.d -o CMakeFiles/sentencepiece_train.dir/unicode_script.cc.o -c /home/<USER>/Documents/log-anomaly/sentencepiece/src/unicode_script.cc

src/CMakeFiles/sentencepiece_train.dir/unicode_script.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sentencepiece_train.dir/unicode_script.cc.i"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/log-anomaly/sentencepiece/src/unicode_script.cc > CMakeFiles/sentencepiece_train.dir/unicode_script.cc.i

src/CMakeFiles/sentencepiece_train.dir/unicode_script.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sentencepiece_train.dir/unicode_script.cc.s"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/log-anomaly/sentencepiece/src/unicode_script.cc -o CMakeFiles/sentencepiece_train.dir/unicode_script.cc.s

src/CMakeFiles/sentencepiece_train.dir/trainer_factory.cc.o: src/CMakeFiles/sentencepiece_train.dir/flags.make
src/CMakeFiles/sentencepiece_train.dir/trainer_factory.cc.o: /home/<USER>/Documents/log-anomaly/sentencepiece/src/trainer_factory.cc
src/CMakeFiles/sentencepiece_train.dir/trainer_factory.cc.o: src/CMakeFiles/sentencepiece_train.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object src/CMakeFiles/sentencepiece_train.dir/trainer_factory.cc.o"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/sentencepiece_train.dir/trainer_factory.cc.o -MF CMakeFiles/sentencepiece_train.dir/trainer_factory.cc.o.d -o CMakeFiles/sentencepiece_train.dir/trainer_factory.cc.o -c /home/<USER>/Documents/log-anomaly/sentencepiece/src/trainer_factory.cc

src/CMakeFiles/sentencepiece_train.dir/trainer_factory.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sentencepiece_train.dir/trainer_factory.cc.i"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/log-anomaly/sentencepiece/src/trainer_factory.cc > CMakeFiles/sentencepiece_train.dir/trainer_factory.cc.i

src/CMakeFiles/sentencepiece_train.dir/trainer_factory.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sentencepiece_train.dir/trainer_factory.cc.s"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/log-anomaly/sentencepiece/src/trainer_factory.cc -o CMakeFiles/sentencepiece_train.dir/trainer_factory.cc.s

src/CMakeFiles/sentencepiece_train.dir/trainer_interface.cc.o: src/CMakeFiles/sentencepiece_train.dir/flags.make
src/CMakeFiles/sentencepiece_train.dir/trainer_interface.cc.o: /home/<USER>/Documents/log-anomaly/sentencepiece/src/trainer_interface.cc
src/CMakeFiles/sentencepiece_train.dir/trainer_interface.cc.o: src/CMakeFiles/sentencepiece_train.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object src/CMakeFiles/sentencepiece_train.dir/trainer_interface.cc.o"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/sentencepiece_train.dir/trainer_interface.cc.o -MF CMakeFiles/sentencepiece_train.dir/trainer_interface.cc.o.d -o CMakeFiles/sentencepiece_train.dir/trainer_interface.cc.o -c /home/<USER>/Documents/log-anomaly/sentencepiece/src/trainer_interface.cc

src/CMakeFiles/sentencepiece_train.dir/trainer_interface.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sentencepiece_train.dir/trainer_interface.cc.i"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/log-anomaly/sentencepiece/src/trainer_interface.cc > CMakeFiles/sentencepiece_train.dir/trainer_interface.cc.i

src/CMakeFiles/sentencepiece_train.dir/trainer_interface.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sentencepiece_train.dir/trainer_interface.cc.s"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/log-anomaly/sentencepiece/src/trainer_interface.cc -o CMakeFiles/sentencepiece_train.dir/trainer_interface.cc.s

src/CMakeFiles/sentencepiece_train.dir/unigram_model_trainer.cc.o: src/CMakeFiles/sentencepiece_train.dir/flags.make
src/CMakeFiles/sentencepiece_train.dir/unigram_model_trainer.cc.o: /home/<USER>/Documents/log-anomaly/sentencepiece/src/unigram_model_trainer.cc
src/CMakeFiles/sentencepiece_train.dir/unigram_model_trainer.cc.o: src/CMakeFiles/sentencepiece_train.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object src/CMakeFiles/sentencepiece_train.dir/unigram_model_trainer.cc.o"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/sentencepiece_train.dir/unigram_model_trainer.cc.o -MF CMakeFiles/sentencepiece_train.dir/unigram_model_trainer.cc.o.d -o CMakeFiles/sentencepiece_train.dir/unigram_model_trainer.cc.o -c /home/<USER>/Documents/log-anomaly/sentencepiece/src/unigram_model_trainer.cc

src/CMakeFiles/sentencepiece_train.dir/unigram_model_trainer.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sentencepiece_train.dir/unigram_model_trainer.cc.i"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/log-anomaly/sentencepiece/src/unigram_model_trainer.cc > CMakeFiles/sentencepiece_train.dir/unigram_model_trainer.cc.i

src/CMakeFiles/sentencepiece_train.dir/unigram_model_trainer.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sentencepiece_train.dir/unigram_model_trainer.cc.s"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/log-anomaly/sentencepiece/src/unigram_model_trainer.cc -o CMakeFiles/sentencepiece_train.dir/unigram_model_trainer.cc.s

src/CMakeFiles/sentencepiece_train.dir/word_model_trainer.cc.o: src/CMakeFiles/sentencepiece_train.dir/flags.make
src/CMakeFiles/sentencepiece_train.dir/word_model_trainer.cc.o: /home/<USER>/Documents/log-anomaly/sentencepiece/src/word_model_trainer.cc
src/CMakeFiles/sentencepiece_train.dir/word_model_trainer.cc.o: src/CMakeFiles/sentencepiece_train.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object src/CMakeFiles/sentencepiece_train.dir/word_model_trainer.cc.o"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/sentencepiece_train.dir/word_model_trainer.cc.o -MF CMakeFiles/sentencepiece_train.dir/word_model_trainer.cc.o.d -o CMakeFiles/sentencepiece_train.dir/word_model_trainer.cc.o -c /home/<USER>/Documents/log-anomaly/sentencepiece/src/word_model_trainer.cc

src/CMakeFiles/sentencepiece_train.dir/word_model_trainer.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sentencepiece_train.dir/word_model_trainer.cc.i"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/log-anomaly/sentencepiece/src/word_model_trainer.cc > CMakeFiles/sentencepiece_train.dir/word_model_trainer.cc.i

src/CMakeFiles/sentencepiece_train.dir/word_model_trainer.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sentencepiece_train.dir/word_model_trainer.cc.s"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/log-anomaly/sentencepiece/src/word_model_trainer.cc -o CMakeFiles/sentencepiece_train.dir/word_model_trainer.cc.s

src/CMakeFiles/sentencepiece_train.dir/char_model_trainer.cc.o: src/CMakeFiles/sentencepiece_train.dir/flags.make
src/CMakeFiles/sentencepiece_train.dir/char_model_trainer.cc.o: /home/<USER>/Documents/log-anomaly/sentencepiece/src/char_model_trainer.cc
src/CMakeFiles/sentencepiece_train.dir/char_model_trainer.cc.o: src/CMakeFiles/sentencepiece_train.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object src/CMakeFiles/sentencepiece_train.dir/char_model_trainer.cc.o"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/sentencepiece_train.dir/char_model_trainer.cc.o -MF CMakeFiles/sentencepiece_train.dir/char_model_trainer.cc.o.d -o CMakeFiles/sentencepiece_train.dir/char_model_trainer.cc.o -c /home/<USER>/Documents/log-anomaly/sentencepiece/src/char_model_trainer.cc

src/CMakeFiles/sentencepiece_train.dir/char_model_trainer.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sentencepiece_train.dir/char_model_trainer.cc.i"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/log-anomaly/sentencepiece/src/char_model_trainer.cc > CMakeFiles/sentencepiece_train.dir/char_model_trainer.cc.i

src/CMakeFiles/sentencepiece_train.dir/char_model_trainer.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sentencepiece_train.dir/char_model_trainer.cc.s"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/log-anomaly/sentencepiece/src/char_model_trainer.cc -o CMakeFiles/sentencepiece_train.dir/char_model_trainer.cc.s

src/CMakeFiles/sentencepiece_train.dir/bpe_model_trainer.cc.o: src/CMakeFiles/sentencepiece_train.dir/flags.make
src/CMakeFiles/sentencepiece_train.dir/bpe_model_trainer.cc.o: /home/<USER>/Documents/log-anomaly/sentencepiece/src/bpe_model_trainer.cc
src/CMakeFiles/sentencepiece_train.dir/bpe_model_trainer.cc.o: src/CMakeFiles/sentencepiece_train.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object src/CMakeFiles/sentencepiece_train.dir/bpe_model_trainer.cc.o"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/sentencepiece_train.dir/bpe_model_trainer.cc.o -MF CMakeFiles/sentencepiece_train.dir/bpe_model_trainer.cc.o.d -o CMakeFiles/sentencepiece_train.dir/bpe_model_trainer.cc.o -c /home/<USER>/Documents/log-anomaly/sentencepiece/src/bpe_model_trainer.cc

src/CMakeFiles/sentencepiece_train.dir/bpe_model_trainer.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sentencepiece_train.dir/bpe_model_trainer.cc.i"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/log-anomaly/sentencepiece/src/bpe_model_trainer.cc > CMakeFiles/sentencepiece_train.dir/bpe_model_trainer.cc.i

src/CMakeFiles/sentencepiece_train.dir/bpe_model_trainer.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sentencepiece_train.dir/bpe_model_trainer.cc.s"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/log-anomaly/sentencepiece/src/bpe_model_trainer.cc -o CMakeFiles/sentencepiece_train.dir/bpe_model_trainer.cc.s

src/CMakeFiles/sentencepiece_train.dir/sentencepiece_trainer.cc.o: src/CMakeFiles/sentencepiece_train.dir/flags.make
src/CMakeFiles/sentencepiece_train.dir/sentencepiece_trainer.cc.o: /home/<USER>/Documents/log-anomaly/sentencepiece/src/sentencepiece_trainer.cc
src/CMakeFiles/sentencepiece_train.dir/sentencepiece_trainer.cc.o: src/CMakeFiles/sentencepiece_train.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building CXX object src/CMakeFiles/sentencepiece_train.dir/sentencepiece_trainer.cc.o"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/sentencepiece_train.dir/sentencepiece_trainer.cc.o -MF CMakeFiles/sentencepiece_train.dir/sentencepiece_trainer.cc.o.d -o CMakeFiles/sentencepiece_train.dir/sentencepiece_trainer.cc.o -c /home/<USER>/Documents/log-anomaly/sentencepiece/src/sentencepiece_trainer.cc

src/CMakeFiles/sentencepiece_train.dir/sentencepiece_trainer.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sentencepiece_train.dir/sentencepiece_trainer.cc.i"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/log-anomaly/sentencepiece/src/sentencepiece_trainer.cc > CMakeFiles/sentencepiece_train.dir/sentencepiece_trainer.cc.i

src/CMakeFiles/sentencepiece_train.dir/sentencepiece_trainer.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sentencepiece_train.dir/sentencepiece_trainer.cc.s"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/log-anomaly/sentencepiece/src/sentencepiece_trainer.cc -o CMakeFiles/sentencepiece_train.dir/sentencepiece_trainer.cc.s

src/CMakeFiles/sentencepiece_train.dir/pretokenizer_for_training.cc.o: src/CMakeFiles/sentencepiece_train.dir/flags.make
src/CMakeFiles/sentencepiece_train.dir/pretokenizer_for_training.cc.o: /home/<USER>/Documents/log-anomaly/sentencepiece/src/pretokenizer_for_training.cc
src/CMakeFiles/sentencepiece_train.dir/pretokenizer_for_training.cc.o: src/CMakeFiles/sentencepiece_train.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building CXX object src/CMakeFiles/sentencepiece_train.dir/pretokenizer_for_training.cc.o"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/CMakeFiles/sentencepiece_train.dir/pretokenizer_for_training.cc.o -MF CMakeFiles/sentencepiece_train.dir/pretokenizer_for_training.cc.o.d -o CMakeFiles/sentencepiece_train.dir/pretokenizer_for_training.cc.o -c /home/<USER>/Documents/log-anomaly/sentencepiece/src/pretokenizer_for_training.cc

src/CMakeFiles/sentencepiece_train.dir/pretokenizer_for_training.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sentencepiece_train.dir/pretokenizer_for_training.cc.i"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/log-anomaly/sentencepiece/src/pretokenizer_for_training.cc > CMakeFiles/sentencepiece_train.dir/pretokenizer_for_training.cc.i

src/CMakeFiles/sentencepiece_train.dir/pretokenizer_for_training.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sentencepiece_train.dir/pretokenizer_for_training.cc.s"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/log-anomaly/sentencepiece/src/pretokenizer_for_training.cc -o CMakeFiles/sentencepiece_train.dir/pretokenizer_for_training.cc.s

# Object files for target sentencepiece_train
sentencepiece_train_OBJECTS = \
"CMakeFiles/sentencepiece_train.dir/builder.cc.o" \
"CMakeFiles/sentencepiece_train.dir/unicode_script.cc.o" \
"CMakeFiles/sentencepiece_train.dir/trainer_factory.cc.o" \
"CMakeFiles/sentencepiece_train.dir/trainer_interface.cc.o" \
"CMakeFiles/sentencepiece_train.dir/unigram_model_trainer.cc.o" \
"CMakeFiles/sentencepiece_train.dir/word_model_trainer.cc.o" \
"CMakeFiles/sentencepiece_train.dir/char_model_trainer.cc.o" \
"CMakeFiles/sentencepiece_train.dir/bpe_model_trainer.cc.o" \
"CMakeFiles/sentencepiece_train.dir/sentencepiece_trainer.cc.o" \
"CMakeFiles/sentencepiece_train.dir/pretokenizer_for_training.cc.o"

# External object files for target sentencepiece_train
sentencepiece_train_EXTERNAL_OBJECTS =

src/libsentencepiece_train.so.0.0.0: src/CMakeFiles/sentencepiece_train.dir/builder.cc.o
src/libsentencepiece_train.so.0.0.0: src/CMakeFiles/sentencepiece_train.dir/unicode_script.cc.o
src/libsentencepiece_train.so.0.0.0: src/CMakeFiles/sentencepiece_train.dir/trainer_factory.cc.o
src/libsentencepiece_train.so.0.0.0: src/CMakeFiles/sentencepiece_train.dir/trainer_interface.cc.o
src/libsentencepiece_train.so.0.0.0: src/CMakeFiles/sentencepiece_train.dir/unigram_model_trainer.cc.o
src/libsentencepiece_train.so.0.0.0: src/CMakeFiles/sentencepiece_train.dir/word_model_trainer.cc.o
src/libsentencepiece_train.so.0.0.0: src/CMakeFiles/sentencepiece_train.dir/char_model_trainer.cc.o
src/libsentencepiece_train.so.0.0.0: src/CMakeFiles/sentencepiece_train.dir/bpe_model_trainer.cc.o
src/libsentencepiece_train.so.0.0.0: src/CMakeFiles/sentencepiece_train.dir/sentencepiece_trainer.cc.o
src/libsentencepiece_train.so.0.0.0: src/CMakeFiles/sentencepiece_train.dir/pretokenizer_for_training.cc.o
src/libsentencepiece_train.so.0.0.0: src/CMakeFiles/sentencepiece_train.dir/build.make
src/libsentencepiece_train.so.0.0.0: src/CMakeFiles/sentencepiece_train.dir/compiler_depend.ts
src/libsentencepiece_train.so.0.0.0: /usr/lib/libtcmalloc_minimal.so
src/libsentencepiece_train.so.0.0.0: src/libsentencepiece.so.0.0.0
src/libsentencepiece_train.so.0.0.0: /usr/lib/libtcmalloc_minimal.so
src/libsentencepiece_train.so.0.0.0: src/CMakeFiles/sentencepiece_train.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/Documents/log-anomaly/sentencepiece/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Linking CXX shared library libsentencepiece_train.so"
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/sentencepiece_train.dir/link.txt --verbose=$(VERBOSE)
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && $(CMAKE_COMMAND) -E cmake_symlink_library libsentencepiece_train.so.0.0.0 libsentencepiece_train.so.0 libsentencepiece_train.so

src/libsentencepiece_train.so.0: src/libsentencepiece_train.so.0.0.0
	@$(CMAKE_COMMAND) -E touch_nocreate src/libsentencepiece_train.so.0

src/libsentencepiece_train.so: src/libsentencepiece_train.so.0.0.0
	@$(CMAKE_COMMAND) -E touch_nocreate src/libsentencepiece_train.so

# Rule to build all files generated by this target.
src/CMakeFiles/sentencepiece_train.dir/build: src/libsentencepiece_train.so
.PHONY : src/CMakeFiles/sentencepiece_train.dir/build

src/CMakeFiles/sentencepiece_train.dir/clean:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build/src && $(CMAKE_COMMAND) -P CMakeFiles/sentencepiece_train.dir/cmake_clean.cmake
.PHONY : src/CMakeFiles/sentencepiece_train.dir/clean

src/CMakeFiles/sentencepiece_train.dir/depend:
	cd /home/<USER>/Documents/log-anomaly/sentencepiece/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Documents/log-anomaly/sentencepiece /home/<USER>/Documents/log-anomaly/sentencepiece/src /home/<USER>/Documents/log-anomaly/sentencepiece/build /home/<USER>/Documents/log-anomaly/sentencepiece/build/src /home/<USER>/Documents/log-anomaly/sentencepiece/build/src/CMakeFiles/sentencepiece_train.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : src/CMakeFiles/sentencepiece_train.dir/depend

