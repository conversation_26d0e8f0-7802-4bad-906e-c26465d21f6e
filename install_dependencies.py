#!/usr/bin/env python3
"""
Installation script for LLM finetuning dependencies
"""

import subprocess
import sys

def install_package(package):
    """Install a package using pip."""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        return True
    except subprocess.CalledProcessError:
        return False

def main():
    """Install required dependencies."""
    print("Installing dependencies for LLM finetuning...")

    packages = [
        "torch>=2.0.0",
        "transformers>=4.36.0",
        "peft>=0.7.0",
        "datasets>=2.14.0",
        "accelerate>=0.24.0",
        "bitsandbytes>=0.41.0",
        "sentencepiece>=0.1.99",
        "protobuf>=3.20.0",
        "scipy",
        "scikit-learn",
        "numpy",
        "pandas",
        "tqdm"
    ]

    failed_packages = []

    for package in packages:
        print(f"Installing {package}...")
        if install_package(package):
            print(f"✓ {package} installed successfully")
        else:
            print(f"✗ Failed to install {package}")
            failed_packages.append(package)

    if failed_packages:
        print(f"\nFailed to install: {', '.join(failed_packages)}")
        print("You may need to install these manually.")
    else:
        print("\n✓ All dependencies installed successfully!")
        print("You can now run: python llama_finetune_log_anomaly.py")

if __name__ == "__main__":
    main()