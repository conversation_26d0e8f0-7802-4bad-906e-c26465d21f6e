{"best_global_step": 500, "best_metric": 0.3713168203830719, "best_model_checkpoint": "./Qwen3-0.6B/checkpoint-500", "epoch": 2.0, "eval_steps": 500, "global_step": 500, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.04, "grad_norm": 2.247204303741455, "learning_rate": 1.8e-05, "loss": 3.3527, "step": 10}, {"epoch": 0.08, "grad_norm": 1.7856965065002441, "learning_rate": 3.8e-05, "loss": 3.1029, "step": 20}, {"epoch": 0.12, "grad_norm": 1.8742154836654663, "learning_rate": 5.8e-05, "loss": 2.5082, "step": 30}, {"epoch": 0.16, "grad_norm": 1.7891484498977661, "learning_rate": 7.800000000000001e-05, "loss": 1.9846, "step": 40}, {"epoch": 0.2, "grad_norm": 1.7505061626434326, "learning_rate": 9.8e-05, "loss": 1.4765, "step": 50}, {"epoch": 0.24, "grad_norm": 1.713850498199463, "learning_rate": 0.000118, "loss": 1.0791, "step": 60}, {"epoch": 0.28, "grad_norm": 1.3905854225158691, "learning_rate": 0.000138, "loss": 0.8593, "step": 70}, {"epoch": 0.32, "grad_norm": 1.035835862159729, "learning_rate": 0.00015800000000000002, "loss": 0.7449, "step": 80}, {"epoch": 0.36, "grad_norm": 0.7627707123756409, "learning_rate": 0.00017800000000000002, "loss": 0.5709, "step": 90}, {"epoch": 0.4, "grad_norm": 0.8012727499008179, "learning_rate": 0.00019800000000000002, "loss": 0.6863, "step": 100}, {"epoch": 0.44, "grad_norm": 1.0837875604629517, "learning_rate": 0.00019723076923076923, "loss": 0.6034, "step": 110}, {"epoch": 0.48, "grad_norm": 0.8324448466300964, "learning_rate": 0.00019415384615384615, "loss": 0.5482, "step": 120}, {"epoch": 0.52, "grad_norm": 0.9156966209411621, "learning_rate": 0.0001910769230769231, "loss": 0.5355, "step": 130}, {"epoch": 0.56, "grad_norm": 1.2512431144714355, "learning_rate": 0.000188, "loss": 0.5772, "step": 140}, {"epoch": 0.6, "grad_norm": 0.8270675539970398, "learning_rate": 0.00018492307692307694, "loss": 0.5014, "step": 150}, {"epoch": 0.64, "grad_norm": 0.7889235019683838, "learning_rate": 0.00018184615384615385, "loss": 0.5323, "step": 160}, {"epoch": 0.68, "grad_norm": 0.8012493848800659, "learning_rate": 0.00017876923076923077, "loss": 0.4996, "step": 170}, {"epoch": 0.72, "grad_norm": 1.0838042497634888, "learning_rate": 0.00017569230769230772, "loss": 0.5198, "step": 180}, {"epoch": 0.76, "grad_norm": 0.7299129366874695, "learning_rate": 0.0001726153846153846, "loss": 0.4245, "step": 190}, {"epoch": 0.8, "grad_norm": 1.0592180490493774, "learning_rate": 0.00016953846153846156, "loss": 0.4592, "step": 200}, {"epoch": 0.84, "grad_norm": 0.7556465268135071, "learning_rate": 0.00016646153846153848, "loss": 0.3996, "step": 210}, {"epoch": 0.88, "grad_norm": 0.7138118743896484, "learning_rate": 0.0001633846153846154, "loss": 0.4051, "step": 220}, {"epoch": 0.92, "grad_norm": 0.6825435757637024, "learning_rate": 0.00016030769230769232, "loss": 0.4119, "step": 230}, {"epoch": 0.96, "grad_norm": 1.015608787536621, "learning_rate": 0.00015723076923076923, "loss": 0.4507, "step": 240}, {"epoch": 1.0, "grad_norm": 0.9981687664985657, "learning_rate": 0.00015415384615384615, "loss": 0.4645, "step": 250}, {"epoch": 1.04, "grad_norm": 1.0435338020324707, "learning_rate": 0.0001510769230769231, "loss": 0.4099, "step": 260}, {"epoch": 1.08, "grad_norm": 0.975358247756958, "learning_rate": 0.000148, "loss": 0.4291, "step": 270}, {"epoch": 1.12, "grad_norm": 1.062126636505127, "learning_rate": 0.00014492307692307694, "loss": 0.4026, "step": 280}, {"epoch": 1.16, "grad_norm": 0.7985924482345581, "learning_rate": 0.00014184615384615386, "loss": 0.3725, "step": 290}, {"epoch": 1.2, "grad_norm": 0.6634752154350281, "learning_rate": 0.00013876923076923078, "loss": 0.3287, "step": 300}, {"epoch": 1.24, "grad_norm": 0.6974637508392334, "learning_rate": 0.0001356923076923077, "loss": 0.3799, "step": 310}, {"epoch": 1.28, "grad_norm": 0.7113198637962341, "learning_rate": 0.00013261538461538464, "loss": 0.4237, "step": 320}, {"epoch": 1.32, "grad_norm": 0.5723293423652649, "learning_rate": 0.00012953846153846153, "loss": 0.3474, "step": 330}, {"epoch": 1.3599999999999999, "grad_norm": 0.7073192596435547, "learning_rate": 0.00012646153846153848, "loss": 0.4241, "step": 340}, {"epoch": 1.4, "grad_norm": 0.9560196399688721, "learning_rate": 0.0001233846153846154, "loss": 0.3883, "step": 350}, {"epoch": 1.44, "grad_norm": 0.73194420337677, "learning_rate": 0.00012030769230769232, "loss": 0.3624, "step": 360}, {"epoch": 1.48, "grad_norm": 0.4779658913612366, "learning_rate": 0.00011723076923076924, "loss": 0.3612, "step": 370}, {"epoch": 1.52, "grad_norm": 0.42529013752937317, "learning_rate": 0.00011415384615384617, "loss": 0.3508, "step": 380}, {"epoch": 1.56, "grad_norm": 0.4999774396419525, "learning_rate": 0.00011107692307692308, "loss": 0.3866, "step": 390}, {"epoch": 1.6, "grad_norm": 0.5618559718132019, "learning_rate": 0.00010800000000000001, "loss": 0.382, "step": 400}, {"epoch": 1.6400000000000001, "grad_norm": 0.5890786051750183, "learning_rate": 0.00010492307692307693, "loss": 0.3501, "step": 410}, {"epoch": 1.6800000000000002, "grad_norm": 0.7351806163787842, "learning_rate": 0.00010184615384615386, "loss": 0.3604, "step": 420}, {"epoch": 1.72, "grad_norm": 0.5229634046554565, "learning_rate": 9.876923076923077e-05, "loss": 0.3495, "step": 430}, {"epoch": 1.76, "grad_norm": 0.6780646443367004, "learning_rate": 9.569230769230769e-05, "loss": 0.4103, "step": 440}, {"epoch": 1.8, "grad_norm": 0.8239733576774597, "learning_rate": 9.261538461538462e-05, "loss": 0.3779, "step": 450}, {"epoch": 1.8399999999999999, "grad_norm": 0.9248573184013367, "learning_rate": 8.953846153846154e-05, "loss": 0.3812, "step": 460}, {"epoch": 1.88, "grad_norm": 0.5365208387374878, "learning_rate": 8.646153846153846e-05, "loss": 0.3358, "step": 470}, {"epoch": 1.92, "grad_norm": 0.5138478875160217, "learning_rate": 8.338461538461538e-05, "loss": 0.37, "step": 480}, {"epoch": 1.96, "grad_norm": 0.6635212302207947, "learning_rate": 8.030769230769231e-05, "loss": 0.369, "step": 490}, {"epoch": 2.0, "grad_norm": 0.7027521729469299, "learning_rate": 7.723076923076924e-05, "loss": 0.3271, "step": 500}, {"epoch": 2.0, "eval_loss": 0.3713168203830719, "eval_runtime": 12.8554, "eval_samples_per_second": 77.788, "eval_steps_per_second": 19.447, "step": 500}], "logging_steps": 10, "max_steps": 750, "num_input_tokens_seen": 0, "num_train_epochs": 3, "save_steps": 500, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 4150803453444096.0, "train_batch_size": 4, "trial_name": null, "trial_params": null}