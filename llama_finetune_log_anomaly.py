#!/usr/bin/env python3
"""
Llama-3.1 2B Finetuning for Log Anomaly Detection
Uses LoRA (Low-Rank Adaptation) for efficient finetuning with your log anomaly examples
"""

import os
import json
import torch
import random
import numpy as np
from typing import List, Dict, <PERSON>ple
from datetime import datetime
from dataclasses import dataclass, field

try:
    from transformers import (
        AutoTokenizer,
        AutoModelForCausalLM,
        TrainingArguments,
        Trainer,
        DataCollatorForLanguageModeling,
        BitsAndBytesConfig
    )
    from peft import LoraConfig, get_peft_model, TaskType, prepare_model_for_kbit_training
    from datasets import Dataset
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False
    print("Warning: transformers, peft, or datasets not installed. Install with:")
    print("pip install transformers peft datasets accelerate bitsandbytes")

try:
    import wandb
    WANDB_AVAILABLE = True
except ImportError:
    WANDB_AVAILABLE = False

# Import your existing data processor
from thunderbird_random_sampling_agent import ThunderbirdRandomSamplingProcessor


@dataclass
class FineTuningConfig:
    """Configuration for finetuning parameters."""
    # Alternative models (choose one):
    # "microsoft/DialoGPT-medium" - 345M params, no sentencepiece required
    # "microsoft/Phi-3.5-mini-instruct" - 3.8B params, good performance
    # "mistralai/Mistral-7B-Instruct-v0.3" - 7B params, requires sentencepiece
    model_name: str = "Qwen/Qwen3-0.6B"
    output_dir: str = "./Qwen3-0.6B/"
    dataset_path: str = "datasets/Thunderbird/Thunderbird.log"

    # Data parameters
    num_samples: int = 5000
    balance_ratio: float = 0.3
    train_split: float = 0.8
    max_length: int = 1000

    # LoRA parameters
    lora_r: int = 16
    lora_alpha: int = 32
    lora_dropout: float = 0.1
    target_modules: List[str] = field(default_factory=lambda: ["q_proj", "v_proj", "k_proj", "o_proj"])

    # Training parameters
    num_epochs: int = 3
    batch_size: int = 4
    gradient_accumulation_steps: int = 4
    learning_rate: float = 2e-4
    warmup_steps: int = 100
    logging_steps: int = 10
    save_steps: int = 500
    eval_steps: int = 500

    # Quantization
    use_4bit: bool = True
    bnb_4bit_compute_dtype: str = "float16"
    bnb_4bit_quant_type: str = "nf4"
    use_nested_quant: bool = False

    # Other
    use_wandb: bool = False
    wandb_project: str = "llama-log-anomaly"
    seed: int = 42


class LogAnomalyDataProcessor:
    """Processes log data for Llama finetuning."""

    def __init__(self, config: FineTuningConfig):
        self.config = config
        self.processor = ThunderbirdRandomSamplingProcessor(config.dataset_path)

    def create_prompt(self, log_entry: str, classification: str = None) -> str:
        """Create a prompt for log anomaly classification."""
        if classification is None:
            # For inference
            return f"""You are an expert log analyst. Classify the following log entry as either 'normal' or 'anomalous'. Provide a brief explanation for your classification.

Log entry: {log_entry}

Classification:"""
        else:
            # For training - include the full response
            reasoning = f"This log entry is {classification} based on its content and patterns."
            return f"""You are an expert log analyst. Classify the following log entry as either 'normal' or 'anomalous'. Provide a brief explanation for your classification.

Log entry: {log_entry}

Classification: {classification}
Reasoning: {reasoning}"""

    def prepare_training_data(self) -> Tuple[List[Dict], List[Dict]]:
        """Prepare training and validation data."""
        print("Loading and processing log data...")

        # Load stratified random sample
        sample_data = self.processor.load_stratified_random_sample(
            num_samples=self.config.num_samples,
            balance_ratio=self.config.balance_ratio
        )

        # Split into train and validation
        train_size = int(self.config.train_split * len(sample_data))
        train_data = sample_data[:train_size]
        val_data = sample_data[train_size:]

        print(f"Training samples: {len(train_data)}")
        print(f"Validation samples: {len(val_data)}")

        # Convert to training format
        train_examples = []
        val_examples = []

        for item in train_data:
            # Clean log entry (remove alert category)
            log_parts = item['full_entry'].split(' ', 1)
            clean_entry = log_parts[1] if len(log_parts) > 1 else item['message']

            prompt = self.create_prompt(clean_entry, item['label'])
            train_examples.append({"text": prompt})

        for item in val_data:
            log_parts = item['full_entry'].split(' ', 1)
            clean_entry = log_parts[1] if len(log_parts) > 1 else item['message']

            prompt = self.create_prompt(clean_entry, item['label'])
            val_examples.append({"text": prompt})

        return train_examples, val_examples


class LlamaFineTuner:
    """Handles Llama-3.1 2B finetuning with LoRA."""

    def __init__(self, config: FineTuningConfig):
        self.config = config
        self.setup_environment()

    def setup_environment(self):
        """Setup environment and random seeds."""
        random.seed(self.config.seed)
        np.random.seed(self.config.seed)
        torch.manual_seed(self.config.seed)

        if self.config.use_wandb and WANDB_AVAILABLE:
            wandb.init(project=self.config.wandb_project)

    def load_model_and_tokenizer(self):
        """Load the base model and tokenizer with quantization."""
        if not TRANSFORMERS_AVAILABLE:
            raise ImportError("transformers library not available")

        print(f"Loading model: {self.config.model_name}")

        # Quantization config
        if self.config.use_4bit:
            bnb_config = BitsAndBytesConfig(
                load_in_4bit=True,
                bnb_4bit_quant_type=self.config.bnb_4bit_quant_type,
                bnb_4bit_compute_dtype=getattr(torch, self.config.bnb_4bit_compute_dtype),
                bnb_4bit_use_double_quant=self.config.use_nested_quant,
            )
        else:
            bnb_config = None

        # Load tokenizer
        try:
            tokenizer = AutoTokenizer.from_pretrained(
                self.config.model_name,
                trust_remote_code=True,
                use_fast=True
            )
        except Exception as e:
            print(f"Failed to load fast tokenizer: {e}")
            print("Trying slow tokenizer...")
            tokenizer = AutoTokenizer.from_pretrained(
                self.config.model_name,
                trust_remote_code=True,
                use_fast=False
            )

        # Set padding token
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
        tokenizer.padding_side = "right"

        # Load model
        model = AutoModelForCausalLM.from_pretrained(
            self.config.model_name,
            quantization_config=bnb_config,
            device_map="auto",
            trust_remote_code=True,
            torch_dtype=torch.float16
        )

        # Prepare model for k-bit training if using quantization
        if self.config.use_4bit:
            model = prepare_model_for_kbit_training(model)

        return model, tokenizer

    def setup_lora(self, model):
        """Setup LoRA configuration."""
        print("Setting up LoRA...")

        lora_config = LoraConfig(
            r=self.config.lora_r,
            lora_alpha=self.config.lora_alpha,
            target_modules=self.config.target_modules,
            lora_dropout=self.config.lora_dropout,
            bias="none",
            task_type=TaskType.CAUSAL_LM,
        )

        model = get_peft_model(model, lora_config)
        model.print_trainable_parameters()

        return model

    def tokenize_data(self, examples: List[Dict], tokenizer) -> Dataset:
        """Tokenize the training data."""
        def tokenize_function(examples):
            return tokenizer(
                examples["text"],
                truncation=True,
                padding=False,
                max_length=self.config.max_length,
                return_overflowing_tokens=False,
            )

        dataset = Dataset.from_list(examples)
        tokenized_dataset = dataset.map(
            tokenize_function,
            batched=True,
            remove_columns=dataset.column_names,
        )

        return tokenized_dataset

    def train(self):
        """Main training function."""
        print("Starting Llama-3.1 2B finetuning for log anomaly detection...")

        # Prepare data
        data_processor = LogAnomalyDataProcessor(self.config)
        train_examples, val_examples = data_processor.prepare_training_data()

        # Load model and tokenizer
        model, tokenizer = self.load_model_and_tokenizer()

        # Setup LoRA
        model = self.setup_lora(model)

        # Tokenize data
        train_dataset = self.tokenize_data(train_examples, tokenizer)
        val_dataset = self.tokenize_data(val_examples, tokenizer)

        # Data collator
        data_collator = DataCollatorForLanguageModeling(
            tokenizer=tokenizer,
            mlm=False,
        )

        # Training arguments
        training_args = TrainingArguments(
            output_dir=self.config.output_dir,
            num_train_epochs=self.config.num_epochs,
            per_device_train_batch_size=self.config.batch_size,
            per_device_eval_batch_size=self.config.batch_size,
            gradient_accumulation_steps=self.config.gradient_accumulation_steps,
            learning_rate=self.config.learning_rate,
            warmup_steps=self.config.warmup_steps,
            logging_steps=self.config.logging_steps,
            save_steps=self.config.save_steps,
            eval_steps=self.config.eval_steps,
            eval_strategy="steps",  # Updated from evaluation_strategy
            save_strategy="steps",
            load_best_model_at_end=True,
            metric_for_best_model="eval_loss",
            greater_is_better=False,
            fp16=True,
            dataloader_pin_memory=False,
            report_to="wandb" if (self.config.use_wandb and WANDB_AVAILABLE) else None,
        )

        # Initialize trainer
        trainer = Trainer(
            model=model,
            args=training_args,
            train_dataset=train_dataset,
            eval_dataset=val_dataset,
            data_collator=data_collator,
        )

        # Train the model
        print("Starting training...")
        trainer.train()

        # Save the final model
        print(f"Saving model to {self.config.output_dir}")
        trainer.save_model()
        tokenizer.save_pretrained(self.config.output_dir)

        # Save config
        with open(f"{self.config.output_dir}/training_config.json", "w") as f:
            json.dump(self.config.__dict__, f, indent=2)

        print("Training completed!")
        return model, tokenizer


def check_dependencies():
    """Check if required dependencies are installed."""
    missing_deps = []

    if not TRANSFORMERS_AVAILABLE:
        missing_deps.extend(["transformers", "peft", "datasets", "accelerate", "bitsandbytes"])

    # Check for sentencepiece
    try:
        import sentencepiece
    except ImportError:
        missing_deps.append("sentencepiece")

    # Check for protobuf
    try:
        import google.protobuf
    except ImportError:
        missing_deps.append("protobuf")

    if missing_deps:
        print("Missing dependencies:")
        for dep in missing_deps:
            print(f"  - {dep}")
        print("\nInstall with:")
        print("pip install -r requirements_llama.txt")
        print("\nOr install manually:")
        print("pip install transformers peft datasets accelerate bitsandbytes sentencepiece protobuf")
        print("\nFor CUDA support (recommended):")
        print("pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118")
        return False

    return True


def main():
    """Main function to run the finetuning."""
    print("Llama-3.1 2B Log Anomaly Detection Finetuning")
    print("=" * 50)


    config = FineTuningConfig()

    # Create output directory
    os.makedirs(config.output_dir, exist_ok=True)

    # Initialize and run training
    finetuner = LlamaFineTuner(config)
    model, tokenizer = finetuner.train()

    print(f"Model saved to: {config.output_dir}")
    print("You can now use the finetuned model for log anomaly detection!")


if __name__ == "__main__":
    main()