#!/usr/bin/env python3
"""
Inference script for the finetuned Llama-3.1 2B log anomaly detection model
"""

import os
import torch
import json
from typing import List, Dict
from dataclasses import dataclass

try:
    from transformers import AutoTokenizer, AutoModelForCausalLM
    from peft import PeftModel
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False
    print("Warning: transformers or peft not installed. Install with:")
    print("pip install transformers peft")

# Import your existing data processor
from thunderbird_random_sampling_agent import ThunderbirdRandomSamplingProcessor


@dataclass
class InferenceConfig:
    """Configuration for inference."""
    model_path: str = "./Qwen3-0.6B"
    base_model_name: str = "Qwen/Qwen3-0.6B"
    dataset_path: str = "datasets/Thunderbird/Thunderbird_10M.log"
    max_length: int = 512
    temperature: float = 0.1
    max_new_tokens: int = 100
    num_test_samples: int = 50


class LlamaLogAnomalyInference:
    """Handles inference with the finetuned Llama model."""

    def __init__(self, config: InferenceConfig):
        self.config = config
        self.model = None
        self.tokenizer = None

    def load_model(self):
        """Load the finetuned model and tokenizer."""
        if not TRANSFORMERS_AVAILABLE:
            raise ImportError("transformers library not available")

        print(f"Loading finetuned model from: {self.config.model_path}")

        # Load tokenizer
        self.tokenizer = AutoTokenizer.from_pretrained(
            self.config.model_path,
            trust_remote_code=True
        )

        # Load base model
        base_model = AutoModelForCausalLM.from_pretrained(
            self.config.base_model_name,
            torch_dtype=torch.float16,
            device_map="auto",
            trust_remote_code=True
        )

        # Load LoRA weights
        self.model = PeftModel.from_pretrained(
            base_model,
            self.config.model_path,
            torch_dtype=torch.float16
        )

        self.model.eval()
        print("Model loaded successfully!")

    def create_prompt(self, log_entry: str) -> str:
        """Create a prompt for log anomaly classification."""
        return f"""You are an expert log analyst. Classify the following log entry as either 'normal' or 'anomalous'. Provide a brief explanation for your classification.

Log entry: {log_entry}

Classification:"""

    def predict(self, log_entry: str) -> Dict[str, str]:
        """Predict anomaly classification for a log entry."""
        if self.model is None or self.tokenizer is None:
            raise ValueError("Model not loaded. Call load_model() first.")

        prompt = self.create_prompt(log_entry)

        # Tokenize input
        inputs = self.tokenizer(
            prompt,
            return_tensors="pt",
            truncation=True,
            max_length=self.config.max_length,
            padding=True
        )

        # Move to device
        inputs = {k: v.to(self.model.device) for k, v in inputs.items()}

        # Generate response
        with torch.no_grad():
            outputs = self.model.generate(
                **inputs,
                max_new_tokens=self.config.max_new_tokens,
                temperature=self.config.temperature,
                do_sample=True,
                pad_token_id=self.tokenizer.eos_token_id,
                eos_token_id=self.tokenizer.eos_token_id,
            )

        # Decode response
        response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)

        # Extract the generated part (after the prompt)
        generated_text = response[len(prompt):].strip()

        # Parse classification and reasoning
        lines = generated_text.split('\n')
        classification = "unknown"
        reasoning = "No reasoning provided"

        for line in lines:
            line = line.strip()
            if line.lower().startswith('classification:'):
                classification = line.split(':', 1)[1].strip().lower()
            elif line.lower().startswith('reasoning:'):
                reasoning = line.split(':', 1)[1].strip()

        # Clean up classification
        if 'normal' in classification:
            classification = 'normal'
        elif 'anomalous' in classification or 'anomaly' in classification:
            classification = 'anomalous'

        return {
            'classification': classification,
            'reasoning': reasoning,
            'full_response': generated_text
        }

    def evaluate_on_test_data(self):
        """Evaluate the model on test data."""
        print("Loading test data...")

        # Load test data
        processor = ThunderbirdRandomSamplingProcessor(self.config.dataset_path)
        test_data = processor.load_stratified_random_sample(
            num_samples=self.config.num_test_samples,
            balance_ratio=0.3
        )

        print(f"Evaluating on {len(test_data)} test samples...")

        correct = 0
        total = 0
        true_positives = 0
        false_positives = 0
        false_negatives = 0
        true_negatives = 0

        for i, item in enumerate(test_data):
            # Clean log entry (remove alert category)
            log_parts = item['full_entry'].split(' ', 1)
            clean_entry = log_parts[1] if len(log_parts) > 1 else item['message']

            # Get prediction
            try:
                prediction = self.predict(clean_entry)
                predicted_class = prediction['classification']
                actual_class = item['label']

                if predicted_class == actual_class:
                    correct += 1

                # Calculate confusion matrix components
                if actual_class == 'anomalous' and predicted_class == 'anomalous':
                    true_positives += 1
                elif actual_class == 'normal' and predicted_class == 'anomalous':
                    false_positives += 1
                elif actual_class == 'anomalous' and predicted_class == 'normal':
                    false_negatives += 1
                elif actual_class == 'normal' and predicted_class == 'normal':
                    true_negatives += 1

                total += 1

                # Show first few predictions
                if i < 5:
                    print(f"\nExample {i+1}:")
                    print(f"Log: {clean_entry[:100]}...")
                    print(f"Actual: {actual_class}")
                    print(f"Predicted: {predicted_class}")
                    print(f"Reasoning: {prediction['reasoning']}")
                    print(f"Correct: {predicted_class == actual_class}")

            except Exception as e:
                print(f"Error processing example {i}: {e}")
                continue

        # Calculate metrics
        accuracy = correct / total if total > 0 else 0
        precision = true_positives / (true_positives + false_positives) if (true_positives + false_positives) > 0 else 0
        recall = true_positives / (true_positives + false_negatives) if (true_positives + false_negatives) > 0 else 0
        f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0

        print(f"\nEvaluation Results:")
        print(f"Accuracy: {accuracy:.3f} ({correct}/{total})")
        print(f"Precision: {precision:.3f}")
        print(f"Recall: {recall:.3f}")
        print(f"F1-Score: {f1_score:.3f}")
        print(f"\nConfusion Matrix:")
        print(f"True Positives (TP): {true_positives}")
        print(f"False Positives (FP): {false_positives}")
        print(f"False Negatives (FN): {false_negatives}")
        print(f"True Negatives (TN): {true_negatives}")


def main():
    """Main function for inference."""
    print("Llama-3.1 2B Log Anomaly Detection Inference")
    print("=" * 50)

    config = InferenceConfig()

    # Check if model exists
    if not os.path.exists(config.model_path):
        print(f"Error: Model not found at {config.model_path}")
        print("Please run the finetuning script first: python llama_finetune_log_anomaly.py")
        return

    # Initialize inference
    inference = LlamaLogAnomalyInference(config)
    inference.load_model()

    # Evaluate on test data
    inference.evaluate_on_test_data()

    # Interactive mode
    print("\nInteractive mode - Enter log entries to classify (or 'quit' to exit):")
    while True:
        log_entry = input("\nLog entry: ").strip()
        if log_entry.lower() in ['quit', 'exit', 'q']:
            break

        if log_entry:
            try:
                result = inference.predict(log_entry)
                print(f"Classification: {result['classification']}")
                print(f"Reasoning: {result['reasoning']}")
            except Exception as e:
                print(f"Error: {e}")


if __name__ == "__main__":
    main()