# Llama-3.1 2B Finetuning for Log Anomaly Detection

This project provides a complete pipeline for finetuning Meta's Llama-3.1 2B model on your log anomaly detection dataset using LoRA (Low-Rank Adaptation) for efficient training.

## Features

- **Efficient Finetuning**: Uses LoRA to reduce memory requirements and training time
- **4-bit Quantization**: Supports BitsAndBytesConfig for memory-efficient training
- **Data Integration**: Leverages your existing Thunderbird dataset processing pipeline
- **Comprehensive Evaluation**: Includes accuracy, precision, recall, and F1-score metrics
- **Interactive Inference**: Test the model interactively after training

## Requirements

### Hardware Requirements
- **GPU**: NVIDIA GPU with at least 8GB VRAM (recommended: 16GB+)
- **RAM**: At least 16GB system RAM
- **Storage**: 10GB+ free space for model and data

### Software Requirements
Install the required dependencies:

```bash
pip install -r requirements_llama.txt
```

Or install manually:
```bash
pip install torch>=2.0.0 transformers>=4.36.0 peft>=0.7.0 datasets>=2.14.0 accelerate>=0.24.0 bitsandbytes>=0.41.0
```

For CUDA support (recommended):
```bash
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
```

## Quick Start

### 1. Prepare Your Data
Ensure your Thunderbird dataset is available at `datasets/Thunderbird/Thunderbird.log`. The script will automatically:
- Load stratified random samples
- Balance normal vs anomalous examples
- Split into training and validation sets

### 2. Configure Training Parameters
Edit the `FineTuningConfig` class in `llama_finetune_log_anomaly.py`:

```python
@dataclass
class FineTuningConfig:
    model_name: str = "meta-llama/Llama-3.1-2B-Instruct"
    output_dir: str = "./llama-3.1-2b-log-anomaly"

    # Data parameters
    num_samples: int = 5000        # Number of training samples
    balance_ratio: float = 0.3     # Ratio of anomalous samples

    # Training parameters
    num_epochs: int = 3
    batch_size: int = 4
    learning_rate: float = 2e-4

    # LoRA parameters
    lora_r: int = 16
    lora_alpha: int = 32
```

### 3. Run Finetuning
```bash
python llama_finetune_log_anomaly.py
```

The script will:
1. Load and process your log data
2. Download the base Llama-3.1 2B model
3. Apply LoRA configuration
4. Train the model with your examples
5. Save the finetuned model to `./llama-3.1-2b-log-anomaly/`

### 4. Run Inference and Evaluation
```bash
python llama_inference_log_anomaly.py
```

This will:
1. Load your finetuned model
2. Evaluate on test data
3. Provide interactive classification interface

## Configuration Options

### Training Configuration

| Parameter | Description | Default | Recommended Range |
|-----------|-------------|---------|-------------------|
| `num_samples` | Training samples | 5000 | 1000-10000 |
| `balance_ratio` | Anomaly ratio | 0.3 | 0.2-0.4 |
| `num_epochs` | Training epochs | 3 | 2-5 |
| `batch_size` | Batch size | 4 | 2-8 |
| `learning_rate` | Learning rate | 2e-4 | 1e-5 to 5e-4 |

### LoRA Configuration

| Parameter | Description | Default | Notes |
|-----------|-------------|---------|-------|
| `lora_r` | LoRA rank | 16 | Higher = more parameters |
| `lora_alpha` | LoRA alpha | 32 | Usually 2x lora_r |
| `lora_dropout` | Dropout rate | 0.1 | Prevents overfitting |

## Memory Optimization

### For Limited GPU Memory (8GB):
```python
# Reduce batch size and enable 4-bit quantization
batch_size: int = 2
gradient_accumulation_steps: int = 8
use_4bit: bool = True
```

### For More GPU Memory (16GB+):
```python
# Increase batch size for faster training
batch_size: int = 8
gradient_accumulation_steps: int = 2
use_4bit: bool = False  # Optional
```

## Expected Results

With the default configuration on Thunderbird dataset:
- **Training Time**: 2-4 hours on RTX 3080
- **Memory Usage**: ~6-8GB VRAM with 4-bit quantization
- **Expected Accuracy**: 85-95% (depends on data quality)

## Troubleshooting

### Common Issues

1. **CUDA Out of Memory**:
   - Reduce `batch_size` to 2 or 1
   - Enable 4-bit quantization: `use_4bit: bool = True`
   - Increase `gradient_accumulation_steps`

2. **Model Download Issues**:
   - Ensure stable internet connection
   - May require Hugging Face authentication for Llama models
   - Run: `huggingface-cli login`

3. **Import Errors**:
   - Install missing dependencies: `pip install -r requirements_llama.txt`
   - Ensure CUDA-compatible PyTorch installation

### Performance Tips

1. **Faster Training**:
   - Use larger batch sizes if memory allows
   - Enable mixed precision training (fp16)
   - Use multiple GPUs with `device_map="auto"`

2. **Better Results**:
   - Increase training samples (`num_samples`)
   - Tune learning rate and LoRA parameters
   - Add more training epochs for complex datasets

## File Structure

```
├── llama_finetune_log_anomaly.py     # Main finetuning script
├── llama_inference_log_anomaly.py    # Inference and evaluation
├── requirements_llama.txt            # Dependencies
├── README_llama_finetuning.md       # This file
└── llama-3.1-2b-log-anomaly/        # Output directory (created after training)
    ├── adapter_config.json
    ├── adapter_model.safetensors
    ├── training_config.json
    └── tokenizer files...
```

## Integration with Existing Workflow

This finetuning approach complements your existing DSPy-based agents:

1. **DSPy Agents**: Great for rapid prototyping and few-shot learning
2. **Finetuned Llama**: Better for production deployment with consistent performance

You can compare results between:
- `thunderbird_random_sampling_agent.py` (DSPy + LabeledFewShot)
- `llama_finetune_log_anomaly.py` (Finetuned Llama-3.1 2B)

## Next Steps

1. **Experiment with hyperparameters** for your specific dataset
2. **Compare with your DSPy results** to see which approach works better
3. **Deploy the finetuned model** in your production environment
4. **Consider larger models** (7B, 13B) if you have more compute resources

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Review the configuration options
3. Compare with your existing DSPy implementations