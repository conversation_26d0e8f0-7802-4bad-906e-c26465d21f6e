{"time":"2025-08-07T18:27:16.741877275+03:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-07T18:27:16.847848425+03:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-07T18:27:16.848206628+03:00","level":"INFO","msg":"stream: created new stream","id":"qulcq8uk"}
{"time":"2025-08-07T18:27:16.848219983+03:00","level":"INFO","msg":"stream: started","id":"qulcq8uk"}
{"time":"2025-08-07T18:27:16.848258896+03:00","level":"INFO","msg":"handler: started","stream_id":"qulcq8uk"}
{"time":"2025-08-07T18:27:16.848242705+03:00","level":"INFO","msg":"writer: Do: started","stream_id":"qulcq8uk"}
{"time":"2025-08-07T18:27:16.848275257+03:00","level":"INFO","msg":"sender: started","stream_id":"qulcq8uk"}
{"time":"2025-08-07T18:27:16.848657455+03:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-07T18:40:41.700974186+03:00","level":"INFO","msg":"stream: closing","id":"qulcq8uk"}
{"time":"2025-08-07T18:40:41.701042655+03:00","level":"INFO","msg":"handler: closed","stream_id":"qulcq8uk"}
{"time":"2025-08-07T18:40:41.701045751+03:00","level":"INFO","msg":"writer: Close: closed","stream_id":"qulcq8uk"}
{"time":"2025-08-07T18:40:41.701047554+03:00","level":"INFO","msg":"sender: closed","stream_id":"qulcq8uk"}
{"time":"2025-08-07T18:40:41.701077571+03:00","level":"INFO","msg":"stream: closed","id":"qulcq8uk"}
