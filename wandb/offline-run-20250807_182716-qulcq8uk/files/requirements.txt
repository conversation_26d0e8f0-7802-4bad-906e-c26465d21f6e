pure_eval==0.2.3
pydantic==2.11.7
multidict==6.6.3
requests==2.32.4
packaging==25.0
nvidia-cuda-cupti-cu12==12.6.80
magicattr==0.1.6
sympy==1.14.0
rpds-py==0.26.0
nvidia-curand-cu12==*********
scipy==1.16.1
psutil==7.0.0
nvidia-cufile-cu12==********
peft==0.17.0
pexpect==4.9.0
importlib_metadata==8.7.0
uv==0.8.4
frozenlist==1.7.0
smmap==5.0.2
huggingface-hub==0.34.3
markdown-it-py==3.0.0
six==1.17.0
jsonschema-specifications==2025.4.1
jedi==0.19.2
gitdb==4.0.12
safetensors==0.5.3
pip==25.2
cloudpickle==3.1.1
h11==0.16.0
Pygments==2.19.2
platformdirs==4.3.8
nvidia-cuda-runtime-cu12==12.6.77
aiosignal==1.4.0
Mako==1.3.10
tqdm==4.67.1
Jinja2==3.1.6
cycler==0.12.1
backoff==2.2.1
triton==3.3.1
idna==3.10
nvidia-cublas-cu12==********
tenacity==9.1.2
nvidia-cusparselt-cu12==0.6.3
nvidia-nvtx-cu12==12.6.77
setuptools==80.9.0
python-dateutil==2.9.0.post0
jiter==0.10.0
propcache==0.3.2
torch==2.7.1
greenlet==3.2.3
pillow==11.3.0
contourpy==1.3.3
scikit-learn==1.7.1
filelock==3.18.0
nvidia-cudnn-cu12==********
pyparsing==3.2.3
sniffio==1.3.1
aiohappyeyeballs==2.6.1
networkx==3.5
pyarrow==21.0.0
pydantic_core==2.33.2
pandas==2.3.1
protobuf==6.31.1
SQLAlchemy==2.0.42
asttokens==3.0.0
parso==0.8.4
sentry-sdk==2.34.1
fsspec==2025.3.0
wandb==0.21.0
seaborn==0.13.2
aiohttp==3.12.15
numpy==2.3.2
attrs==25.3.0
nvidia-cusolver-cu12==********
prompt_toolkit==3.0.51
openai==1.98.0
ipython==9.4.0
MarkupSafe==3.0.2
asyncer==0.0.8
nvidia-cufft-cu12==********
sentence-transformers==5.0.0
matplotlib-inline==0.1.7
ujson==5.10.0
zipp==3.23.0
accelerate==1.10.0
multiprocess==0.70.16
ipython_pygments_lexers==1.1.1
hf-xet==1.1.5
dill==0.3.8
httpcore==1.0.9
GitPython==3.1.45
alembic==1.16.4
httpx==0.28.1
certifi==2025.8.3
optuna==4.4.0
diskcache==5.6.3
nvidia-nccl-cu12==2.26.2
rich==14.1.0
threadpoolctl==3.6.0
json_repair==0.48.0
bitsandbytes==0.46.1
click==8.2.1
typing_extensions==4.14.1
nvidia-cuda-nvrtc-cu12==12.6.77
matplotlib==3.10.5
xxhash==3.5.0
mpmath==1.3.0
referencing==0.36.2
kiwisolver==1.4.8
stack-data==0.6.3
nvidia-nvjitlink-cu12==12.6.85
PyYAML==6.0.2
fonttools==4.59.0
executing==2.2.0
wcwidth==0.2.13
litellm==1.74.15.post1
mdurl==0.1.2
tzdata==2025.2
regex==2025.7.34
yarl==1.20.1
urllib3==2.5.0
ptyprocess==0.7.0
charset-normalizer==3.4.2
distro==1.9.0
jsonschema==4.25.0
python-dotenv==1.1.1
transformers==4.55.0
traitlets==5.14.3
joblib==1.5.1
tiktoken==0.9.0
nvidia-cusparse-cu12==********
tokenizers==0.21.4
pytz==2025.2
datasets==4.0.0
dspy==2.6.27
decorator==5.2.1
annotated-types==0.7.0
typing-inspection==0.4.1
colorlog==6.9.0
cachetools==6.1.0
anyio==4.10.0
