#!/usr/bin/env python3
"""
DSPy-based Log Anomaly Detection Agent for Thunderbird Dataset with Random Line Sampling
Uses BootstrapFinetune optimization with local Llama-3.2-1B student and GPT-4o-mini teacher
Follows the DSPy classification finetuning tutorial pattern

Requirements:
1. Install SGLang for local model serving: pip install "sglang[all]>=0.4.4.post3" --find-links https://flashinfer.ai/whl/cu124/torch2.5/flashinfer-python
2. Install finetuning dependencies: pip install torch transformers==4.48.3 accelerate trl peft
3. Set OPENAI_API_KEY environment variable for teacher model
4. Optional: Set CUDA_VISIBLE_DEVICES and DSPY_FINETUNEDIR environment variables
"""

import dspy
from typing import List, Dict, Set
import random
import os


# Define the classification classes for log anomaly detection
LOG_CLASSES = ['normal', 'anomalous']

# Create the DSPy classifier following the tutorial pattern
classify = dspy.ChainOfThought(f"log_entry -> classification: Literal{LOG_CLASSES}, reasoning")


class ThunderbirdRandomSamplingProcessor:
    """Processes the Thunderbird dataset with random line sampling."""
    
    def __init__(self, dataset_path: str):
        self.dataset_path = dataset_path
        self.total_lines = 0
        self.data = []
    
    def count_total_lines(self) -> int:
        """Count total lines in the dataset file."""
        print(f"Counting total lines in {self.dataset_path}...")
        
        line_count = 0
        with open(self.dataset_path, 'r', encoding='utf-8', errors='ignore') as f:
            for line_num, line in enumerate(f, 1):
                if line_num % 100000 == 0:
                    print(f"Counted {line_num} lines...")
                line_count = line_num
        
        self.total_lines = line_count
        print(f"Total lines in dataset: {self.total_lines:,}")
        return self.total_lines
    
    def parse_log_line(self, line: str) -> Dict:
        """Parse a single log line from Thunderbird format."""
        parts = line.strip().split(' ', 4)
        if len(parts) < 5:
            return None
        
        alert_category = parts[0]
        timestamp = parts[1]
        date = parts[2]
        node = parts[3]
        message = parts[4]
        
        # Label: "-" means normal (non-alert), anything else is anomalous (alert)
        is_anomalous = alert_category != "-"
        
        return {
            'alert_category': alert_category,
            'timestamp': timestamp,
            'date': date,
            'node': node,
            'message': message,
            'is_anomalous': is_anomalous,
            'label': 'anomalous' if is_anomalous else 'normal',
            'full_entry': line.strip()
        }
    
    def generate_random_line_numbers(self, num_samples: int) -> Set[int]:
        """Generate random line numbers to sample from the dataset."""
        if self.total_lines == 0:
            self.count_total_lines()
        
        if num_samples > self.total_lines:
            print(f"Warning: Requested {num_samples} samples but only {self.total_lines} lines available")
            num_samples = self.total_lines
        
        print(f"Generating {num_samples} random line numbers from {self.total_lines} total lines...")
        random_lines = set(random.sample(range(1, self.total_lines + 1), num_samples))
        
        return random_lines
    
    def load_stratified_random_sample(self, num_samples: int = 100, balance_ratio: float = 0.3) -> List[Dict]:
        """Load stratified random samples to maintain class balance."""
        print(f"Loading {num_samples} stratified random samples (target {balance_ratio:.1%} anomalous)...")
        
        target_anomalous = int(num_samples * balance_ratio)
        target_normal = num_samples - target_anomalous
        
        # First pass: collect all line numbers by class
        normal_lines = []
        anomalous_lines = []
        
        print("First pass: identifying line numbers by class...")
        with open(self.dataset_path, 'r', encoding='utf-8', errors='ignore') as f:
            for line_num, line in enumerate(f, 1):
                if line_num % 100000 == 0:
                    print(f"Scanned {line_num} lines...")
                
                parsed = self.parse_log_line(line)
                if parsed is not None:
                    if parsed['is_anomalous']:
                        anomalous_lines.append(line_num)
                    else:
                        normal_lines.append(line_num)
        
        print(f"Found {len(normal_lines)} normal lines and {len(anomalous_lines)} anomalous lines")
        
        # Sample random line numbers from each class
        selected_normal_lines = set(random.sample(normal_lines, min(target_normal, len(normal_lines))))
        selected_anomalous_lines = set(random.sample(anomalous_lines, min(target_anomalous, len(anomalous_lines))))
        
        all_selected_lines = selected_normal_lines | selected_anomalous_lines
        
        print(f"Selected {len(selected_normal_lines)} normal and {len(selected_anomalous_lines)} anomalous line numbers")
        
        # Second pass: collect the actual data
        samples = []
        print("Second pass: collecting selected lines...")
        
        with open(self.dataset_path, 'r', encoding='utf-8', errors='ignore') as f:
            for line_num, line in enumerate(f, 1):
                if line_num % 100000 == 0:
                    print(f"Processed {line_num} lines, collected {len(samples)} samples...")
                
                if line_num in all_selected_lines:
                    parsed = self.parse_log_line(line)
                    if parsed is not None:
                        samples.append(parsed)
                
                # Early exit if we've collected all samples
                if len(samples) >= len(all_selected_lines):
                    break
        
        # Shuffle the collected samples
        random.shuffle(samples)
        
        # Print final statistics
        normal_count = sum(1 for item in samples if not item['is_anomalous'])
        anomalous_count = len(samples) - normal_count
        
        print(f"Final stratified sample: {len(samples)} total")
        print(f"  Normal: {normal_count} ({normal_count/len(samples)*100:.1f}%)")
        print(f"  Anomalous: {anomalous_count} ({anomalous_count/len(samples)*100:.1f}%)")
        
        return samples
    
    def create_dspy_examples(self, data: List[Dict], for_training: bool = True) -> List[dspy.Example]:
        """Convert parsed data to DSPy examples following tutorial pattern."""
        examples = []
        for item in data:
            # Create a clean log entry without the alert category for classification
            log_parts = item['full_entry'].split(' ', 1)
            if len(log_parts) > 1:
                clean_entry = log_parts[1]  # Remove the alert category
            else:
                clean_entry = item['message']

            if for_training:
                # For training: include the correct classification and reasoning
                example = dspy.Example(
                    log_entry=clean_entry,
                    classification=item['label'],
                    reasoning=f"This log entry is {item['label']} based on its content and patterns."
                ).with_inputs('log_entry')
            else:
                # For testing: only include the log entry, no ground truth labels
                example = dspy.Example(
                    log_entry=clean_entry
                ).with_inputs('log_entry')
                # Store the ground truth separately for evaluation
                example.ground_truth_classification = item['label']

            examples.append(example)

        return examples


def setup_models_for_finetuning():
    """Setup student and teacher models following the DSPy tutorial pattern."""
    print("Setting up models for BootstrapFinetune...")

    # Setup student model: Llama-3.2-1B-Instruct (local)
    from dspy.clients.lm_local import LocalProvider

    student_lm_name = "meta-llama/Llama-3.2-1B-Instruct"
    print(f"Setting up student model: {student_lm_name}")
    student_lm = dspy.LM(model=f"openai/local:{student_lm_name}", provider=LocalProvider(), max_tokens=2000)

    # Setup teacher model: GPT-4o-mini (OpenAI)
    if not os.getenv("OPENAI_API_KEY"):
        raise Exception("OPENAI_API_KEY environment variable is required for teacher model")

    print("Setting up teacher model: GPT-4o-mini")
    teacher_lm = dspy.LM('openai/gpt-4o-mini', max_tokens=3000)

    # Create student and teacher classifiers
    student_classify = classify.deepcopy()
    student_classify.set_lm(student_lm)

    teacher_classify = classify.deepcopy()
    teacher_classify.set_lm(teacher_lm)

    print("Models configured successfully!")
    return student_classify, teacher_classify


def evaluate_accuracy(example, prediction, trace=None):
    """Evaluation metric for BootstrapFinetune following tutorial pattern."""
    try:
        predicted_class = prediction.classification.lower().strip()

        # Handle both training and test examples
        if hasattr(example, 'ground_truth_classification'):
            actual_class = example.ground_truth_classification.lower().strip()
        elif hasattr(example, 'classification'):
            actual_class = example.classification.lower().strip()
        else:
            return False

        return predicted_class == actual_class
    except Exception as e:
        print(f"Error in evaluation: {e}")
        return False


def train_and_evaluate_bootstrap_finetune():
    """Main training and evaluation function with BootstrapFinetune following tutorial pattern."""
    print("Starting Thunderbird Log Anomaly Detection with BootstrapFinetune...")

    # Enable experimental features for fine-tuning
    dspy.settings.experimental = True

    # Setup student and teacher models
    student_classify, teacher_classify = setup_models_for_finetuning()

    # Load and process data with random sampling
    processor = ThunderbirdRandomSamplingProcessor("datasets/Thunderbird/Thunderbird.log")

    # Count total lines first
    total_lines = processor.count_total_lines()

    # Load stratified random sample (smaller size for BootstrapFinetune example)
    sample_data = processor.load_stratified_random_sample(num_samples=500, balance_ratio=0.3)

    # Split data into train, dev, and test
    train_size = int(0.6 * len(sample_data))
    dev_size = int(0.2 * len(sample_data))
    train_data = sample_data[:train_size]
    dev_data = sample_data[train_size:train_size + dev_size]
    test_data = sample_data[train_size + dev_size:]

    # Create examples with appropriate visibility of labels
    train_examples = processor.create_dspy_examples(train_data, for_training=True)
    dev_examples = processor.create_dspy_examples(dev_data, for_training=False)
    test_examples = processor.create_dspy_examples(test_data, for_training=False)

    # Add ground truth to dev and test examples for evaluation
    for i, dev_example in enumerate(dev_examples):
        dev_example.ground_truth_classification = dev_data[i]['label']
    for i, test_example in enumerate(test_examples):
        test_example.ground_truth_classification = test_data[i]['label']

    print(f"Dataset split: {len(train_examples)} train, {len(dev_examples)} dev, {len(test_examples)} test")

    # First try BootstrapFinetune without labels (unlabeled training)
    print("Setting up BootstrapFinetune optimizer (unlabeled training)...")
    try:
        # Create unlabeled training set (remove labels from training examples)
        unlabeled_trainset = [dspy.Example(log_entry=ex.log_entry).with_inputs("log_entry") for ex in train_examples]

        optimizer = dspy.BootstrapFinetune(num_threads=16)
        classify_ft = optimizer.compile(student_classify, teacher=teacher_classify, trainset=unlabeled_trainset)

        # Launch the finetuned model
        classify_ft.get_lm().launch()
        print("BootstrapFinetune (unlabeled) completed successfully!")

    except Exception as e:
        print(f"BootstrapFinetune (unlabeled) failed: {e}")
        print("Trying BootstrapFinetune with labels...")

        try:
            # Try with labels and metric
            optimizer = dspy.BootstrapFinetune(num_threads=16, metric=evaluate_accuracy)
            classify_ft = optimizer.compile(student_classify, teacher=teacher_classify, trainset=train_examples)

            # Launch the finetuned model
            classify_ft.get_lm().launch()
            print("BootstrapFinetune (with labels) completed successfully!")

        except Exception as e:
            print(f"BootstrapFinetune (with labels) failed: {e}")
            print("Falling back to BootstrapFewShot...")

            optimizer = dspy.BootstrapFewShot(
                metric=evaluate_accuracy,
                max_bootstrapped_demos=8,
                max_labeled_demos=4
            )
            classify_ft = optimizer.compile(
                student_classify,
                trainset=train_examples,
                valset=dev_examples
            )

    # Evaluate on test set
    print("Evaluating on test set...")
    correct = 0
    total = 0

    # For F1-score and recall calculation
    true_positives = 0  # Correctly predicted anomalous
    false_positives = 0  # Incorrectly predicted anomalous
    false_negatives = 0  # Incorrectly predicted normal (missed anomalies)
    true_negatives = 0  # Correctly predicted normal

    for example in test_examples:
        try:
            prediction = classify_ft(log_entry=example.log_entry)
            predicted_class = prediction.classification.lower().strip()
            actual_class = example.ground_truth_classification.lower().strip()

            if predicted_class == actual_class:
                correct += 1

            # Calculate confusion matrix components
            if actual_class == 'anomalous' and predicted_class == 'anomalous':
                true_positives += 1
            elif actual_class == 'normal' and predicted_class == 'anomalous':
                false_positives += 1
            elif actual_class == 'anomalous' and predicted_class == 'normal':
                false_negatives += 1
            elif actual_class == 'normal' and predicted_class == 'normal':
                true_negatives += 1

            total += 1
            if total <= 5:  # Show first 5 predictions
                print(f"\nExample {total}:")
                print(f"Log: {example.log_entry[:100]}...")
                print(f"Actual: {actual_class}")
                print(f"Predicted: {predicted_class}")
                print(f"Reasoning: {prediction.reasoning}")
                print(f"Correct: {predicted_class == actual_class}")

        except Exception as e:
            print(f"Error processing example: {e}")
            continue

    # Calculate metrics
    accuracy = correct / total if total > 0 else 0
    precision = true_positives / (true_positives + false_positives) if (true_positives + false_positives) > 0 else 0
    recall = true_positives / (true_positives + false_negatives) if (true_positives + false_negatives) > 0 else 0
    f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0

    print(f"\nFinal Results (BootstrapFinetune):")
    print(f"Total lines in dataset: {total_lines:,}")
    print(f"Samples used: {len(sample_data):,} ({len(sample_data)/total_lines*100:.3f}% of total)")
    print(f"Student model: meta-llama/Llama-3.2-1B-Instruct")
    print(f"Teacher model: GPT-4o-mini")
    print(f"Accuracy: {accuracy:.3f} ({correct}/{total})")
    print(f"Precision: {precision:.3f}")
    print(f"Recall: {recall:.3f}")
    print(f"F1-Score: {f1_score:.3f}")
    print(f"\nConfusion Matrix:")
    print(f"True Positives (TP): {true_positives}")
    print(f"False Positives (FP): {false_positives}")
    print(f"False Negatives (FN): {false_negatives}")
    print(f"True Negatives (TN): {true_negatives}")

    return classify_ft


if __name__ == "__main__":
    # Optional environment variable setup (following tutorial pattern)
    # Uncomment and modify as needed:
    # import os
    # os.environ["CUDA_VISIBLE_DEVICES"] = "0"  # Control GPU for fine-tuning
    # os.environ["DSPY_FINETUNEDIR"] = "/path/to/dir"  # Control checkpoint directory

    # Set random seed for reproducibility
    random.seed(42)

    # Train and evaluate the model
    trained_model = train_and_evaluate_bootstrap_finetune()

    print("\nThunderbird BootstrapFinetune training completed!")
    print("Note: The finetuned model is running locally. Call trained_model.get_lm().kill() to free GPU memory.")
